from pathlib import Path
from unittest.mock import ANY, call, patch

import pendulum
import pytest
from airflow.models import TaskInstance

from gdrive.operators.gdrive_to_gcs_operator import GoogleDriveToGcsOperator

DEFAULT_DATE = pendulum.datetime(2019, 6, 1)
operator_kwargs = {
    "task_id": "upload_gdrive_data",
    "gdrive_file": {"id": "k2342l3k4j23432lkj", "name": "sample.xlsx"},
    "gcs_bucket": "nv-data-local-datalake",
    "gcs_folder_path": "gdrive/cod_advance/staging",
    "gdrive_conn_id": "google_cloud_default",
    "gcs_conn_id": "google_cloud_default",
}


def get_gdrive_response(file_name):
    return str(Path(__file__).resolve().parent / file_name)


@patch("gdrive.operators.gdrive_to_gcs_operator.GCSHook")
@patch("gdrive.operators.gdrive_to_gcs_operator.GoogleDriveHook")
@pytest.mark.parametrize("default_date", [DEFAULT_DATE])
def test_execute_with_xlsx_file(mock_gdrive_hook, mock_gcs_hook, dag):
    op_kwargs = operator_kwargs
    mock_gdrive_hook.return_value.download_file.return_value = get_gdrive_response(op_kwargs["gdrive_file"]["name"])
    mock_gcs_hook.return_value.upload.side_effect = None

    task = GoogleDriveToGcsOperator(dag=dag, **op_kwargs)
    ti = TaskInstance(task=task, run_id=f"manual__{DEFAULT_DATE}")
    ti.run(ignore_ti_state=True)

    mock_gdrive_hook.assert_called_once_with(conn_id=op_kwargs["gdrive_conn_id"])
    file_id = op_kwargs["gdrive_file"]["id"]
    mock_gdrive_hook.return_value.download_file.assert_called_once_with(file_id)
    mock_gcs_hook.assert_called_once_with(gcp_conn_id=op_kwargs["gcs_conn_id"])
    context = ti.get_template_context()
    file_path = f"{op_kwargs['gcs_folder_path']}/{context['ts_nodash']}.snappy.parquet"
    gcs_bucket = op_kwargs["gcs_bucket"]
    mock_gcs_hook.return_value.upload.assert_called_once_with(gcs_bucket, file_path, ANY)


@patch("gdrive.operators.gdrive_to_gcs_operator.GCSHook")
@patch("gdrive.operators.gdrive_to_gcs_operator.GoogleDriveHook")
@pytest.mark.parametrize("default_date", [DEFAULT_DATE])
def test_execute_with_csv_file(mock_gdrive_hook, mock_gcs_hook, dag):
    op_kwargs = {**operator_kwargs, "gdrive_file": {"id": "abcde12345", "name": "sample.csv"}}
    mock_gdrive_hook.return_value.download_file.return_value = get_gdrive_response(op_kwargs["gdrive_file"]["name"])
    mock_gcs_hook.return_value.upload.side_effect = None

    task = GoogleDriveToGcsOperator(dag=dag, **op_kwargs)
    ti = TaskInstance(task=task, run_id=f"manual__{DEFAULT_DATE}")
    ti.run(ignore_ti_state=True)

    mock_gdrive_hook.assert_called_once_with(conn_id=op_kwargs["gdrive_conn_id"])
    file_id = op_kwargs["gdrive_file"]["id"]
    mock_gdrive_hook.return_value.download_file.assert_called_once_with(file_id)
    mock_gcs_hook.assert_called_once_with(gcp_conn_id=op_kwargs["gcs_conn_id"])
    context = ti.get_template_context()
    file_path = f"{op_kwargs['gcs_folder_path']}/{context['ts_nodash']}.snappy.parquet"
    gcs_bucket = op_kwargs["gcs_bucket"]
    mock_gcs_hook.return_value.upload.assert_called_once_with(gcs_bucket, file_path, ANY)


@patch("gdrive.operators.gdrive_to_gcs_operator.GCSHook")
@patch("gdrive.operators.gdrive_to_gcs_operator.GoogleDriveHook")
@pytest.mark.parametrize("default_date", [DEFAULT_DATE])
def test_execute_with_schema_file(mock_gdrive_hook, mock_gcs_hook, dag):
    op_kwargs = {
        **operator_kwargs,
        "gdrive_file": {"id": "abcde12345", "name": "sample.csv"},
        "gdrive_schema_file": {"id": "fgh123", "name": "schema.csv"},
    }
    mock_gdrive_hook.return_value.download_file.side_effect = [
        get_gdrive_response(op_kwargs["gdrive_file"]["name"]),
        get_gdrive_response(op_kwargs["gdrive_schema_file"]["name"]),
    ]
    mock_gcs_hook.return_value.upload.side_effect = None

    task = GoogleDriveToGcsOperator(dag=dag, **op_kwargs)
    ti = TaskInstance(task=task, run_id=f"manual__{DEFAULT_DATE}")
    ti.run(ignore_ti_state=True)

    mock_gdrive_hook.assert_called_once_with(conn_id=op_kwargs["gdrive_conn_id"])
    file_id = op_kwargs["gdrive_file"]["id"]
    schema_file_id = op_kwargs["gdrive_schema_file"]["id"]
    mock_gdrive_hook.return_value.download_file.assert_has_calls([call(file_id), call(schema_file_id)])
    mock_gcs_hook.assert_called_once_with(gcp_conn_id=op_kwargs["gcs_conn_id"])
    context = ti.get_template_context()
    file_path = f"{op_kwargs['gcs_folder_path']}/{context['ts_nodash']}.snappy.parquet"
    gcs_bucket = op_kwargs["gcs_bucket"]
    mock_gcs_hook.return_value.upload.assert_called_once_with(gcs_bucket, file_path, ANY)


@patch("gdrive.operators.gdrive_to_gcs_operator.GCSHook")
@patch("gdrive.operators.gdrive_to_gcs_operator.GoogleDriveHook")
@pytest.mark.parametrize("default_date", [DEFAULT_DATE])
def test_execute_with_invalid_file(mock_gdrive_hook, mock_gcs_hook, dag):
    op_kwargs = {**operator_kwargs, "gdrive_file": {"id": "abcde123455"}}
    task = GoogleDriveToGcsOperator(dag=dag, **op_kwargs)
    ti = TaskInstance(task=task, run_id=f"manual__{DEFAULT_DATE}")
    ti.run(ignore_ti_state=True)

    mock_gdrive_hook.assert_not_called()
    mock_gcs_hook.assert_not_called()


@patch("gdrive.operators.gdrive_to_gcs_operator.GCSHook")
@patch("gdrive.operators.gdrive_to_gcs_operator.GoogleDriveHook")
@pytest.mark.parametrize("default_date", [DEFAULT_DATE])
def test_execute_with_no_file(mock_gdrive_hook, mock_gcs_hook, dag):
    op_kwargs = {**operator_kwargs, "gdrive_file": None}
    task = GoogleDriveToGcsOperator(dag=dag, **op_kwargs)
    ti = TaskInstance(task=task, run_id=f"manual__{DEFAULT_DATE}")
    ti.run(ignore_ti_state=True)

    mock_gdrive_hook.assert_not_called()
    mock_gcs_hook.assert_not_called()


def test_split_date_and_non_date_schema():
    test_cases = [
        ({}, ({}, {})),
        (
            {"col1": "string", "col2": "date", "col3": "datetime", "col4": "datetime64"},
            ({"col2": "date", "col3": "datetime", "col4": "datetime64"}, {"col1": "string"}),
        ),
        ({"col1": "string"}, ({}, {"col1": "string"})),
        ({"col1": "date"}, ({"col1": "date"}, {})),
    ]
    for tc in test_cases:
        assert GoogleDriveToGcsOperator._split_date_and_non_date_schema(tc[0]) == tc[1]
