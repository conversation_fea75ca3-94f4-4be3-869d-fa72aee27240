import logging
from tempfile import NamedTemporaryFile

import pandas as pd
import pendulum
from airflow.models import BaseOperator, SkipMixin
from airflow.providers.google.cloud.hooks.gcs import GCSHook

from jotform.hooks.jotform_hook import JotformHook

JOTFORM_TZ = pendulum.timezone("America/New_York")
JOTFORM_TZ_FORMAT = "YYYY-MM-DD HH:mm:ss"
PARTITION_COLUMN = "nv_updated_date"
CREATED_AT = "created_at"
UPDATED_AT = "updated_at"
SYSTEM_ID = "system_id"
FORM_ID = "form_id"


class JotformFormSubmissionsToGCSOperator(BaseOperator, SkipMixin):
    """
    Jotform Form Submissions to GCS Operator
    Writes objects in parquet format with snappy compression

    :param jotform_conn_id:         The Jotform connection id
    :type jotform_conn_id:          string
    :param jotform_form_id:         The Jotform form id
    :type jotform_form_id:          string
    :param gcs_conn_id:             The GCS connection id
    :type gcs_conn_id:              string
    :param gcs_bucket:              The GCS bucket to be used to store the data
    :type gcs_bucket                string
    :param gcs_object_base_path:     The base name of the objects to upload to GCS. Must end with a '/'.
                                    eg. 'orders/', 'reports/', etc
    :type gcs_object_base_path       string
    :param system_id:                 The system id
    :type system_id:                  string
    """

    def __init__(
        self,
        system_id,
        jotform_form_id,
        jotform_form_name,
        gcs_bucket,
        jotform_conn_id="jotform_default",
        gcs_conn_id="google_cloud_default",
        gcs_object_base_path="",
        **kwargs,
    ):

        super().__init__(**kwargs)

        self.system_id = system_id
        self.jotform_conn_id = jotform_conn_id
        self.jotform_form_id = jotform_form_id
        self.jotform_form_name = jotform_form_name
        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_object_base_path = gcs_object_base_path

    def execute(self, context):
        """
        Get all the form submissions from Jotform in a particular interval and upload it to GCS in parquet format
        """
        logging.info(f"Prepping to gather form submissions from Jotform for '{self.jotform_form_name}'")
        from_dt, to_dt = context["execution_date"], context["next_execution_date"]

        logging.info(f"Fetching form submissions from '{from_dt}' to '{to_dt}'")
        form_submissions = self._fetch_form_submissions(from_dt, to_dt)
        if len(form_submissions) == 0:
            logging.info("No records pulled from Jotform")
            return

        df = pd.DataFrame(form_submissions)
        df = self._normalise_jotform_data(df)

        gcs_hook = GCSHook(gcp_conn_id=self.gcs_conn_id)
        dirname = (
            f"{self.gcs_object_base_path}{SYSTEM_ID}={self.system_id}/{FORM_ID}={self.jotform_form_id}"
            f"/{PARTITION_COLUMN}={from_dt.date()}/"
        )
        fname = f"{self.system_id}_{self.jotform_form_id}_{context['ts_nodash']}.snappy.parquet"
        objname = dirname + fname
        with NamedTemporaryFile("w") as tmp:
            final_df = df.drop(columns=PARTITION_COLUMN)
            final_df.to_parquet(tmp.name, compression="snappy", engine="fastparquet", index=False)
            logging.info(f"Uploading to GCS Bucket: '{self.gcs_bucket}'...")
            gcs_hook.upload(self.gcs_bucket, objname, tmp.name)
        logging.info("Uploaded Jotform data to GCS")

    @staticmethod
    def _normalise_jotform_data(df):
        """
        Perform the following normalisation on the jotform data:
        1. Rows whose updated_at is null will be populated with its created_at
        2. Convert Jotform's created_at and updated_at timestamp that are in America/New_York tz to UTC
        3. Append `nv_updated_date` column
        4. De-duplicate the rows, taking the record with the latest updated_at. We pulled data from Jotform twice,
            once by `created_at` filter, the other by `updated_at` filter. Consequently, we could have some duplicates
        """
        df.loc[df[UPDATED_AT].isnull(), UPDATED_AT] = df[CREATED_AT]
        for time_field in (CREATED_AT, UPDATED_AT):
            df[time_field] = df[time_field].apply(
                lambda dt: str(pendulum.from_format(dt, JOTFORM_TZ_FORMAT, tz=JOTFORM_TZ).in_tz("UTC"))
            )
        df[PARTITION_COLUMN] = df[UPDATED_AT].apply(lambda dt: str(pendulum.parse(dt).date()))
        df = df.drop(columns=FORM_ID)
        return df.drop_duplicates("id")

    def _fetch_form_submissions(self, from_dt, to_dt):
        """
        Pull all form submissions from Jotform in [from_dt, to_dt)
        Note: Jotform API expects the datetime to be in "%Y-%m-%d %H:%M%S" format
              We're passing in "%Y-%m-%dT%H:%M:%S%z", but Jotform will do a positional extraction
              and ignore the timezone info

        :type from_dt:              pendulum.Pendulum
        :type to_dt:                pendulum.Pendulum
        :return: list
        """
        hook = JotformHook(conn_id=self.jotform_conn_id)
        result = []
        for field in (CREATED_AT, UPDATED_AT):
            has_fetched_all = False
            offset, limit = 0, 1000
            while not has_fetched_all:
                params = {
                    "limit": limit,
                    "offset": offset,
                    "filter_array": {
                        f"{field}:gte": str(from_dt.in_tz(JOTFORM_TZ)),
                        f"{field}:lt": str(to_dt.in_tz(JOTFORM_TZ)),
                    },
                }
                resp_data = hook.get_form_submissions(self.jotform_form_id, **params)
                if len(resp_data) < limit:
                    has_fetched_all = True
                result.extend(resp_data)
                offset += limit
        logging.info(f"Retrieved {len(result)} records")
        return result
