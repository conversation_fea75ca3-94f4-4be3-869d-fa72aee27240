from datetime import timedelta
import json
import logging
import requests

from airflow.models import Variable
import airflow
from airflow.decorators import task, dag
from airflow.providers.apache.kafka.hooks.base import Kaf<PERSON><PERSON><PERSON>Hook
from common.airflow import notifications as notif
from common.utils.gcs import strip_uri
from metadata import monitoring
from metadata.constants import CDCKafkaTopics, DataLakeGcsBucketUris, Timeout
from confluent_kafka import TopicPartition
from confluent_kafka.admin import OffsetSpec

from google.cloud import storage

from requests.adapters import Retry, HTTPAdapter


DAG_ID = monitoring.RawLayerKafkaOffsetLagDAG.DAG_ID

ENV = Variable.get("env")
# env = PROD or DEV
RAW_GS_BUCKET = strip_uri(DataLakeGcsBucketUris(ENV).raw)

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if ENV == "prod" else None,
}


def generate_prometheus_payload(*, data):
    payload = ""
    for key, value in data.items():
        # Metric name: my_metric_{key}
        metric_name = f"ticdc_kafka_offset_lag_partition_{key}"

        # Help and Type comments
        payload += f"# HELP {metric_name} Description of the metric\n"
        payload += f"# TYPE {metric_name} gauge\n"

        # Metric data
        payload += f'{metric_name}{{key="{key}"}} {value}\n'

    return payload


def get_offsets_at_checkpoint(*, checkpoint_datetime):
    client = storage.Client()
    folder_prefix = "ticdc_stream/checkpoint/offsets/"
    bucket = client.bucket(RAW_GS_BUCKET)

    # the cost of iterating through blobs is cheap because the older
    # checkpoints get purged leaving only more or less than 100 entries to iterate
    blob = [
        i
        for i in bucket.list_blobs(prefix=folder_prefix)
        if i.time_created.replace(second=0, microsecond=0) == checkpoint_datetime
    ][0]

    checkpoint_content = blob.download_as_text()

    # Extract the third line, this is where the offsets are stored
    third_line = checkpoint_content.splitlines()[2]

    # Parse the third line as JSON
    parsed_data = json.loads(third_line)

    # Extract and convert the desired dictionary
    return {int(k): int(v) for k, v in parsed_data["data-prod-ticdc-data-engineering-gl-cdc"].items()}


@dag(
    dag_id=DAG_ID,
    default_args=default_args,
    max_active_runs=1,
    schedule="*/10 * * * *",
    start_date=airflow.utils.dates.days_ago(1),
    catchup=True,
    dagrun_timeout=Timeout.TWO_MINUTES,
)
def create_dag():
    @task
    def get_latest_offset_in_kafka_topic(*, topic):
        """
        Fetch the latest offsets for a Kafka topic in data cluster
        """
        logging.info("Setting up connection to connect to kafka cluster")
        admin_client = KafkaBaseHook().get_conn
        metadata = admin_client.list_topics()

        # Get partitions for the topic
        partitions = metadata.topics[topic].partitions.keys()
        topic_partitions = [TopicPartition(topic, partition) for partition in partitions]

        # Create the offset specifications
        offset_specs = {tp: OffsetSpec.latest() for tp in topic_partitions}

        # Fetch offsets
        offset_futures = admin_client.list_offsets(offset_specs)
        latest_offsets = {}

        for tp, future in offset_futures.items():
            # Resolve the future and extract the offset
            offset_info = future.result()  # Use .result() to get the offset info
            latest_offsets[tp.partition] = offset_info.offset

        for partition, offset in latest_offsets.items():
            logging.info(f"Partition {partition}: Latest offset = {offset}")

        return latest_offsets

    @task
    def calculate_lag(offsets_from_kafka, data_interval_end=None):
        logging.info(f"Offsets from Kafka, passed from the previous task: {offsets_from_kafka}")
        checkpoint_datetime = data_interval_end.replace(second=0, microsecond=0)
        logging.info(f"Retrieving checkpoint offsets for datetime: {checkpoint_datetime}")
        offsets_from_checkpoint = get_offsets_at_checkpoint(checkpoint_datetime=checkpoint_datetime)
        logging.info(f"Offsets at checkpoint: {offsets_from_checkpoint}")
        return {
            int(partition): int(latest_offset) - offsets_from_checkpoint[int(partition)]
            for partition, latest_offset in offsets_from_kafka.items()
        }

    @task
    def push_lag_to_prom(*, offsets_lag):
        # Push this lag to prometheus
        logging.info("Pushing the offset lag to prom gateway!")
        payload = generate_prometheus_payload(data=offsets_lag)
        job_name = "ticdc_spark_offset_monitoring"
        host = Variable.get("NV_PROMETHEUS_PUSHGATEWAY_URL")
        url = f"{host}/metrics/job/{job_name}"

        s = requests.Session()

        retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[500, 502, 503, 504])

        s.mount("http://", HTTPAdapter(max_retries=retries))
        # Send the POST request with the metric data
        response = s.post(url, data=payload)

        if response.status_code == 200:
            logging.info("Metrics successfully pushed to the Push Gateway")
        else:
            raise Exception(f"Failed to push metrics. Status code: {response.status_code}")

    offsets = get_latest_offset_in_kafka_topic(topic=CDCKafkaTopics(ENV).ticdc)
    offsets_lag = calculate_lag(offsets)
    push_lag_to_prom(offsets_lag=offsets_lag)


dag = create_dag()
