from datetime import datetime, timedelta
from importlib import import_module

from airflow import DAG
from airflow.models import Variable
from airflow.providers.google.cloud.sensors.gcs import GCSObjectsWithPrefixExistenceSensor

from common.airflow import notifications as notif
from common.utils.gcs import strip_uri
from data_warehouse.utils import airflow
from metadata import data_warehouse
from metadata.constants import MASKED_DATA_WAREHOUSE_BASE_URI, Timeout
from custom_operators.dwh_to_gcs_operator import DWHToGCSOperator

DAG_ID = data_warehouse.DataWarehouseExportDAG.DAG_ID
TASKS = (
    data_warehouse.DataWarehouseExportDAG.Task.PARTNERSHIP_WEBHOOK_EXPORT_MASKED,
    data_warehouse.DataWarehouseExportDAG.Task.PARTNERSHIP_WEBHOOK_SUMMARY_EXPORT_MASKED,
)
env = Variable.get("env")
gs_bucket = strip_uri(MASKED_DATA_WAREHOUSE_BASE_URI.format(env))

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2022, 1, 25),
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

external_sensors = {}
gcs_sensors = {}
with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    max_active_runs=1,
    schedule_interval="0 18 * * *",
    catchup=False,
    dagrun_timeout=Timeout.FIFTEEN_HOURS,
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
) as dag:
    for task in TASKS:
        task_config = import_module(f"data_warehouse.tasks.dwh_export_tasks.{task}").get_task_config(env)

        dwh_to_gcs_task = DWHToGCSOperator(
            task_id=task_config.task_id,
            name=task_config.task_name,
            input_path=task_config.input_path,
            conf=task_config.conf if task_config.conf else None,
            sql=task_config.sql,
            output_path=task_config.output_path,
        )

        for ext_dep in task_config.depends_on_external:
            ext_dag_task_id = f"{ext_dep.dag_id}_{ext_dep.task_id}"
            if ext_dag_task_id not in external_sensors:
                external_sensors[ext_dag_task_id] = airflow.get_dwh_external_task_sensor(
                    ext_dep.dag_id, ext_dep.task_id, DAG_ID, ext_dep.execution_delta
                )
            gcs_sensor_id = f"check_gcs_bucket_existence_{ext_dep.task_id}"
            if gcs_sensor_id not in gcs_sensors:
                gcs_sensors[gcs_sensor_id] = GCSObjectsWithPrefixExistenceSensor(
                    task_id=gcs_sensor_id,
                    bucket=gs_bucket,
                    prefix=task_config.gcs_check_prefix,
                    params={"alert_channel": task_config.alert_channel},
                    google_cloud_conn_id="google_cloud_default",
                    execution_timeout=Timeout.ONE_MINUTE,
                    retries=1,
                )
            external_sensors[ext_dag_task_id] >> gcs_sensors[gcs_sensor_id] >> dwh_to_gcs_task
