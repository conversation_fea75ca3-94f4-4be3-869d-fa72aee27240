import logging
import sys

from delta import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import date_format

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

CREATED_AT = "date_stop"
SYSTEM_ID = "system_id"


def _get_delta_path(gcs_bucket, entity):
    """
    Returns path at which Facebook entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Facebook entity (e.g. ad)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/facebook/delta/{entity}"


def _get_obj_path(gcs_bucket, entity):
    """
    Returns path at which incremental change data of Facebook entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Facebook entity (e.g. ad)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}-raw/facebook/objects/{entity}"


def _get_object(spark, path, date_str):
    """
    Fetches incremental change data of Facebook entity for a specified updated date.

    :param spark:       Spark session
    :param path:        GCS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :return:            Spark dataframe of entity change data
    """
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(CREATED_AT, "yyyy-MM"))
    )
    return df


def merge_delta(spark, gcs_bucket, entity, date_str):
    obj_path = _get_obj_path(gcs_bucket, entity)
    obj_df = _get_object(spark, obj_path, date_str)
    if obj_df.count() == 0:
        logging.info("No records to update")
        return

    delta_path = _get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        obj_df.write.format("delta").mode("append").save(delta_path)
        logging.info("Results are updated")
    else:
        obj_df.write.format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(delta_path)
        logging.info(f"Results are save in {delta_path}")


if __name__ == "__main__":
    gcs_bucket, entity, date_str = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, gcs_bucket, entity, date_str)
    spark.stop()
