import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.ORDER_PROFITS_PH_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.ORDER_PROFITS_PH_MASKED,
    system_ids=(SystemID.PH,),
    depends_on=(
        data_warehouse.CostCardDAG.Task.COST_CARD_INTERMEDIATE_MASKED,
        data_warehouse.CostCardDAG.Task.PRICED_ORDERS_INTERMEDIATE_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).COST_CARD_INTERMEDIATE,
                view_name="cost_card_intermediate",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).PRICED_ORDERS_INTERMEDIATE,
                view_name="priced_orders_intermediate",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_BILLING_ZONE_MAPPING_PH,
                view_name="cost_card_billing_zone_mapping_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_TIKTOK_PRICING_CONFIG_PH,
                view_name="cost_card_tiktok_pricing_config_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_TIKTOK_PRICING_TIER_CONFIG_PH,
                view_name="cost_card_tiktok_pricing_tier_config_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_TIKTOK_XB_TP_RATE_REFERENCE_PH,
                view_name="cost_card_tiktok_xb_tp_rate_reference_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_TIKTOK_XB_TP_PRICING_CONFIG_PH,
                view_name="cost_card_tiktok_xb_tp_pricing_config_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_XB_TP_PRICING_CONFIG_PH,
                view_name="cost_card_xb_tp_pricing_config_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_XB_TP_SHIPPERS_PH,
                view_name="cost_card_xb_tp_shippers_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_LAZADA_DISCOUNT_CONFIG_PH,
                view_name="cost_card_lazada_discount_config_ph",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_WEIGHT_BINS,
                view_name="cost_card_weight_bins",
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="base",
                jinja_template="""
                -- Consolidate columns from cost_card_intermediate, priced_orders_intermediate and ttr tables

                select distinct
                    cc.order_id
                    , cc.tracking_id
                    , cc.creation_datetime
                    , cc.created_month
                    , cc.terminal_datetime
                    , date(cc.terminal_datetime) as terminal_date
                    , date_trunc('month', cc.terminal_datetime) as terminal_month
                    , cc.terminal_datetime_type
                    , cc.system_id
                    , cc.granular_status

                    -- Shipper information
                    , po.shipper_id
                    , po.shipper_name
                    , po.sales_channel
                    , po.shipper_parent_id_coalesce
                    , po.shipper_parent_name_coalesce
                    , po.sf_acc_id
                    , po.sf_parent_acc_id_coalesce
                    , po.sf_parent_acc_name_coalesce
                    , po.sf_parent_acc_shipper_id_coalesce
                    , po.sf_sales_territory
                    , po.sf_sales_team
                    , po.sf_nv_product_line

                    , cc.delivery_type
                    , cc.om_size
                    , cc.om_width
                    , cc.om_height
                    , cc.om_length
                    , cc.om_cubic_measure
                    , cc.om_weight
                    , least(cc.om_weight, 200) as capped_om_weight
                    , cc.om_delivery_fee
                    , cc.om_estimated_weight
                    , cc.om_estimated_volume
                    , cc.third_party_flag
                    , cc.force_success_flag
                    , cc.airhaul_flag
                    , cc.seahaul_flag
                    , cc.direct_dispatch_flag
                    , po.rts_flag
                    , po.first_mile_type
                    , po.service_level
                    , po.service_type

                    -- Timestamp information
                    , po.pickup_datetime
                    , po.nv_pickup_datetime
                    , po.inbound_datetime
                    , po.delivery_success_datetime

                    -- Cost information
                    , cc.fm_cost
                    , cc.sort_cost
                    , cc.mm_cost
                    , cc.lm_cost
                    , cc.hub_cost
                    , cc.total_ops_cost
                    , cc.total_raw_events
                    , cc.fm_non_null_count
                    , cc.sort_non_null_count
                    , cc.mm_non_null_count
                    , cc.lm_non_null_count
                    , cc.hub_non_null_count
                    , cc.total_non_null_count
                    , cc.fm_null_count
                    , cc.sort_null_count
                    , cc.mm_null_count
                    , cc.lm_null_count
                    , cc.hub_null_count
                    , cc.null_cost_count

                    , cc.mm_intersort_cost
                    , cc.mm_pri_land_cost
                    , cc.mm_pri_air_cost
                    , cc.mm_pri_sea_cost
                    , cc.mm_sec_cost
                    , cc.mm_dd_cost
                    , cc.mm_intersort_non_null_count
                    , cc.mm_pri_land_non_null_count
                    , cc.mm_pri_air_non_null_count
                    , cc.mm_pri_sea_non_null_count
                    , cc.mm_sec_non_null_count
                    , cc.mm_dd_non_null_count
                    , cc.mm_intersort_null_count
                    , cc.mm_pri_land_null_count
                    , cc.mm_pri_air_null_count
                    , cc.mm_pri_sea_null_count
                    , cc.mm_sec_null_count
                    , cc.mm_dd_null_count

                    -- Pricing information
                    , po.billing_delivery_fee
                    , po.cod_value
                    , po.cod_collected
                    , po.script_id
                    , po.pricing_breakdown
                    , po.pricer_rate_card_id
                    , po.pricing_profile_id
                    , po.billing_size
                    , po.billing_weight
                    , po.billing_weight_source
                    , po.pricing_billable_weight
                    , if(po.pricing_billable_weight is null, null, least(po.pricing_billable_weight, 200)) as capped_pricing_billable_weight
                    , po.pricing_from_billing_zone
                    , po.pricing_from_region
                    , po.pricing_from_city
                    , po.pricing_to_billing_zone
                    , po.pricing_to_region
                    , po.pricing_to_city
                    , po.cod_fee
                    , po.rts_fee
                    , po.flat_discount
                    , po.insurance_fee
                    , po.handling_fee
                    , po.pricing_created_at                        

                    -- OD hub information from TTR
                    , ttr.origin_hub_id
                    , ttr.origin_hub_name
                    , ttr.origin_hub_address_city as origin_hub_city
                    , ttr.origin_hub_region
                    , ttr.dest_hub_id
                    , ttr.dest_hub_name
                    , ttr.dest_hub_address_city as dest_hub_city
                    , ttr.dest_hub_region

                    -- FM information
                    , cc.fm_waypoint_id
                    , cc.fm_pudo_id

                    -- Utilization
                    , cc.mm_sum_utilization_numerator
                    , cc.mm_sum_utilization_denominator
                    , cc.mm_avg_utilization_rate

                    -- Country Specific Columns
                    , cost_card_weight_bins.weight_bracket
                    , cc.hub_fm_cost
                    , cc.hub_wh_cost
                    , cc.hub_lm_cost
                    , cc.hub_fm_non_null_count
                    , cc.hub_wh_non_null_count
                    , cc.hub_lm_non_null_count
                    , cc.hub_fm_null_count
                    , cc.hub_wh_null_count
                    , cc.hub_lm_null_count

                from cost_card_intermediate as cc
                left join priced_orders_intermediate as po 
                    on cc.order_id = po.order_id
                left join transit_time_report as ttr 
                    on cc.order_id = ttr.order_id
                left join cost_card_weight_bins
                    on po.pricing_billable_weight >= cost_card_weight_bins.from_weight_inclusive
                    and po.pricing_billable_weight < cost_card_weight_bins.to_weight_exclusive
                """,
            ),
            base.TransformView(
                view_name="unique_data",
                jinja_template="""
                                    -- Get distinct records key data from base
                                   with uniq_base_data as (select 
                                        order_id,pricing_from_billing_zone,pricing_to_billing_zone,count(*)
                                    from base
                                    group by 1,2,3
                                    )
                                    select 
                                        uniq_base_data.order_id
                                        , uniq_base_data.pricing_from_billing_zone
                                        , uniq_base_data.pricing_to_billing_zone
                                        , coalesce(origin_region.lazada_region, 'missing') as lazada_origin_hub_region
                                        , coalesce(dest_region.lazada_region, 'missing') as lazada_dest_hub_region
                                        , coalesce(origin_region.tiktok_region, 'missing') as tiktok_origin_hub_region
                                        , coalesce(dest_region.tiktok_region, 'missing') as tiktok_dest_hub_region
                                        , coalesce(origin_region.tiktok_xb_region, 'missing') as tiktok_xb_origin_hub_region
                                        , coalesce(dest_region.tiktok_xb_region, 'missing') as tiktok_xb_dest_hub_region
                                        , count(*)
                                    from uniq_base_data
                                    left join cost_card_billing_zone_mapping_ph as origin_region
                                        on nvl(uniq_base_data.pricing_from_billing_zone, 'no_data') = nvl(origin_region.pricing_billing_zone, 'no_data')
                             left join cost_card_billing_zone_mapping_ph as dest_region
                                 on nvl(uniq_base_data.pricing_to_billing_zone, 'no_data') = nvl(dest_region.pricing_billing_zone, 'no_data')
                                    group by 1,2,3,4,5,6,7,8,9
                                    """,
            ),

            base.TransformView(
                view_name="base_region",
                jinja_template="""
                                    select 
                                        base.*
                                        , lazada_origin_hub_region
                                        , lazada_dest_hub_region
                                        , tiktok_origin_hub_region
                                        , tiktok_dest_hub_region
                                        , tiktok_xb_origin_hub_region
                                        , tiktok_xb_dest_hub_region
                                    from base
                                    join unique_data on base.order_id = unique_data.order_id
                                        and nvl(base.pricing_from_billing_zone, 'no_data') = nvl(unique_data.pricing_from_billing_zone, 'no_data')
                                        and nvl(base.pricing_to_billing_zone, 'no_data') = nvl(unique_data.pricing_to_billing_zone , 'no_data')              
                                    """,
            ),
            base.TransformView(
                view_name="tiktok_local",
                jinja_template="""
                -- To get Tiktok Local revenue
                -- There are 7 Rate cards in total with 1 base rate card and 6 volume tier rate cards 

                with 
                    tier_rates as (
                    -- Cast Gsheet datatype

                        select 
                            date(start_date) as start_date
                            , date(end_date) as end_date
                            , origin_hub_region
                            , dest_hub_region
                            , cast(lower_weight_bound as int) as lower_weight_bound
                            , cast(upper_weight_bound_inclusive as int) as upper_weight_bound_inclusive
                            , cast(rate_card as float) as rate_card
                            , cast(per_additional_kg as int) as per_additional_kg
                            , cast(replace(lower_volume_bound_inclusive, ',', '') as int) as lower_volume_bound_inclusive
                            , cast(replace(upper_volume_bound, ',', '') as int) as upper_volume_bound
                            , volume_tier
                        from cost_card_tiktok_pricing_tier_config_ph

                    )

                    , base_rates as (
                    -- Cast Gsheet datatype

                        select 
                            date(start_date) as start_date
                            , date(end_date) as end_date
                            , origin_hub_region
                            , dest_hub_region
                            , cast(lower_weight_bound as int) as lower_weight_bound
                            , cast(upper_weight_bound_inclusive as int) as upper_weight_bound_inclusive
                            , cast(base_price as float) as base_price
                            , cast(per_additional_kg as int) as per_additional_kg
                        from cost_card_tiktok_pricing_config_ph

                    )

                    , volume_monthly as (

                        select
                            terminal_month
                            , tiktok_dest_hub_region
                            , count(order_id) as volume_monthly
                        from base_region
                        where 
                            -- Tiktok local shipper volume that originated from MM/GMA
                            shipper_parent_id_coalesce = 7823651
                            and terminal_datetime is not null
                            and granular_status != 'Cancelled'
                            and tiktok_origin_hub_region in ('Metro Manila', 'GMA')
                        group by 1,2                    

                    )

                    , tiktok_local_rates as (
                    -- Orders from MM/GMA enjoy different rates according to destination region volume tier 
                    -- Orders that do not fit into volume tier rates will have base rates

                        select
                            base_region.*
                            , volume_monthly.volume_monthly
                            , tier_rates.volume_tier
                            , tier_rates.rate_card + (base_region.capped_pricing_billable_weight - tier_rates.lower_weight_bound) * tier_rates.per_additional_kg as tier_rates
                            , base_rates.base_price + (base_region.capped_pricing_billable_weight  - base_rates.lower_weight_bound) * base_rates.per_additional_kg as base_rates
                            , coalesce(
                                tier_rates.rate_card + (base_region.capped_pricing_billable_weight - tier_rates.lower_weight_bound) * tier_rates.per_additional_kg
                                , base_rates.base_price + (base_region.capped_pricing_billable_weight - base_rates.lower_weight_bound) * base_rates.per_additional_kg
                                , 0
                            ) as order_rates
                        from base_region
                        left join volume_monthly
                            on base_region.terminal_month = volume_monthly.terminal_month
                            and base_region.tiktok_dest_hub_region = volume_monthly.tiktok_dest_hub_region
                        left join tier_rates
                            on base_region.terminal_date >= tier_rates.start_date
                            and base_region.terminal_date <= tier_rates.end_date
                            and base_region.capped_pricing_billable_weight > tier_rates.lower_weight_bound
                            and base_region.capped_pricing_billable_weight <= tier_rates.upper_weight_bound_inclusive
                            and base_region.tiktok_origin_hub_region = tier_rates.origin_hub_region
                            and base_region.tiktok_dest_hub_region = tier_rates.dest_hub_region
                            and volume_monthly >= tier_rates.lower_volume_bound_inclusive
                            and volume_monthly < tier_rates.upper_volume_bound
                        left join base_rates
                            on base_region.terminal_date >= base_rates.start_date
                            and base_region.terminal_date <= base_rates.end_date
                            and base_region.capped_pricing_billable_weight > base_rates.lower_weight_bound
                            and base_region.capped_pricing_billable_weight <= base_rates.upper_weight_bound_inclusive
                            and base_region.tiktok_origin_hub_region = base_rates.origin_hub_region
                            and base_region.tiktok_dest_hub_region = base_rates.dest_hub_region
                        where 
                            -- Tiktok Local
                            base_region.shipper_parent_id_coalesce = 7823651 

                    )

                select
                    *
                    , 1.12 * order_rates as calculated_delivery_fee_with_tax
                    , order_rates as calculated_delivery_fee_without_tax
                    -- Double dashboard fee for RTS orders 
                    , if(rts_flag = 1, order_rates * 2, order_rates) as dashboard_fee
                    , tiktok_origin_hub_region as dashboard_origin_region
                    , tiktok_dest_hub_region as dashboard_dest_region
                    , if(om_weight <= 200 and total_ops_cost is not null and order_rates <= 2000, 1, 0) as dashboard_flag
                from tiktok_local_rates
                """,
            ),
            base.TransformView(
                view_name="tiktok_xb",
                jinja_template="""
                -- To get Tiktok XB & DI orders revenue
                -- Hub mapping logic is provided by regional XB DA, rates Gsheet maintained by regional XB commercial team

                with 
                    tiktok_xb_base as (

                        select
                            *
                            , case
                                when origin_hub_name like '%CBY2%' then 'CBY2'
                                when origin_hub_name like '%-ILO-%' then 'ILO'
                                when origin_hub_name like '%-PAG-%' then 'PAG'
                                when origin_hub_name like '%-ZAM-%' then 'ZAM'
                                when origin_hub_name like '%-CDO-%' then 'CDO'
                                when origin_hub_name like '%-BXU-%' then 'BXU'
                                when origin_hub_name like '%-TACL-%' then 'TAC'
                                when origin_hub_name like '%-BAC-%' then 'BAC'
                                when origin_hub_name like '%-DAV-%' or origin_hub_name in ('WH-DAVAO','WH-DAV') then 'DVO'
                                when origin_hub_name like '%-PAL-%' then 'PAL'
                                when origin_hub_name like '%-COR-%' then 'COR'
                                when origin_hub_name like '%-CEB-%' then 'CEB'
                                when origin_hub_region = 'Central Visayas' then 'CEB'
                                when origin_hub_region in ('Metro Manila','GMA') then 'MM/GMA'
                                when origin_hub_name like '%-CAL-%' then 'TAC (Calbayog)'
                                when origin_hub_region in ('North Luzon','South Luzon') and origin_hub_city not in ('Palawan') then 'LUZON'
                                else origin_hub_name
                            end as origin_hub

                            , case
                                when dest_hub_name like '%CBY2%' then 'CBY2'
                                when dest_hub_name like '%-ILO-%' then 'ILO'
                                when dest_hub_name like '%-PAG-%' then 'PAG'
                                when dest_hub_name like '%-ZAM-%' then 'ZAM'
                                when dest_hub_name like '%-CDO-%' then 'CDO'
                                when dest_hub_name like '%-BXU-%' then 'BXU'
                                when dest_hub_name like '%-TACL-%' then 'TAC'
                                when dest_hub_name like '%-BAC-%' then 'BAC'
                                when dest_hub_name like '%-DAV-%' then 'DVO'
                                when dest_hub_name like '%-PAL-%' then 'PAL'
                                when dest_hub_name like '%-COR-%' then 'COR'
                                when dest_hub_name like '%-CEB-%' then 'CEB'
                                when dest_hub_region = 'Central Visayas' then 'CEB'
                                when dest_hub_region in ('Metro Manila','GMA') then 'MM/GMA'
                                when dest_hub_name like '%-CAL-%' then 'TAC (Calbayog)'
                                when dest_hub_region in ('North Luzon','South Luzon') and dest_hub_city not in ('Palawan') then 'LUZON'
                                else dest_hub_name
                            end as dest_hub

                            , case 
                                when dest_hub_name = 'Z - Out of Zone' then 1 
                                else 0 
                            end as ooz_flag

                        from base_region
                        where
                            -- Tiktok XB Shipper and Tiktok DI Shipper
                            shipper_parent_id_coalesce in (7314943, 10416620) 

                    )

                    ,reference as (
                    -- Cast Gsheet datatype

                        select
                            origin_hub
                            , dest_hub
                            , rate_reference
                            , cast(multiplier as float) as multiplier
                            , system_id
                        from cost_card_tiktok_xb_tp_rate_reference_ph

                    )

                    , rates as (
                    -- Cast Gsheet datatype

                        select
                            date(start_date) as start_date
                            , date(end_date) as end_date
                            , rate_reference
                            , cast(lower_weight_bound as float) as lower_weight_bound
                            , cast(upper_weight_bound_inclusive as float) as upper_weight_bound_inclusive
                            , cast(base_price as float) as base_price
                        from cost_card_tiktok_xb_tp_pricing_config_ph

                    )

                    , tiktok_xb_rates as (

                        select
                            tiktok_xb_base.*
                            , reference.origin_hub as ref_origin_hub
                            , reference.dest_hub as ref_dest_hub
                            , rates.rate_reference
                            -- Cancelled orders should have 0 as dashboard fee
                            , if(granular_status = 'Cancelled', 0, reference.multiplier * rates.base_price) as base_price_with_multiplier
                        from tiktok_xb_base
                        left join reference
                            on tiktok_xb_base.origin_hub = reference.origin_hub
                            and tiktok_xb_base.dest_hub = reference.dest_hub
                        left join rates
                            on reference.rate_reference = rates.rate_reference
                            and tiktok_xb_base.terminal_date >= rates.start_date
                            and tiktok_xb_base.terminal_date <= rates.end_date 
                            and tiktok_xb_base.capped_pricing_billable_weight > rates.lower_weight_bound
                            and tiktok_xb_base.capped_pricing_billable_weight <= rates.upper_weight_bound_inclusive                    

                    )

                select
                    tiktok_xb_rates.*
                    , 1.12 * base_price_with_multiplier as calculated_delivery_fee_with_tax
                    , base_price_with_multiplier as calculated_delivery_fee_without_tax
                    , base_price_with_multiplier as dashboard_fee
                    , tiktok_xb_origin_hub_region as dashboard_origin_region
                    , tiktok_xb_dest_hub_region as dashboard_dest_region
                    , if(om_weight <= 200 and total_ops_cost is not null and base_price_with_multiplier <= 2000, 1, 0) as dashboard_flag
                from tiktok_xb_rates
                """,
            ),
            base.TransformView(
                view_name="xb",
                jinja_template="""
                -- To get XB orders revenue based on TP
                -- Shipper list Gsheet maintained by Reg FP&A, based on data from XB finance

                with
                    rates as (
                    -- Cast Gsheet datatype

                        select
                            date(start_date) as start_date
                            , date(end_date) as end_date
                            , dest_hub_region
                            , cast(lower_weight_bound as float) as lower_weight_bound
                            , cast(upper_weight_bound_inclusive as float) as upper_weight_bound_inclusive
                            , cast(base_price as float) as base_price
                            , cast(per_additional_half_kg as int) as per_additional_half_kg
                        from cost_card_xb_tp_pricing_config_ph

                    )

                    , xb_tp_rates as (

                        select
                            base_region.*
                            , rates.base_price + 
                            (base_region.capped_pricing_billable_weight - rates.lower_weight_bound) 
                            * rates.per_additional_half_kg 
                            * 2
                            as order_rates
                        from base_region
                        left join cost_card_xb_tp_shippers_ph
                            on base_region.shipper_parent_id_coalesce = cost_card_xb_tp_shippers_ph.global_shipper_id
                        left join rates
                            on base_region.terminal_date >= rates.start_date
                            and base_region.terminal_date <= rates.end_date 
                            and base_region.capped_pricing_billable_weight > rates.lower_weight_bound
                            and base_region.capped_pricing_billable_weight <= rates.upper_weight_bound_inclusive
                            and base_region.dest_hub_region = rates.dest_hub_region
                        where cost_card_xb_tp_shippers_ph.global_shipper_id is not null

                    )

                    , final as (

                        select
                            *
                            , 1.12 * order_rates as calculated_delivery_fee_with_tax
                            , order_rates as calculated_delivery_fee_without_tax
                            , order_rates as dashboard_fee
                            , origin_hub_region as dashboard_origin_region
                            , dest_hub_region as dashboard_dest_region
                            , if(om_weight <= 200 and total_ops_cost is not null and order_rates <= 2000, 1, 0) as dashboard_flag
                        from xb_tp_rates
                    
                    )

                select * from final
                    
                """,
            ),
            base.TransformView(
                view_name="lazada",
                jinja_template="""
                -- To get Lazada orders revenue
                -- Discount qualified on parent shipper level based on OD/Weight/Volume Threshold

                with 
                    shipper_volumes as (

                        select
                            terminal_month
                            , shipper_parent_id_coalesce
                            , count(order_id) as volume_monthly
                        from base_region
                        where 
                            terminal_datetime is not null
                            and granular_status != 'Cancelled'
                            -- Lazada Shipper
                            and shipper_parent_id_coalesce = 341153
                        group by 1,2

                    )

                    , discounts as (
                    -- Cast Gsheet datatype

                        select 
                            date(start_date) as start_date
                            , date(end_date) as end_date
                            , origin_hub_region
                            , dest_hub_region
                            , cast(lower_weight_bound as float) as lower_weight_bound
                            , cast(upper_weight_bound_inclusive as float) as upper_weight_bound_inclusive
                            , cast(base_discount as float) as base_discount
                            , cast(per_additional_half_kg as float) as per_additional_half_kg
                            , cast(volume_threshold as int) as volume_threshold
                        from cost_card_lazada_discount_config_ph                        

                    )

                    , lazada_discount as (

                        select
                            base_region.*
                            , shipper_volumes.volume_monthly
                            , coalesce(
                                discounts.base_discount + (base_region.capped_pricing_billable_weight - discounts.lower_weight_bound) * discounts.per_additional_half_kg * 2
                                , 0
                            ) as volume_discount
                        from base_region
                        left join shipper_volumes
                            on base_region.terminal_month = shipper_volumes.terminal_month
                            and base_region.shipper_parent_id_coalesce = shipper_volumes.shipper_parent_id_coalesce
                        left join discounts
                            on base_region.terminal_date >= discounts.start_date
                            and base_region.terminal_date <= discounts.end_date
                            and base_region.capped_pricing_billable_weight > discounts.lower_weight_bound
                            and base_region.capped_pricing_billable_weight <= discounts.upper_weight_bound_inclusive
                            and base_region.lazada_origin_hub_region = discounts.origin_hub_region
                            and base_region.lazada_dest_hub_region = discounts.dest_hub_region
                            and shipper_volumes.volume_monthly >= discounts.volume_threshold
                        where 
                            -- Lazada Shipper
                            base_region.shipper_parent_id_coalesce = 341153

                    )

                select
                    *
                    , billing_delivery_fee - volume_discount as calculated_delivery_fee_with_tax
                    , billing_delivery_fee - volume_discount as calculated_delivery_fee_without_tax
                    -- Adding rts_fee as requested by FPA and null handling
                    , billing_delivery_fee - volume_discount + coalesce(rts_fee, 0) as dashboard_fee
                    , lazada_origin_hub_region as dashboard_origin_region
                    , lazada_dest_hub_region as dashboard_dest_region
                    , if(om_weight <= 200 and total_ops_cost is not null and (billing_delivery_fee - volume_discount <= 2000), 1, 0) as dashboard_flag
                from lazada_discount


                """,
            ),
            base.TransformView(
                view_name="fs",
                jinja_template="""
                -- To get FS orders revenue

                select
                    base_region.*
                    , base_region.billing_delivery_fee * 1.12 as calculated_delivery_fee_with_tax
                    , base_region.billing_delivery_fee as calculated_delivery_fee_without_tax
                    , base_region.billing_delivery_fee as dashboard_fee
                    , base_region.origin_hub_region as dashboard_origin_region
                    , base_region.dest_hub_region as dashboard_dest_region
                    , if(base_region.om_weight <= 200 
                        and base_region.total_ops_cost is not null 
                        and base_region.billing_delivery_fee <= 2000
                        , 1, 0) as dashboard_flag
                from base_region
                left join cost_card_xb_tp_shippers_ph
                    on base_region.shipper_parent_id_coalesce = cost_card_xb_tp_shippers_ph.global_shipper_id
                where 
                    base_region.shipper_parent_id_coalesce not in (7823651, 7314943, 10416620, 341153)
                    and base_region.sales_channel = 'Field Sales'
                    and cost_card_xb_tp_shippers_ph.global_shipper_id is null
                """,
            ),
            base.TransformView(
                view_name="others",
                jinja_template="""
                -- Orders that do not belong to Tiktok Local / Tiktok XB / Lazada / FS use billing_delivery_fee as revenue

                select
                    base_region.*
                    , base_region.billing_delivery_fee * 1.12 as calculated_delivery_fee_with_tax
                    , base_region.billing_delivery_fee as calculated_delivery_fee_without_tax
                    , base_region.billing_delivery_fee as dashboard_fee
                    , base_region.origin_hub_region as dashboard_origin_region
                    , base_region.dest_hub_region as dashboard_dest_region
                    , if(base_region.om_weight <= 200 
                        and base_region.total_ops_cost is not null 
                        and base_region.billing_delivery_fee <= 2000
                        , 1, 0) as dashboard_flag
                from base_region
                left join cost_card_xb_tp_shippers_ph
                    on base_region.shipper_parent_id_coalesce = cost_card_xb_tp_shippers_ph.global_shipper_id
                where 
                    base_region.shipper_parent_id_coalesce not in (7823651, 7314943, 10416620, 341153)
                    and base_region.sales_channel <> 'Field Sales'
                    and cost_card_xb_tp_shippers_ph.global_shipper_id is null
                """,
            ),
            base.TransformView(
                view_name="pre_final",
                jinja_template="""
                -- Combine orders from different markets / channels together

                select {{ columns | join(',') }} from tiktok_local
                union all 
                select {{ columns | join(',') }} from tiktok_xb
                union all
                select {{ columns | join(',') }} from xb
                union all
                select {{ columns | join(',') }} from lazada
                union all
                select {{ columns | join(',') }} from fs
                union all
                select {{ columns | join(',') }} from others

                """,
                jinja_arguments={
                    "columns": [
                        "order_id",
                        "tracking_id",
                        "creation_datetime",
                        "created_month",
                        "terminal_datetime",
                        "terminal_month",
                        "terminal_datetime_type",
                        "system_id",
                        "granular_status",

                        # Shipper information
                        "shipper_id",
                        "shipper_name",
                        "sales_channel",
                        "shipper_parent_id_coalesce",
                        "shipper_parent_name_coalesce",
                        "sf_acc_id",
                        "sf_parent_acc_id_coalesce",
                        "sf_parent_acc_name_coalesce",
                        "sf_parent_acc_shipper_id_coalesce",
                        "sf_sales_territory",
                        "sf_sales_team",
                        "sf_nv_product_line",

                        "delivery_type",
                        "om_size",
                        "om_width",
                        "om_height",
                        "om_length",
                        "om_cubic_measure",
                        "om_weight",
                        "om_delivery_fee",
                        "om_estimated_weight",
                        "om_estimated_volume",
                        "third_party_flag",
                        "force_success_flag",
                        "airhaul_flag",
                        "seahaul_flag",
                        "direct_dispatch_flag",
                        "rts_flag",
                        "first_mile_type",
                        "service_level",
                        "service_type",

                        # Timestamp information
                        "pickup_datetime",
                        "nv_pickup_datetime",
                        "inbound_datetime",
                        "delivery_success_datetime",

                        # Cost information
                        "fm_cost",
                        "sort_cost",
                        "mm_cost",
                        "lm_cost",
                        "hub_cost",
                        "total_ops_cost",
                        "total_raw_events",
                        "fm_non_null_count",
                        "sort_non_null_count",
                        "mm_non_null_count",
                        "lm_non_null_count",
                        "hub_non_null_count",
                        "total_non_null_count",
                        "fm_null_count",
                        "sort_null_count",
                        "mm_null_count",
                        "lm_null_count",
                        "hub_null_count",
                        "null_cost_count",
                        "mm_intersort_cost",
                        "mm_pri_land_cost",
                        "mm_pri_air_cost",
                        "mm_pri_sea_cost",
                        "mm_sec_cost",
                        "mm_dd_cost",
                        "mm_intersort_non_null_count",
                        "mm_pri_land_non_null_count",
                        "mm_pri_air_non_null_count",
                        "mm_pri_sea_non_null_count",
                        "mm_sec_non_null_count",
                        "mm_dd_non_null_count",
                        "mm_intersort_null_count",
                        "mm_pri_land_null_count",
                        "mm_pri_air_null_count",
                        "mm_pri_sea_null_count",
                        "mm_sec_null_count",
                        "mm_dd_null_count",

                        # Pricing information
                        "billing_delivery_fee",
                        "cod_value",
                        "cod_collected",
                        "script_id",
                        "pricing_breakdown",
                        "pricer_rate_card_id",
                        "pricing_profile_id",
                        "billing_size",
                        "billing_weight",
                        "billing_weight_source",
                        "pricing_billable_weight",
                        "pricing_from_billing_zone",
                        "pricing_from_region",
                        "pricing_from_city",
                        "pricing_to_billing_zone",
                        "pricing_to_region",
                        "pricing_to_city",
                        "cod_fee",
                        "rts_fee",
                        "flat_discount",
                        "insurance_fee",
                        "handling_fee",
                        "pricing_created_at",

                        # Calculated revenue information
                        "calculated_delivery_fee_with_tax",
                        "calculated_delivery_fee_without_tax",

                        # OD hub information from TTR
                        "origin_hub_id",
                        "origin_hub_city",
                        "origin_hub_region",
                        "dest_hub_id",
                        "dest_hub_city",
                        "dest_hub_region",

                        # FM information
                        "fm_waypoint_id",
                        "fm_pudo_id",

                        # Utilization
                        "mm_sum_utilization_numerator",
                        "mm_sum_utilization_denominator",
                        "mm_avg_utilization_rate",

                        # Standardized display columns
                        "dashboard_fee",
                        "dashboard_origin_region",
                        "dashboard_dest_region",
                        "dashboard_flag",

                        # Country specific columns
                        "lazada_origin_hub_region",
                        "lazada_dest_hub_region",
                        "tiktok_origin_hub_region",
                        "tiktok_dest_hub_region",
                        "tiktok_xb_origin_hub_region",
                        "tiktok_xb_dest_hub_region",

                        "hub_fm_cost",
                        "hub_wh_cost",
                        "hub_lm_cost",

                        "hub_fm_non_null_count",
                        "hub_wh_non_null_count",
                        "hub_lm_non_null_count",

                        "hub_fm_null_count",
                        "hub_wh_null_count",
                        "hub_lm_null_count",
                    ],
                }
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                        select distinct * from pre_final
                        """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_PROFITS_PH,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()