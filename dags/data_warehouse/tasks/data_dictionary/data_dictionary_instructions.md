## Instructions

1. Create the data dictionary according to the format stated in `Example output` below, making sure to adhere to the `Rules for consistent output`
2. List all the fields in the table created by the code.
3. If the number of fields in the data dictionary does not match the number of fields in the table, edit the data dictionary to make sure the missing fields are included.


## Example output

```yaml
# Data dictionary

table_name: ...

overview:
  purpose: ...
  granularity: ...
  business_rules:
    - ...
    - ...
    
input_tables:
    - ...
    - ...

fields:
  - name: ...
    description: ...
    source: ...
    technical_description: ...

```

## Rules for consistent output

1. Table name: The table name should be the same as the input file name, excluding any extensions.

2. Overview section:
   - purpose: Summarize what this table does, what the user can use it for and the type of information included in it
   - granularity: Granularity of the table, e.g. "Each row represents orders for a single country on a single day."
   - business_rules: List the key business rules affecting data inclusion/exclusion. Remove any mention that it contains masked data.

3. Input Tables section:
   - list the table names that are used as inputs to this table in the form `schema_name.table_name` in snake-case e.g. data_warehouse.order_milestones, hub_prod_gl.shipments

4. Fields section:
   - Each field must have all four components (name, description, source, technical_description)
   - description: Use concise, business-friendly language
   - source format:
     - For direct fields: schema.table_name.field_name
     - For derived fields: list all source tables and fields used
   - technical_description: describe how this field is derived
     - For direct fields: "Direct field from source"
     - For derived fields: Must include the complete transformation logic
     - Use SQL-like syntax for clarity
     - Include any special conditions or filters
     - Enclose the whole line in double quotes

5. Formatting:
   - Use consistent indentation (2 spaces)
   - Use consistent line breaks
   - Use consistent field ordering
   - Use consistent terminology