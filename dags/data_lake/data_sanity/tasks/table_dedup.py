import logging
import sys
import json
from dataclasses import dataclass
from typing import List
from pyspark.sql import SparkSession, DataFrame
import pyspark.sql.functions as f
from abc import ABC, abstractmethod
from pyspark.sql import Window

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TableConfig:
    """Configuration for a table to be deduplicated"""
    name: str
    source_path: str
    partition_columns: List[str]
    primary_key: str = 'order_id'
    strategy: str = 'drop_duplicates'
    timestamp_column: str = None

class DeduplicationStrategy(ABC):
    """Abstract base class for deduplication strategies"""
    @abstractmethod
    def deduplicate(self, df: DataFrame) -> DataFrame:
        pass

class DropDuplicatesStrategy(DeduplicationStrategy):
    """Deduplication by dropping duplicates"""
    def __init__(self, key_columns: List[str]):
        self.key_columns = key_columns

    def deduplicate(self, df: DataFrame) -> DataFrame:
        return df.dropDuplicates(self.key_columns)

class KeepLatestStrategy(DeduplicationStrategy):
    """Deduplicate by keeping the latest record based on a timestamp"""
    def __init__(self, key_columns: List[str], timestamp_column: str):
        self.key_columns = key_columns
        self.timestamp_column = timestamp_column

    def deduplicate(self, df: DataFrame) -> DataFrame:
        window = Window.partitionBy(*self.key_columns).orderBy(f.desc(self.timestamp_column))
        return df.withColumn("rank", f.row_number().over(window)).filter(f.col("rank") == 1).drop("rank")

STRATEGY_MAP = {
    'drop_duplicates': DropDuplicatesStrategy,
    'keep_latest': KeepLatestStrategy,
}

class TableDeduplicator:
    """Handles table deduplication"""

    def __init__(self, spark: SparkSession):
        self.spark = spark

    def process_table(self, config: TableConfig) -> None:
        """Process a single table"""
        logger.info(f"Processing table: {config.name}")

        df = self.spark.read.parquet(config.source_path)
        total_records = df.count()
        logger.info(f"Total records in {config.name}: {total_records}")

        strategy_class = STRATEGY_MAP.get(config.strategy)
        if not strategy_class:
            raise ValueError(f"Unknown deduplication strategy: {config.strategy}")

        if config.strategy == 'drop_duplicates':
            strategy = strategy_class(key_columns=[config.primary_key])
        elif config.strategy == 'keep_latest':
            if not config.timestamp_column:
                raise ValueError("Timestamp column must be specified for 'keep_latest' strategy")
            strategy = strategy_class(
                key_columns=[config.primary_key],
                timestamp_column=config.timestamp_column
            )
        else:
            raise ValueError(f"Unsupported strategy: {config.strategy}")

        logger.info("Checking for duplicates before deduplication")
        duplicates_df = (
            df.groupBy(config.primary_key)
            .count()
            .filter(f.col("count") > 1)
        )
        duplicate_count = duplicates_df.count()

        if duplicate_count > 0:
            logger.warning(f"Found {duplicate_count} duplicates in {config.name} on '{config.primary_key}'")
            duplicates_df.show(n=5, truncate=False)
        else:
            logger.info(f"No duplicates found in {config.name} on '{config.primary_key}'")

        df_dedup = strategy.deduplicate(df)
        deduped_records = df_dedup.count()
        logger.info(f"Total records after deduplication in {config.name}: {deduped_records}")

        df_dedup.write \
            .partitionBy(*config.partition_columns) \
            .mode('overwrite') \
            .parquet(config.source_path)

        logger.info(f"Completed processing {config.name}")

def main():
    """Main execution function"""
    if len(sys.argv) != 2:
        logger.error("Usage: table_deduplication.py: table_config_json")
        sys.exit(1)

    config_json = sys.argv[1]
    config_dict = json.loads(config_json)
    table_config = TableConfig(**config_dict)

    spark = SparkSession.builder.getOrCreate()

    deduplicator = TableDeduplicator(spark=spark)

    try:
        deduplicator.process_table(table_config)
    except Exception as e:
        logger.error(f"Error processing table {table_config.name}: {str(e)}", exc_info=True)
        sys.exit(1)
    finally:
        spark.stop()

if __name__ == "__main__":
    main()