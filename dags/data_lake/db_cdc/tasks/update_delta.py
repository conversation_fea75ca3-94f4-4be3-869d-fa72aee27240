import ast
import codecs
import json
import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession

from common import date as date_util
from common import db
from common.spark import cdc, util
from common.utils.gcs import get_nv_data_bucket_uri

PARTITION_COLUMN = "created_month"


def get_cdc(spark, bucket, schema, table, date, time, primary_keys):
    df_cdc = spark.read.parquet(f"{bucket}/cdc_processed/{schema}/{table}/cdc_date={date}/cdc_time={time}")
    cdc_latest = cdc.get_latest_records(df_cdc, primary_keys)
    return cdc_latest


def has_new_cols(delta_df, cdc_df):
    """Return new columns that are present in cdc_df"""
    excluded_cols = {"nv_data_old"}
    diff = set(cdc_df.columns) - set(delta_df.columns)
    return bool(diff - excluded_cols)


def update_delta(spark, gs_bucket_raw, gs_bucket_path_db, schema, table, date, time, primary_keys, conn_dict):
    delta_table = DeltaTable.forPath(spark, f"{gs_bucket_path_db}/{schema}/{table}")
    cdc_latest = get_cdc(spark, gs_bucket_raw, schema, table, date, time, primary_keys)
    delta_df = delta_table.toDF()

    if has_new_cols(delta_df, cdc_latest) and conn_dict is not None:
        print("New columns detected. Proceed to get the dtype from jdbc connection to the DB")
        jdbc_dtypes = _get_jdbc_dtypes(conn_dict)

        # Note: new columns will be casted accordingly, but also columns with updated type as a side-effect.
        # But, that should cause no trouble to us.
        cdc_latest = util.cast_df(cdc_latest, jdbc_dtypes)

    partitions = _find_partitions_with_changes(cdc_latest)
    delta_merge_condition = _generate_merge_condition(primary_keys, partitions)

    (
        delta_table.alias("delta")
        .merge(cdc_latest.alias("cdc"), delta_merge_condition)
        .whenMatchedDelete(condition="cdc.nv_data_change_type = 'delete'")
        .whenMatchedUpdateAll(
            condition="""
                cdc.nv_data_ts > coalesce(delta.nv_data_ts, 0)
                OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid > delta.nv_data_xid)
                OR (cdc.nv_data_ts = delta.nv_data_ts AND cdc.nv_data_xid = delta.nv_data_xid
                    AND (cdc.nv_data_xoffset > delta.nv_data_xoffset OR cdc.nv_data_xoffset IS NULL))
                """
        )
        .whenNotMatchedInsertAll(condition="cdc.nv_data_change_type <> 'delete'")
        .execute()
    )


def create_delta(spark, gs_bucket_raw, gs_bucket_path_db, schema, table, date, time, primary_keys, conn_dict):
    cdc_latest = get_cdc(spark, gs_bucket_raw, schema, table, date, time, primary_keys)

    if conn_dict is not None:
        jdbc_dtypes = _get_jdbc_dtypes(conn_dict)
        cdc_latest = util.cast_df(cdc_latest, jdbc_dtypes)

    (
        cdc_latest.repartition(10, PARTITION_COLUMN)
        .write.mode("overwrite")
        .format("delta")
        .partitionBy(PARTITION_COLUMN)
        .save(f"{gs_bucket_path_db}/{schema}/{table}")
    )


def _get_jdbc_dtypes(conn_dict):
    jdbc_url, conn_props = db.get_spark_jdbc_config(**conn_dict, schema=schema)
    jdbc_df = spark.read.jdbc(url=jdbc_url, table=table, properties=conn_props)
    # Cast boolean columns to bigint as process_cdc_messages task infers these columns as int/string type,
    # which are implicitly incompatible with boolean type, causing Delta merge to fail.
    return [(column, "bigint" if dtype == "boolean" else dtype) for column, dtype in jdbc_df.dtypes]


def _find_partitions_with_changes(cdc_latest):
    partitions_list = [f"'{i[PARTITION_COLUMN]}'" for i in cdc_latest.select(PARTITION_COLUMN).distinct().collect()]
    partitions = ", ".join(partitions_list)
    return partitions


def _generate_merge_condition(primary_keys, partitions):
    join_keys = " and ".join([f"delta.{key} = cdc.{key}" for key in primary_keys])
    partition_pruning = f"delta.{PARTITION_COLUMN} in ({partitions})"
    merge_condition = f"{join_keys} and {partition_pruning}"
    return merge_condition


if __name__ == "__main__":
    (env, schema, table, ts, primary_keys_str, conn_dict_str, is_pii) = sys.argv[1:]

    if isinstance(is_pii, str):
        is_pii = ast.literal_eval(is_pii)

    primary_keys = json.loads(primary_keys_str.replace("'", '"'))

    if conn_dict_str != "None":
        conn_dict = json.loads(conn_dict_str.replace("'", '"'))
        password = codecs.decode(conn_dict["password"], "rot-13")
        conn_dict["password"] = password
    else:
        conn_dict = None

    date = date_util.date_from_ts(ts)
    time = date_util.time_from_ts(ts)

    spark = SparkSession.builder.getOrCreate()

    gs_bucket_input = get_nv_data_bucket_uri(env, bucket_type="processed", schema=schema, strip=False)
    gs_bucket_path_db = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=False)

    if DeltaTable.isDeltaTable(spark, f"{gs_bucket_path_db}/{schema}/{table}"):
        print(f"Delta table for {schema}.{table} exists. Update it.")
        update_delta(spark, gs_bucket_input, gs_bucket_path_db, schema, table, date, time, primary_keys, conn_dict)
    else:
        print(f"{schema}.{table} is new. Create a Delta table for it.")
        create_delta(spark, gs_bucket_input, gs_bucket_path_db, schema, table, date, time, primary_keys, conn_dict)

    spark.stop()
