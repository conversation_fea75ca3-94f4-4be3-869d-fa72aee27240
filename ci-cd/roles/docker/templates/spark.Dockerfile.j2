# This Spark base image is generated in JupyterHub repo.
FROM {{ spark_base_img }}

USER root

ENV AIRFLOW_HOME /opt/airflow
ENV PYTHONPATH $PYTHONPATH:$AIRFLOW_HOME/dags

# Install dependencies
# Install gcloud as root to a directory other users have access to
RUN apt-get update --allow-releaseinfo-change-suite -y \
    && apt-get install -y \
        python3-dev \
        curl \
    && pip3 install --upgrade pip

RUN cd /opt \
    && curl -O https://storage.googleapis.com/cloud-sdk-release/google-cloud-cli-450.0.0-linux-x86.tar.gz \
    && tar -xvzf google-cloud-cli-450.0.0-linux-x86.tar.gz \
    && ./google-cloud-sdk/install.sh \
    && export PATH="$PATH:/opt/google-cloud-sdk/bin/gcloud" \
    && rm -rf /var/lib/apt/lists/*

COPY keys/key.json /keys/key.json

COPY ./requirements-spark.txt requirements.txt
RUN pip3 install -r requirements.txt

COPY spark-defaults.conf $SPARK_HOME/conf/
COPY log4j.properties $SPARK_HOME/conf/
COPY log4j.properties $SPARK_HOME/nv/spark/conf/

COPY dags $AIRFLOW_HOME/dags
COPY plugins $AIRFLOW_HOME/plugins
COPY great_expectations great_expectations

# zip dags folder for use of spark UDF
RUN mkdir $AIRFLOW_HOME/files \
    && tar -cvzf $AIRFLOW_HOME/files/dags.zip $AIRFLOW_HOME/dags
USER spark

RUN gcloud auth activate-service-account --key-file /keys/key.json --project ninja-van-data
