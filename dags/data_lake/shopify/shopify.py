from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.stringcase import kebab_case
from metadata.constants import Email
from metadata.shopify import SHOPIFY_CONFIG
from shopify.operators.shopify_to_gcs_operator import ShopifyToGCSOperator

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
pii_tables = Variable.get("pii_fields_shopify", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "start_date": "2022-02-15",
    "email": [Email.DATA_ENGR],
    "email_on_failure": env == "prod",
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

spark_conf = {
    "spark.executor.instances": "1",
    "spark.executor.memory": "5g",
    "spark.driver.memory": "5g",
    "spark.sql.shuffle.partitions": "1",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
}

with DAG(
    dag_id="datalake_shopify",
    default_args=default_args,
    schedule_interval="0 16 * * *",  # Daily 00:00am SGT,
    concurrency=3,
    max_active_runs=1,
    catchup=False,
    user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
    tags=["data_lake"],
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in SHOPIFY_CONFIG.items():
        task_id = f"merge_delta_{entity}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                gcs_bucket,
                env,
                entity,
                "{{ds}}",
                f"""{{{{ var.json.pii_fields_shopify | extract('{entity}', {None}) }}}}""",
            ],
            conn_id="spark_default",
            conf=spark_conf,
        )
        merge_delta_unmasked = SparkSubmitOperator(
            task_id=task_id + "_unmasked",
            name=kebab_case(task_id + "_unmasked"),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                f"nv-data-{env}-datalake",
                env,
                entity,
                "{{ds}}",
                "[]",
            ],
            conn_id="spark_default",
            conf=spark_conf,
        )
        for system_id in config["system_ids"]:
            entity_config = {"system_id": system_id, "entity": entity, **config}
            entity_config.pop("system_ids")
            load_shopify_objects = ShopifyToGCSOperator(
                task_id=f"load_shopify_objects_{entity}_{system_id}",
                entity_config=entity_config,
                gcs_bucket=gcs_bucket + "-raw",
                gcs_folder_path=f"shopify/objects/{entity}",
                shopify_conn_id=f"shopify_{system_id}",
                gcs_conn_id="google_cloud_default",
            )
            load_shopify_objects >> [merge_delta, merge_delta_unmasked]
