import os
import pendulum
import logging
from datetime import timedelta, datetime
from airflow import DAG
from airflow.models import Variable
from config.config import Config
from common.airflow import notifications as notif
from config.database import DatabaseConfig

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 10, 5, 0, 0, 0),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "catchup": False,
    "pool": "cdc_pool",
    "on_failure_callback": (
        lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
    )
    if Config.ENV in ("prod", "dev")
    else None,
}

def choose_spark_operator(schema, table, **kwargs):
    use_spark_k8s = Config.SPARK_OPERATOR
    return f"process_cdc_messages_{schema}_{table}" if use_spark_k8s else f"process_cdc_messages_{schema}_{table}_v1"


def get_schema_schedule_category(schema, SCHEDULE_GROUPS):
    if schema in SCHEDULE_GROUPS["HIGH"]:
        return Config.WAIT_TIME_DYNAMIC.get("HIGH", 300)
    elif schema in SCHEDULE_GROUPS["MEDIUM"]:
        return Config.WAIT_TIME_DYNAMIC.get("MEDIUM", 300)
    else:
        return Config.WAIT_TIME_DYNAMIC.get("LOW", 300)

def create_dag(dag_id, schedule_interval=f"*/{Config.INTERVAL_DURATION} * * * *", default_args=default_args):
    dag = DAG(
        dag_id=dag_id,
        schedule_interval=schedule_interval,
        default_args=default_args,
        max_active_runs=1,
        concurrency=16,
        user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
        params={"alert_channel": Config.ALERT_CHANNEL},
        tags=["data_lake", "db_cdc", "db_cdc_delta"],
        dagrun_timeout=timedelta(hours=2),
    )
    globals()[dag_id] = dag
    return dag

def is_table_supported_for_cdc(schema, table):
    from common.airflow import db
    import logging
    from common.airflow import pk

    """
    Information_schema columns index mapping
    """
    _COLUMN_KEY = 16
    _COLUMN_NAME = 3
    _DATA_TYPE = 7
    try:
        fields = db.get_column_info(schema, table, DatabaseConfig.MYSQL_CONNECTIONS)
    except Exception:
        logging.info(f"Error when trying to describe table: {schema}.{table}")
        return False
    logging.info(f"Fields pulled: {fields}")
    primary_keys = []
    has_created_at = False
    for f in fields:
        if "COLUMN_NAME" in f:
            if f["COLUMN_KEY"] == "PRI":
                primary_keys.append(f["COLUMN_NAME"])
            elif f["COLUMN_NAME"] == "created_at" and f["DATA_TYPE"] in ("timestamp", "datetime"):
                has_created_at = True
        else:
            if f[_COLUMN_KEY] == "PRI":
                primary_keys.append(f[_COLUMN_NAME])
            if f[_COLUMN_NAME] == "created_at" and f[_DATA_TYPE] in ("timestamp", "datetime"):
                has_created_at = True

    if not (primary_keys and has_created_at):
        return False

    pk.manage_primary_keys_variable(schema, table, primary_keys)
    return True


def list_table_tasks(schema, **context):
    import logging
    from airflow.models import Variable

    tables_with_changes = []
    task_ids = f"{schema}.list_tables_with_changes"
    change_list = context["ti"].xcom_pull(dag_id=f"datalake_cdc_{schema}", task_ids=task_ids)
    logging.info(f'''List of DB tables with changes from datalake_cdc_{schema}: {change_list},
                pulled from :{{task_ids}}''')

    if change_list is None:
        logging.warning(f"No changes detected for schema {schema} or XCom pull failed")
        return []

    for table in change_list:
        if table not in DatabaseConfig.MYSQL_TABLES[schema]:
            logging.info(f"Table {schema}.{table} is not in mysql_tables")
            if not is_table_supported_for_cdc(schema, table):
                logging.info(f"Table {schema}.{table} is not supported for cdc")
                continue
            logging.info(f"Table {schema}.{table} is supported for cdc. Adding it to mysql_tables")
            if schema not in DatabaseConfig.MYSQL_TABLES:
                DatabaseConfig.MYSQL_TABLES[schema] = []
            DatabaseConfig.MYSQL_TABLES[schema].append(table)
            Variable.set("mysql_tables", DatabaseConfig.MYSQL_TABLES, serialize_json=True)
        tables_with_changes.append(f"choose_spark_operator_{schema}_{table}")
        
    if not tables_with_changes:
        logging.info(f"No tasks to run for schema {schema}")
        return [] 
    return tables_with_changes


# Mapping from Cron (0=Sunday) to Pendulum (0=Monday)
cron_to_pendulum_mapping = {
    0: 6,  # Sunday to 6
    1: 0,  # Monday to 0
    2: 1,  # Tuesday to 1
    3: 2,  # Wednesday to 2
    4: 3,  # Thursday to 3
    5: 4,  # Friday to 4
    6: 5   # Saturday to 5
}

def _get_path(connection, schema):
    from metadata.ticdc import TICDC_SCHEMAS

    """
    Check CDC message stream based on connection and schema
    """
    return "ticdc_stream" if (schema in TICDC_SCHEMAS[Config.ENV] or connection == "ninjamart") else "cdc_stream"


def get_last_compaction_date(schema_name: str, execution_date: pendulum.DateTime):
    from common.date import get_schema_day
    schema_day = get_schema_day(schema_name)
    dt = execution_date.subtract(weeks=1).add(minutes=Config.INTERVAL_DURATION)
    return dt.start_of("day") if dt.day_of_week == cron_to_pendulum_mapping.get(schema_day) else dt.previous(cron_to_pendulum_mapping.get(schema_day))



def get_last_scheduled_execution_date(execution_date):
    """Allows sensor to find the last run even though DAG is triggered out of schedule, such as when running E2E tests,
    e.g., sensor with execution_date = '2020-01-01T11:00:00.500' will find '2020-01-01T10:00:00.000' run
    """
    return execution_date.replace(minute=0, second=0, microsecond=0).subtract(minutes=Config.INTERVAL_DURATION)


def extract_pii_fields_from_var(schema, table):
    """
    Extracts PII fields from the Airflow variable.

    Args:
        schema (str): The schema name.
        table (str): The table name.

    Returns:
        str: String representation of the list of PII fields for the specified schema and table.
    """
    try:
        pii_fields_var = Variable.get("pii_fields", deserialize_json=True)
        
        if schema in pii_fields_var and table in pii_fields_var[schema]:
            print(pii_fields_var[schema][table])
            return str(pii_fields_var[schema][table])
        else:
            return '[]'
    except Exception as e:
        logging.error(f"Error extracting PII fields: {e}")
        return '[]'
    
def list_changes(bucket_name, schema, path, execution_date, **kwargs):
    from airflow.providers.google.cloud.hooks.gcs import GCSHook
    from datetime import datetime, timedelta
    import logging

    """Creates a list of tables with changes per DB."""
    delimiter = "/part"
    tables_with_changes = []
    blob_list = []
    hook = GCSHook()

    if schema.startswith("ninjamart"):
        _schema = schema.replace("ninjamart_", "")
    else:
        _schema = schema

    for i in range(Config.NUM_INTERVALS):
        ts = execution_date + timedelta(minutes=i * 15)
        prefix = f"{path}/data/date={ts:%Y-%m-%d}/time={ts:%H-%M-%S}/database={_schema}"
        logging.info(f"Listing changes in '{bucket_name}/{prefix}'")
        # Note: the delimiter param is used so that the list_blobs returns results in a directory-like mode.
        #  See https://cloud.google.com/storage/docs/json_api/v1/objects/list
        blobs = hook.list(bucket_name=bucket_name, prefix=prefix, delimiter=delimiter)
        blob_list.extend(blobs)

    for blob in blob_list:
        if "table" in blob:
            table = blob.split("/")[-2].split("=")[-1]
            if table not in tables_with_changes:
                tables_with_changes.append(table)

    return tables_with_changes
