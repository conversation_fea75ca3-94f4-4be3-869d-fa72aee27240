import requests
from airflow.hooks.base import BaseHook


class <PERSON><PERSON>Hook(BaseHook):
    """
    Eber hook to fetch data with the Eber REST API (https://api.eber.co/api/v3/home). Depending on
    the entity, either incremental or full data is returned (e.g. full data for users and incremental for transactions).

    :param conn_id: Eber connection ID
    :type conn_id:  string
    """

    def __init__(self, conn_id="eber_default"):
        self.conn = self.get_connection(conn_id)
        extras = self.conn.extra_dejson
        required_keys = {"access_token", "business_id"}
        if not required_keys <= extras.keys():
            raise ValueError(f"Missing configuration in Eber connection: {required_keys}")
        self.access_token = extras.get("access_token")
        self.business_id = extras.get("business_id")
        self.max_retries = 2

    def get_data(self, path_fmt, query_params=None):
        """
        Fetches entity data with the specified relative url path and query parameters.

        :param path_fmt:        relative URL path format (with business ID as a parameter)
        :param query_params:    request query parameters
        :return:                Eber entity data
        """
        results = []
        path = path_fmt.format(business_id=self.business_id)
        url = f"{self.conn.host}/{path}"
        headers = {"Authorization": f"Basic {self.access_token}"}
        data, next_url = self._get_with_retry(url, headers=headers, params=query_params)
        results.extend(data)
        while data and next_url:
            print(f"next url: {next_url}")
            data, next_url = self._get_with_retry(next_url, headers=headers)
            results.extend(data)
        return results

    def _get_with_retry(self, url, headers=None, params=None):
        """
        Fetches entity data with the specified url, headers and query parameters.
        Retries up to ``self.max_retries`` times if the request is unsuccessful.

        :param url:     request URL (e.g. https://api.eber.co/...)
        :param headers: request headers (optional)
        :param params:  request query parameters (optional)
        :return:        tuple of response data and next URL (for pagination)
        :raises:        runtime error if request fails after ``self.max_retries`` consecutive retries
        """
        rem_retries = self.max_retries
        resp = requests.get(url, headers=headers, params=params)
        while resp.status_code != 200:
            if rem_retries == 0:
                raise RuntimeError(f"Error fetching Eber data: {resp.text}")
            rem_retries -= 1
            resp = requests.get(url, headers=headers, params=params)
        resp_json = resp.json()
        data = resp_json.get("data", [])
        next_url = resp_json.get("next_url")
        return data, next_url
