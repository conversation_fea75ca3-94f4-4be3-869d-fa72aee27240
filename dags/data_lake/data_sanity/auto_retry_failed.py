import os
import logging
import json
import airflow_client.client
from airflow_client.client.api import dag_api
from airflow_client.client.model.error import Error
from datetime import timedelta
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator
from airflow.providers.postgres.hooks.postgres import Postg<PERSON>Hook
from airflow_client.client.model.clear_task_instances import ClearTaskInstances

env = Variable.get("env")
airflow_creds = json.loads(Variable.get("airflow_creds_secret"))

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'start_date': datetime(2024, 3, 7, 0, 0, 0),
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'auto_clear_failed_tasks',
    default_args=default_args,
    description='A DAG to auto-clear failed tasks',
    schedule_interval='@hourly',
    catchup=False,
)

def query_metastore(**context):
    postgres_conn_id = 'airflow_metastore_db' if env == 'prod' else 'airflow_metastore_db_dev'
    postgres_hook = PostgresHook(postgres_conn_id=postgres_conn_id)
    execution_date = context['execution_date'].strftime('%Y-%m-%dT%H:%M:%S')
    print(execution_date)
    last_execution_date = (context['execution_date'] - timedelta(hours=2)).strftime('%Y-%m-%dT%H:%M:%S')
    print(last_execution_date)
    sql_query = f"""select dag_id, task_id, run_id from task_fail
                    where task_id ~ '^(pii_delta|process)' and
                    SUBSTRING(run_id, 12, 19) between '{last_execution_date}' and '{execution_date}'
                    ORDER BY run_id DESC"""
    
    print(sql_query)

    results = postgres_hook.get_records(sql_query)
    print(len(results))
    if results:
        context['ti'].xcom_push(key='results', value=results)
    else:
        print("No matching records found")

query_task = PythonOperator(
    task_id='query_airflow_metastore',
    python_callable=query_metastore,
    dag=dag
)

def clear_tasks_from_xcom(**context):
    host = "https://airflow.ninjavan.co/api/v1" if env == 'prod' else "https://airflow-dev.ninjavan.co/api/v1"
    api_configuration = airflow_client.client.Configuration(
        host=host,
        username=airflow_creds['username'],
        password=airflow_creds['password']
    )

    with airflow_client.client.ApiClient(api_configuration) as api_client:
        dag_api_instance = dag_api.DAGApi(api_client)
        xcom_results = context['ti'].xcom_pull(task_ids='query_airflow_metastore', key='results')

        if xcom_results:
            for result in xcom_results:
                dag_id = result[0]
                print(result)
                execution_date = result[2].replace('scheduled__', '')
                print(execution_date)

                clear_task_instances = ClearTaskInstances(
                    dry_run=False,
                    task_ids=[result[1]],
                    start_date=execution_date,
                    end_date=(datetime.strptime(execution_date, '%Y-%m-%dT%H:%M:%S%z') + timedelta(hours=0.5)).strftime('%Y-%m-%dT%H:%M:%S%z'),  # Convert datetime to string
                    only_failed=False,
                    include_downstream=True,
                    reset_dag_runs=True,
                )
                api_response = dag_api_instance.post_clear_task_instances(dag_id, clear_task_instances)
                print(api_response)
        else:
            print("No XCom results found")

clear_task = PythonOperator(
    task_id='clear_tasks_from_xcom',
    python_callable=clear_tasks_from_xcom,
    dag=dag
)

query_task >> clear_task