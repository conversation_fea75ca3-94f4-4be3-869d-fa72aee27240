import sys

from delta import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, date_format

NV_UPDATED_DATE = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"
ID = "id"
SYSTEM_ID = "system_id"
CREATED_AT = "created_at"


def get_delta_path(gcs_bucket, entity):
    return f"gs://{gcs_bucket}/jotform/delta/{entity}"


def get_obj_path(gcs_bucket, entity):
    return f"gs://{gcs_bucket}/jotform/objects/{entity}"


def get_object(spark, path, date):
    month_fmt = "yyyy-MM"
    return (
        spark.read.parquet(path)
        .where(col(NV_UPDATED_DATE) == date)
        .drop(NV_UPDATED_DATE)
        .withColumn(NV_CREATED_MONTH, date_format(CREATED_AT, month_fmt))
    )


def create_delta(df, path):
    df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)


def merge_delta(spark, df, path):
    delta_table = DeltaTable.forPath(spark, path)

    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH]) for row in df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")

    (
        delta_table.alias("delta")
        .merge(
            df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) AND delta.{ID} = cdc.{ID}",
        )
        .whenMatchedUpdateAll(condition="cdc.updated_at > delta.updated_at")
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_or_create_delta(spark, gcs_bucket, entity, date):
    obj_path = get_obj_path(gcs_bucket, entity)
    obj_df = get_object(spark, obj_path, date)

    if obj_df.count() == 0:
        print("No records to update")
        return

    delta_path = get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge_delta(spark, obj_df, delta_path)
    else:
        create_delta(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, entity, date = sys.argv[1:]
    spark = SparkSession.builder.getOrCreate()
    merge_or_create_delta(spark, gcs_bucket, entity, date)
    spark.stop()
