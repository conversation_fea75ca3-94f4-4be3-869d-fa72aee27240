import json
import logging

import pandas as pd
from airflow.models import Base<PERSON>perator, SkipMixin
from airflow.providers.google.cloud.hooks.gcs import GCSHook

from data_warehouse.utils.gcs import get_uri_bucket_and_directory
from gsuite.hooks.gsuite_hook import GsuiteHook

MAX_RESULTS_LIMIT = 100


class GsuiteToGCSOperator(BaseOperator, SkipMixin):
    """
    G-suite to GCS Operator

    Fetches data from G-suite (https://api.ninjavan.co/global/g-suite) and stores it in Google Cloud Storage (GCS).

    Data will be stored in the path {gcs_bucket}/{gcs_folder_path}/nv_updated_date={date}

    :param entity_config:           G-suite entity config to fetch data (e.g. users)
    :type entity_config:            dict
    :param gcs_bucket:              GCS bucket to upload data file to
    :type gcs_bucket:               string
    :param gcs_folder_path:         GCS folder path
    :type gcs_folder_path:          string
    :param gsuite_conn_id:          G-suite connection ID
    :type gsuite_conn_id:           string
    :param dwh_input_path:          Data warehouse input table path
    :type dwh_input_path:           string
    :param aaa_auth_token_task_id:  Task ID for fetching access token for G-suite API
    :type aaa_auth_token_task_id:   string
    :param gcs_conn_id:             GCS connection ID
    :type gcs_conn_id:              string
    """

    EXECUTION_DATE_FORMAT = "%Y-%m-%d %H-%M-%S"
    PARTITION_COLUMN = "nv_updated_date"
    CREATED_AT = "created_at"
    MASKED = "_masked"

    def __init__(
        self,
        entity_config,
        gcs_bucket,
        gcs_folder_path,
        dwh_input_path,
        aaa_auth_token_task_id,
        gsuite_conn_id="gsuite_default",
        gcs_conn_id="google_cloud_default",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity_config = entity_config
        self.gcs_bucket = gcs_bucket
        self.gcs_folder_path = gcs_folder_path
        self.dwh_input_path = dwh_input_path
        self.aaa_auth_token_task_id = aaa_auth_token_task_id
        self.gsuite_conn_id = gsuite_conn_id
        self.gcs_conn_id = gcs_conn_id

    def _get_dwh_table(self, execution_date):
        """
        Read in data warehouse table from Google Cloud Storage(GCS) as pandas Dataframe

        :param execution_date: Datetime when task runs
        :return:               Pandas Dataframe
        """
        measurement_datetime_partition = f"/measurement_datetime={execution_date.strftime(self.EXECUTION_DATE_FORMAT)}"
        dwh_table_path = self.dwh_input_path.rstrip(self.MASKED) + measurement_datetime_partition


        gcs_hook = GCSHook(gcp_conn_id=self.gcs_conn_id)
        bucket, directory = get_uri_bucket_and_directory(dwh_table_path)
        parquet_file_directories = gcs_hook.list(bucket_name=bucket, prefix=directory, delimiter=".parquet")
        dfs = []
        for file_directory in parquet_file_directories:
            dfs.append(pd.read_parquet(f"gs://{bucket}/{file_directory}"))
        return pd.concat(dfs, ignore_index=True)

    def _get_response_from_gsuite(self, df, primary_column, access_token):
        """
        Call G-suite API as internal services using G-suite Hook, schematize response and return as a Pandas Dataframe

        :param df:              DWH data frame to extract values to be searched
        :param primary_column:  Primary key to search
        :param access_token:    Access token for G-suite API
        :return:                Pandas Dataframe
        """
        entity = self.entity_config["entity"]
        method = self.entity_config["method"]
        data_key = self.entity_config["data_key"]
        gsuite_hook = GsuiteHook(self.gsuite_conn_id)
        primary_keys = df[primary_column].to_list()

        endpoint_path = f"{entity}/{method}"
        logging.info(f"Fetching data from {endpoint_path}...")
        gsuite_response = []
        while primary_keys:
            data = json.dumps({data_key: primary_keys[:MAX_RESULTS_LIMIT]})
            del primary_keys[:MAX_RESULTS_LIMIT]
            gsuite_response.extend(gsuite_hook.get_data(access_token, endpoint_path, data=data))

        if not gsuite_response:
            logging.info("No records retrieved")
            return

        df = pd.DataFrame(gsuite_response)

        if df.empty:
            return
        logging.info(f"No. of records: {len(df.index)}")

        schema = self.entity_config.get("schema", {})
        df = df[schema.keys()].astype(schema)
        return df

    def _write_results(self, df):
        """
        Write results to GCS using pandas

        :param df: object data frame to be stored
        :return:   None
        """

        updated_at = self.entity_config["updated_at_field"]
        df[self.PARTITION_COLUMN] = df[updated_at].dt.date
        created_at_field = self.entity_config["created_at_field"]
        df[self.CREATED_AT] = df[created_at_field].dt.date
        dir_name = f"gs://{self.gcs_bucket}/{self.gcs_folder_path}"
        df.to_parquet(
            dir_name, compression="snappy", engine="pyarrow", index=False, partition_cols=[self.PARTITION_COLUMN]
        )

    def execute(self, context):
        """
        Fetches gsuite entity data and stores it in GCS in parquet format.

        :param context: Task context
        """
        entity = self.entity_config["entity"]
        logging.info(f"Fetching gsuite {entity} data")

        logging.info(f"Loading input data warehouse table: {self.dwh_input_path.split('/')[-1]}...")
        execution_date = context["execution_date"]
        dwh_df = self._get_dwh_table(execution_date.add(days=1))
        logging.info("Loaded data warehouse table")

        logging.info("Fetching data from G-suite API")
        primary_column = self.entity_config["dwh_key_column"]
        access_token = context["ti"].xcom_pull(task_ids=self.aaa_auth_token_task_id)
        response_df = self._get_response_from_gsuite(dwh_df, primary_column, access_token)
        logging.info("Fetched data from G-suite API")

        logging.info("Merging DWH data with API response")
        response_key_column = self.entity_config["response_key_column"]
        combined_df = dwh_df.merge(response_df, how="left", left_on=primary_column, right_on=response_key_column)
        combined_df = combined_df.drop(response_key_column, 1)
        logging.info("Merged DWH data with API response")

        logging.info("Uploading to GCS bucket")
        self._write_results(combined_df)
        logging.info("Uploaded gsuite data to GCS")
