import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import date_format

from common.spark import pii
from metadata.constants import LegacyGcsBucketUris

PRIMARY_KEY = "id"
SRC_PARTITION_COLUMN = "nv_updated_date"
DST_PARTITION_COLUMN = "nv_created_month"
OBJECT_BASE_PATH = "salesforce/sales_cloud/objects"
DELTA_BASE_PATH = "salesforce/sales_cloud/delta"
OBJECT_BUCKET = LegacyGcsBucketUris.raw


def merge_delta(spark, gs_bucket, obj_df, obj_name, created_at_field, updated_at_field, refresh_data):
    delta_table = DeltaTable.forPath(spark, f"gs://{gs_bucket}/{DELTA_BASE_PATH}/{obj_name}")
    print("Delta schema:")
    delta_table.toDF().printSchema()

    update_operator = ">=" if refresh_data else ">"
    merge_str = f"delta.{PRIMARY_KEY} = cdc.{PRIMARY_KEY}"
    # add partition filters if defined
    if created_at_field:
        obj_df = obj_df.withColumn(DST_PARTITION_COLUMN, date_format(created_at_field, "yyyy-MM"))
        partitions = [i[DST_PARTITION_COLUMN] for i in obj_df.select(DST_PARTITION_COLUMN).distinct().collect()]
        partitions_list = ",".join(repr(p) for p in partitions)
        merge_str = f"{merge_str} AND delta.{DST_PARTITION_COLUMN} IN ({partitions_list})"

    print("Proceed to merge updates to delta table")
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            merge_str,
        )
        .whenMatchedUpdateAll(condition=f"cdc.{updated_at_field} {update_operator} delta.{updated_at_field}")
        .whenNotMatchedInsertAll()
        .execute()
    )


def create_delta(gs_bucket, obj_df, obj_name, created_at_field):
    path = f"gs://{gs_bucket}/{DELTA_BASE_PATH}/{obj_name}"
    # add partition if defined
    if created_at_field:
        obj_df = obj_df.withColumn(DST_PARTITION_COLUMN, date_format(created_at_field, "yyyy-MM"))
        obj_df.write.mode("overwrite").format("delta").partitionBy(DST_PARTITION_COLUMN).save(path)
    else:
        obj_df.write.mode("overwrite").format("delta").save(path)


def get_salesforce_object(spark, env, obj_name, date, pii_fields=None):
    df = spark.read.parquet(f"{OBJECT_BUCKET.format(env)}/{OBJECT_BASE_PATH}/{obj_name}/{SRC_PARTITION_COLUMN}={date}")
    if pii_fields:
        df = pii.mask(spark, df, pii_fields)
    print(f"{obj_name} schema read:")
    df.printSchema()
    return df


if __name__ == "__main__":
    gs_bucket, env, obj_name, created_at_field, updated_at_field, date, refresh_data_str, pii_str = sys.argv[1:]
    refresh_data = refresh_data_str == "True"
    pii_fields = [
        f.strip() for f in pii_str[(pii_str.index("[") + 1) : (pii_str.index("]"))].replace("'", "").split(",")
    ]
    spark = SparkSession.builder.getOrCreate()

    obj_df = get_salesforce_object(spark, env, obj_name, date, pii_fields)

    if DeltaTable.isDeltaTable(spark, f"gs://{gs_bucket}/{DELTA_BASE_PATH}/{obj_name}"):
        print(f"Delta table for {obj_name} exists. Update it.")
        merge_delta(spark, gs_bucket, obj_df, obj_name, created_at_field, updated_at_field, refresh_data)
    else:
        print(f"{obj_name} is new. Create a Delta table for it.")
        create_delta(gs_bucket, obj_df, obj_name, created_at_field)

    print(f"Spark job is done. Delta table for {obj_name} has been created/updated.")
    spark.stop()
