import os
import json
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.hooks.base import BaseHook
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.stringcase import kebab_case
from metadata.constants import Timeout


tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")

# TiDB connection ID in Airflow
tidb_conn_id = "aaa_tidb"

# Get connection details
tidb_conn = BaseHook.get_connection(tidb_conn_id)

# Get CA certificate from connection extras
ca_cert_content = ""
if tidb_conn.extra_dejson:
    ca_cert_content = tidb_conn.extra_dejson.get("ca_cert", "")

# Create a dictionary with connection details
db_config = {
    "host": tidb_conn.host,
    "port": tidb_conn.port or 4000,
    "schema": tidb_conn.schema,
    "user": tidb_conn.login,
    "password": tidb_conn.password,
    "driver": "com.mysql.jdbc.Driver",
    "ca_cert": ca_cert_content
}

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2025, 5, 15),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

delta_base_path = f"gs://nv-data-{env}-data-lake/db/aaa_views/delta/"

aaa_views = [
    "view_all_app_users",
    "view_all_users_with_roles",
    "view_inactive_drivers_30_days",
    "view_inactive_users_30_days",
    "view_roles_by_system_id",
    "view_roles_with_scope",
    "view_user_role_added_at",
    "view_user_role_deleted_at",
    "view_user_roles_by_system_id",
    "view_users_with_role",
    "view_users_with_scope"
]

spark_conf = {
    "spark.executor.instances": "2",
    "spark.executor.memory": "8g",
    "spark.driver.memory": "4g",
    "spark.sql.shuffle.partitions": "10",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
}

# DAG definition
with DAG(
    dag_id="aaa_views_sync_to_delta_lake",
    default_args=default_args,
    schedule_interval="@daily", 
    catchup=False,
    tags=["data_lake", "tidb", "aaa"],
) as dag:
    
    task_list = []
    
    for view_name in aaa_views:
        task_id = f"sync_tidb_to_delta_{view_name}"
        
        sync_task = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                env,
                view_name,
                "{{ ds }}", 
                delta_base_path,
                json.dumps(db_config),
            ],
            conn_id="spark_default",
            conf=spark_conf,
            execution_timeout=Timeout.ONE_HOUR, 
        )
        
        task_list.append(sync_task) 