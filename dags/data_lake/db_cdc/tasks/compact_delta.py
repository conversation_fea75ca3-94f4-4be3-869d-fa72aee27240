import ast
import json
import math
import pprint
import sys
import time
from collections import defaultdict

from delta.exceptions import ConcurrentDeleteReadException, ProtocolChangedException
from pyspark.sql import SparkSession

from common import gsutil, hive_metastore
from common.spark import config as spark_conf_util
from common.utils.gcs import get_nv_data_bucket_uri

MERGE_PREDICATE_REGEX = r"(?<=`created_month` IN \()(.*)(?=\)\)\))"
SNAPPY_PARQUET_COMPRESSION_RATIO = 6  # Based on observation, it's around 4-5.
PARTITION_COLUMN = "created_month"
MAX_RETRIES = 3
INITIAL_RETRY_DELAY = 500  # seconds

def construct_predicate(partitions):
    """
    :param partitions:  Set of changed partitions
    :return:            String with created_month in unique list of changed partitions
    """
    if partitions[0] is None:
        return f"{PARTITION_COLUMN} is null"
    partitions_str = ",".join([repr(p) for p in partitions])
    predicate_str = f"{PARTITION_COLUMN} in ({partitions_str})"
    return predicate_str


def get_part_to_files(spark, path):
    """Read the manifest files of a delta table and output the partition to files mapping"""
    files_df = spark.read.text(f"{path}/_symlink_format_manifest/")
    part_to_files = defaultdict(list)
    for row in files_df.collect():
        part_to_files[row.created_month].append(row.value)
    # print("Partition -> files mapping:")
    # pprint.pprint(part_to_files)
    return part_to_files


def get_num_files_to_parts(part_to_files):
    """Takes in the partition to files mapping and output the number of files to partition mapping"""
    num_files_to_partitions = defaultdict(list)
    for month, files in part_to_files.items():
        partition_size_in_disk = gsutil.get_aggregate_size(files)
        partition_size_in_mem = partition_size_in_disk * SNAPPY_PARQUET_COMPRESSION_RATIO
        num_files = math.ceil(partition_size_in_mem / 1e9)  # each file should take around 1GB in mem, but 200MB in du
        if len(files) <= num_files:
            print(f"Partition {month} has {len(files)} files, <= the optimum {num_files}. No repartitioning needed")
            continue
        print(f"Partition {month} has {len(files)} files, >= the optimum {num_files}. Repartitioning needed")
        num_files_to_partitions[num_files].append((month, partition_size_in_mem))
    print("Num files -> partitions mapping:")
    pprint.pprint(num_files_to_partitions)
    return num_files_to_partitions


def split_to_chunks(mem_available, partitions):
    """Split partitions into chunks that can be processed together

    :param mem_available:       memory available
    :param partitions:          list of partitions with mem size, eg. [("2020-05", 6e9), ("2020-04, 4.5e9), ...]
    :return:                    chunks of partitions, eg. [["2020-05"], ["2020-04"], ["2020-03", "2020-02", "2020-01"]]
    """
    chunks, chunk = [], []
    mem_occupied = 0
    for month, size_in_mem in partitions:
        if month is None:  # to handle NULL partition
            chunks.append([month])
            continue

        if mem_occupied + size_in_mem >= mem_available and len(chunk) > 0:
            chunks.append(chunk)
            chunk = []
            mem_occupied = 0

        chunk.append(month)
        mem_occupied += size_in_mem

    if len(chunk) > 0:
        chunks.append(chunk)

    return chunks


def compact_delta_table(spark, gs_path, schema, table, num_executors, mem_per_executor):
    """
    Compacts a Delta table by repartitioning it to a smaller number of files.

    :param spark:       Spark session
    :param gs_path:     Data lake GCS bucket path
                        e.g., gs://nv-data-prod-datalake/delta, gs://nv-data-prod-data-lake/db
    :param schema:      DB schema
    :param table:       DB table
    :return:
    """
    path = f"{gs_path}/{schema}/{table}"

    part_to_files = get_part_to_files(spark, path)
    num_files_to_parts = get_num_files_to_parts(part_to_files)

    for num_files, partitions in num_files_to_parts.items():
        mem_available = min(num_files, num_executors) * mem_per_executor
        chunks = split_to_chunks(mem_available, partitions)
        print(f"Going to execute the repartitioning for chunks {chunks} into {num_files} number of files each")
        for chunk in chunks:
            predicate = construct_predicate(chunk)
            print(f"Repartitioning partitions where {predicate} to {num_files} number of files")
            (
                spark.read.format("delta")
                .load(path)
                .where(predicate)
                .repartition(num_files)
                .write
                .option("dataChange", "false")
                .format("delta")
                .mode("overwrite")
                .option("replaceWhere", predicate)
                .save(path)
            )

    hive_metastore.generate_delta_manifest(spark, path)


def compact_delta_table_with_retry(spark, gs_path, schema, table, num_executors, mem_per_executor):
    """
    Test version of compact_delta_table with retry logic for handling concurrent operations.
    """
    path = f"{gs_path}/{schema}/{table}"

    part_to_files = get_part_to_files(spark, path)
    num_files_to_parts = get_num_files_to_parts(part_to_files)

    for num_files, partitions in num_files_to_parts.items():
        mem_available = min(num_files, num_executors) * mem_per_executor
        chunks = split_to_chunks(mem_available, partitions)
        print(f"Going to execute the repartitioning for chunks {chunks} into {num_files} number of files each")
        for chunk in chunks:
            retry_count = 0
            while retry_count < MAX_RETRIES:
                try:
                    predicate = construct_predicate(chunk)
                    print(f"Repartitioning partitions where {predicate} to {num_files} number of files")
                    print(f"Attempt {retry_count + 1} of {MAX_RETRIES}")
                    
                    (
                        spark.read.format("delta")
                        .load(path)
                        .where(predicate)
                        .repartition(num_files)
                        .write
                        .option("dataChange", "false")
                        .format("delta")
                        .mode("overwrite")
                        .option("replaceWhere", predicate)
                        .option("isolationLevel", "Serializable")  # Stronger isolation level
                        .save(path)
                    )
                    break  # Success, exit retry loop
                    
                except (ConcurrentDeleteReadException, ProtocolChangedException) as e:
                    retry_count += 1
                    if retry_count == MAX_RETRIES:
                        print(f"Failed to compact chunk after {MAX_RETRIES} attempts. Error: {str(e)}")
                        raise e
                    
                    # Exponential backoff
                    delay = INITIAL_RETRY_DELAY * (2 ** (retry_count - 1))
                    print(f"Encountered concurrent modification, retrying in {delay} seconds...")
                    time.sleep(delay)
                    
                except Exception as e:
                    print(f"Unexpected error during compaction: {str(e)}")
                    raise e

    hive_metastore.generate_delta_manifest(spark, path)


if __name__ == "__main__":
    env, schema, table, spark_conf_str = sys.argv[1:]
    spark = SparkSession.builder.getOrCreate()
    gs_path = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=False)

    print(f"Run compaction for Delta table {schema}.{table} in {gs_path}")

    spark_conf = json.loads(spark_conf_str)
    num_executors = int(spark_conf["spark.executor.instances"])
    mem_per_executor = spark_conf_util.bytes_spec_to_num_bytes(spark_conf["spark.executor.memory"])
    
    # Check if this is the test schema/table that should use retry logic
    use_retry_logic = spark_conf.get('use_retry_logic', False)
    if use_retry_logic:
        print(f"Using retry logic for table: {schema}.{table}")
        compact_delta_table_with_retry(spark, gs_path, schema, table, num_executors, mem_per_executor)
    else:
        compact_delta_table(spark, gs_path, schema, table, num_executors, mem_per_executor)
    
    spark.stop()
