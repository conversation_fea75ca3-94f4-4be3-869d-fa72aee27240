import requests
from bs4 import BeautifulSoup
import time
from typing import List, Dict
import logging
import csv
from dataclasses import dataclass
from datetime import datetime
import sys

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DagProcessingResult:
    dag_id: str
    status: str  # 'SUCCESS', 'FAILED', 'NOT_FOUND'
    total_runs_processed: int = 0
    failed_runs: int = 0
    error_message: str = ''
    processed_at: str = datetime.now().isoformat()

class AirflowDagCleaner:
    def __init__(self, base_url: str, session_id: str):
        self.base_url = base_url
        self.session_id = session_id
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1'
        }
        self.cookies = {
            'session': session_id
        }

    def _get_curl_command(self, method: str, url: str, data: dict = None) -> str:
        """Convert request to curl command for debugging"""
        curl_command = f"curl -X {method} '{url}'"
        
        # Add headers
        for header, value in self.headers.items():
            curl_command += f" \\\n  -H '{header}: {value}'"
        
        # Add cookie
        curl_command += f" \\\n  -b 'session={self.session_id}'"
        
        # Add data if POST request
        if data:
            data_args = ' '.join([f"-F '{k}={v}'" for k, v in data.items()])
            curl_command += f" \\\n  {data_args}"
        
        return curl_command

    def get_row_ids_from_page(self, page: int = None, dag_id: str = None) -> tuple[List[str], bool, str]:
        """Extract row IDs from a specific page and return csrf token"""
        # Use instance dag_id if not provided
        dag_id = dag_id or self.dag_id
        
        # Build URL - only add page parameter if specified
        url = f"{self.base_url}/dagrun/list/?_flt_0_state=failed&_flt_2_dag_id={dag_id}"
        if page is not None:
            url += f"&page_DagRunModelView={page}"
        
        # Log curl command
        logger.info(f"Executing request:\n{self._get_curl_command('GET', url)}")
        
        response = requests.get(url, headers=self.headers, cookies=self.cookies)
        if response.status_code != 200:
            logger.error(f"Failed to fetch {'page ' + str(page) if page else 'DAG runs'}. Status code: {response.status_code}")
            return [], False, None

        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract CSRF token
        csrf_token = None
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
        
        # Find all input elements with class="action_check" and name="rowid"
        checkboxes = soup.find_all('input', {
            'class': 'action_check',
            'name': 'rowid',
            'type': 'checkbox'
        })
        
        # Extract the value attribute from each checkbox
        row_ids = [checkbox.get('value') for checkbox in checkboxes if checkbox.get('value')]
        
        # Consider there are more pages if we got any results on this page
        has_next = bool(row_ids)
        
        return row_ids, has_next, csrf_token

    def clear_dag_runs(self, row_ids: List[str], csrf_token: str, retry_with_smaller_batch: bool = True) -> bool:
        """Clear the specified DAG runs"""
        if not row_ids:
            return True

        # Log the row IDs being cleared
        logger.info(f"Clearing row IDs: {', '.join(row_ids)}")

        clear_url = f"{self.base_url}/dagrun/action_post"
        
        # Prepare form data with CSRF token and multiple rowids
        data = {
            'csrf_token': csrf_token,
            'action': 'clear'
        }
        # Add multiple rowid entries as a list
        data['rowid'] = row_ids
        
        headers = {
            **self.headers,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': self.base_url,
            'Referer': f"{self.base_url}/dagrun/list/?_flt_0_state=failed&_flt_2_dag_id={self.dag_id}"
        }
        
        # Log curl command
        logger.info(f"Executing request:\n{self._get_curl_command('POST', clear_url, data)}")
        
        response = requests.post(
            clear_url,
            headers=headers,
            cookies=self.cookies,
            data=data
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to clear DAG runs. Status code: {response.status_code}")
            
            # Only retry with smaller batch if this was a larger batch
            if len(row_ids) > 5 and retry_with_smaller_batch:
                logger.info(f"Large batch of {len(row_ids)} failed, retrying in smaller batches of 5")
                success = True
                # Process in smaller batches
                for i in range(0, len(row_ids), 5):
                    small_batch = row_ids[i:i + 5]
                    if not self.clear_dag_runs(small_batch, csrf_token, retry_with_smaller_batch=False):
                        success = False
                        break
                    time.sleep(1)  # Small delay between retries
                return success
            return False
            
        logger.info(f"Successfully cleared {len(row_ids)} DAG runs")
        return True

    def check_dag_exists(self, dag_id: str) -> bool:
        """Check if DAG exists by trying to fetch its runs"""
        url = f"{self.base_url}/dagrun/list/?_flt_2_dag_id={dag_id}"
        response = requests.get(url, headers=self.headers, cookies=self.cookies)
        
        if response.status_code != 200:
            return False
            
        # Check if we get any results or a "No records found" message
        soup = BeautifulSoup(response.text, 'html.parser')
        no_records = soup.find('div', string='No records found')
        return not bool(no_records)

    def process_dag(self, dag_id: str, batch_size: int = 25) -> DagProcessingResult:
        """Process a single DAG and return its results"""
        result = DagProcessingResult(dag_id=dag_id, status='SUCCESS')
        
        # Check if DAG exists
        if not self.check_dag_exists(dag_id):
            result.status = 'NOT_FOUND'
            result.error_message = f"DAG {dag_id} not found"
            return result

        self.dag_id = dag_id  # Set current dag_id for use in other methods
        csrf_token = None
        
        try:
            while True:
                # Always check page 0 since pages shift down as we clear runs
                row_ids, _, csrf_token = self.get_row_ids_from_page(0, dag_id)
                
                if not row_ids or not csrf_token:
                    logger.info(f"No more failed runs found for DAG {dag_id}")
                    break
                    
                # Process in batches
                for i in range(0, len(row_ids), batch_size):
                    batch = row_ids[i:i + batch_size]
                    result.total_runs_processed += len(batch)
                    
                    if not self.clear_dag_runs(batch, csrf_token):
                        result.failed_runs += len(batch)
                
                time.sleep(2)  # Delay between fetches

        except Exception as e:
            result.status = 'FAILED'
            result.error_message = str(e)
            
        # Set final status
        if result.failed_runs > 0:
            result.status = 'FAILED'
            result.error_message = f"Failed to clear {result.failed_runs} of {result.total_runs_processed} runs"
            
        return result

    def process_dags(self, dag_ids: List[str]) -> List[DagProcessingResult]:
        """Process multiple DAGs and return results"""
        results = []
        for dag_id in dag_ids:
            logger.info(f"Processing DAG: {dag_id}")
            result = self.process_dag(dag_id)
            results.append(result)
            logger.info(f"Finished processing {dag_id}: {result.status}")
        return results

def write_report(results: List[DagProcessingResult], filename: str = 'dag_clearing_report.csv'):
    """Write processing results to CSV file"""
    fieldnames = ['dag_id', 'status', 'total_runs_processed', 'failed_runs', 'error_message', 'processed_at']
    
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result.__dict__)
    
    logger.info(f"Report written to {filename}")

def main():
    # Configuration
    BASE_URL = "https://airflow.ninjavan.dev"
    SESSION_ID = "1aa5f920-1bf5-43ae-bc3a-b7a097cea5bc.PQRXDFlDchMtPINkxSSsj0_WtFc"
    
    # List of all DAG IDs to process
    DAG_IDS = [
        "datalake_cdc_3pl_oms_prod_gl",
        "datalake_cdc_3pl_prod_gl",
        "datalake_cdc_aaa_prod_gl",
        "datalake_cdc_address_appraiser_prod_gl",
        "datalake_cdc_addressing_prod_gl",
        "datalake_cdc_argus_prod_gl",
        "datalake_cdc_automation",
        "datalake_cdc_billing_prod_gl",
        "datalake_cdc_cod_prod_gl",
        "datalake_cdc_consignee_prod_gl",
        "datalake_cdc_control_prod_gl",
        "datalake_cdc_core_prod_id",
        "datalake_cdc_core_prod_mm",
        "datalake_cdc_core_prod_my",
        "datalake_cdc_core_prod_ph",
        "datalake_cdc_core_prod_sg",
        "datalake_cdc_core_prod_th",
        "datalake_cdc_core_prod_vn",
        "datalake_cdc_dash_prod_gl",
        "datalake_cdc_developer_support_automation_prod_gl",
        "datalake_cdc_direct_prod_gl",
        "datalake_cdc_direct_supplier_prod_gl",
        "datalake_cdc_dp_prod_gl",
        "datalake_cdc_driver_prod_gl",
        "datalake_cdc_driver_vantage_prod_gl",
        "datalake_cdc_epi_prod_gl",
        "datalake_cdc_events_prod_gl",
        "datalake_cdc_fdm_prod_gl",
        "datalake_cdc_first_mile_prod_gl",
        "datalake_cdc_goals_prod_gl",
        "datalake_cdc_hub_prod_gl",
        "datalake_cdc_hub_usrs_prod_gl",
        "datalake_cdc_loyalty_prod_gl",
        "datalake_cdc_mart_ecommerce_prod_gl",
        "datalake_cdc_mart_promotion_prod_gl",
        "datalake_cdc_mart_sp_mgmt_prod_gl",
        "datalake_cdc_movement_trip_prod_gl",
        "datalake_cdc_ninjamart_order",
        "datalake_cdc_ninjamart_report",
        "datalake_cdc_ninjamart_warehouse_service",
        "datalake_cdc_notifications_email_prod_gl",
        "datalake_cdc_notifications_prod_gl",
        "datalake_cdc_notifications_push_prod_gl",
        "datalake_cdc_notifications_sms_prod_gl",
        "datalake_cdc_notifications_v2_prod_gl",
        "datalake_cdc_notifications_voice_prod_gl",
        "datalake_cdc_ocreate_prod_gl",
        "datalake_cdc_order_prod_gl",
        "datalake_cdc_overwatch_prod_gl",
        "datalake_cdc_pa_job_search_prod_gl",
        "datalake_cdc_pod_validation_prod_gl",
        "datalake_cdc_pricing_prod_gl",
        "datalake_cdc_recovery_comms_prod_gl",
        "datalake_cdc_reports_prod_gl",
        "datalake_cdc_route_prod_gl",
        "datalake_cdc_script_engine_prod_gl",
        "datalake_cdc_shipper_prod_gl",
        "datalake_cdc_sns_platforms_prod_gl",
        "datalake_cdc_sns_prod_gl",
        "datalake_cdc_sort_mistake_prod_gl",
        "datalake_cdc_sort_prod_gl",
        "datalake_cdc_sort_vendor_prod_gl",
        "datalake_cdc_station_prod_gl",
        "datalake_cdc_ticketing_prod_gl",
        "datalake_cdc_url_shortener_gl",
        "datalake_cdc_webhook_history_prod_gl",
        "datalake_cdc_webhook_receiver_prod_gl",
        "datalake_cdc_wms_prod_gl",
        "datalake_cdc_xb_operations_prod_gl"
    ]
    
    cleaner = AirflowDagCleaner(BASE_URL, SESSION_ID)
    results = cleaner.process_dags(DAG_IDS)
    
    # Write report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f'dag_clearing_report_{timestamp}.csv'
    write_report(results, report_filename)
    
    # Print summary to console
    print("\nProcessing Summary:")
    print("-" * 80)
    for result in results:
        print(f"DAG: {result.dag_id}")
        print(f"Status: {result.status}")
        print(f"Processed: {result.total_runs_processed} runs")
        if result.failed_runs > 0:
            print(f"Failed: {result.failed_runs} runs")
        if result.error_message:
            print(f"Error: {result.error_message}")
        print("-" * 80)

if __name__ == "__main__":
    main() 