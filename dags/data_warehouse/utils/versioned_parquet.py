from pyspark.sql.functions import lit

from common.spark.util import cast_df
from data_warehouse.utils import gcs, logger

logger = logger.get_logger(__file__)


def _get_filter(partitions):
    """
    Gets Spark filter for relevant created_month and system_id partitions within a measurement_datetime partition.

    :param partitions:       List of relevant partitions, e.g.
                             [
                                {'created_month': '2020-08', 'system_id': 'id'},
                                {'created_month': '2020-09', 'system_id': 'id'}
                             ]
    """
    partition_filters = []
    for partition in partitions:
        sub_filters = []
        for partition_column, value in partition.items():
            sub_filter_str = f"{partition_column} = '{value}'"
            if value == "__HIVE_DEFAULT_PARTITION__":
                sub_filter_str = f"{partition_column} is null"
            sub_filters.append(sub_filter_str)
        partition_filters.append(f"({' and '.join(sub_filters)})")
    return " or ".join(partition_filters)


def get_relevant_partitions(partition_combination_df, version_datetime):
    """
    Finds the partition with the latest measurement_datetime before version_datetime for each (system_id, created_month)
    combination. Returns a dict that groups partitions with the same measurement_datetime together, e.g.
    {
        "2020-08-26 18-00-00": [
            {"created_month": "2020-06", "system_id": "id"},
            {"created_month": "2020-07", "system_id": "id"},
        ],
        "2020-08-31 18-00-00": [{"created_month": "2020-08", "system_id": "id"}],
    }
    """
    relevant_partitions = partition_combination_df.query(f"measurement_datetime <= '{version_datetime}'")
    if relevant_partitions.empty:
        raise ValueError(f"No table versions before {version_datetime}.")
    relevant_partitions = relevant_partitions.groupby(["created_month", "system_id"], as_index=False).max()
    return (
        relevant_partitions.groupby("measurement_datetime")
        .apply(lambda x: x.set_index("measurement_datetime").to_dict("records"))
        .to_dict()
    )


def get_latest_version(partition_combination_df):
    """Gets the latest measurement_datetime in the partition_combination_df. Excludes measurement_datetime=latest."""
    partition_combination_df = partition_combination_df.query("measurement_datetime != 'latest'")
    return partition_combination_df["measurement_datetime"].max()


def read(spark, path, version_datetime=None):
    """
    Reads a versioned parquet table.
    - version_datetime can be specified to time travel to a older version of the table
    - If partitions have different schema, the schema follows that of the latest version
    - Assumes that the table has measurement_datetime, system_id and created_month partitions
    """
    partition_combination_df = gcs.get_partition_combination_df(
        path, ["measurement_datetime", "system_id", "created_month"]
    )
    latest_version = get_latest_version(partition_combination_df)

    if not version_datetime:
        version_datetime = latest_version
    logger.info(f"Version: {version_datetime}")

    relevant_partitions = get_relevant_partitions(partition_combination_df, version_datetime)
    latest_schema = spark.read.parquet(f"{path}/measurement_datetime={latest_version}").dtypes

    full_df = None
    for version, partitions in relevant_partitions.items():
        df = spark.read.parquet(f"{path}/measurement_datetime={version}")
        filter_condition = _get_filter(partitions)
        df = df.filter(filter_condition)

        schema_mismatch = df.dtypes != latest_schema
        if schema_mismatch:
            df = cast_df(df, latest_schema, True)

        df = df.withColumn("measurement_datetime", lit(version).cast("string"))

        full_df = full_df.unionByName(df) if full_df else df
    return full_df
