FROM apache/airflow:{{ versions.airflow }}-{{ versions.python }}
USER root

ENV SPARK_HOME /home/<USER>/.local/lib/{{ versions.python }}/site-packages/pyspark
ENV PATH $PATH:$SPARK_HOME/bin
ENV PYTHONPATH $PYTHONPATH:$AIRFLOW_HOME/dags
ENV GOOGLE_APPLICATION_CREDENTIALS /keys/key.json

RUN mkdir -p /usr/share/man/man1 \
    && apt-get update -y \
    && apt-get install -y \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        libsnappy-dev \
        procps \
        software-properties-common \
        unzip \
        wget \
        apt-transport-https \
    && mkdir -p /etc/apt/keyrings \
    && wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | tee /etc/apt/keyrings/adoptium.asc \
    && echo "deb [signed-by=/etc/apt/keyrings/adoptium.asc] https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
    && apt-get update -y \
    && apt-get install -y temurin-8-jdk \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

USER airflow
RUN pip install --upgrade pip==20.2.4
RUN pip install --user delta-spark==1.0.0
COPY --chown=airflow:root ./requirements-airflow.txt requirements.txt
RUN pip install --no-cache-dir --user -r requirements.txt

USER root

RUN cd $SPARK_HOME/jars \
    && curl -O https://storage.googleapis.com/hadoop-lib/gcs/gcs-connector-hadoop2-2.0.1.jar

COPY files $AIRFLOW_HOME/files
COPY spark-defaults.conf $SPARK_HOME/conf/
COPY log4j.properties $SPARK_HOME/conf/
COPY plugins $AIRFLOW_HOME/plugins
COPY dags $AIRFLOW_HOME/dags

RUN ln -s $AIRFLOW_HOME/files/keys /keys \
    && chown -R airflow:root /keys $AIRFLOW_HOME $SPARK_HOME

USER airflow
