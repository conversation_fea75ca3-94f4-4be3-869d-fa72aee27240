# The output from this task is used for exporting to SFMC only. It should not be exposed to Metabase or used for
# analytics. As such, column naming in this table also need not follow DWH conventions.
import sys

from pyspark.sql import SparkSession
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceExportDAG.Task.SFMC_SHIPPERS_EXPORT_MASKED + ".py",
    task_name=data_warehouse.SalesforceExportDAG.Task.SFMC_SHIPPERS_EXPORT_MASKED,
    system_ids=(SystemID.GL,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceExportDAG.DAG_ID,
            task_id=data_warehouse.SalesforceExportDAG.Task.SS_SEGMENTATION_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceExportDAG.DAG_ID,
            task_id=data_warehouse.SalesforceExportDAG.Task.FS_SEGMENTATION_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_DAILY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        ),
    ),
    hive_metastore_config=(base.HiveMetastoreTaskConfig(hive_schema="data_warehouse", partition_columns=tuple()),),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    if enable_full_run:
        scvd_lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    # measurement_datetime for parquet_tables
    measurement_datetime_partition = f"""/measurement_datetime={
        measurement_datetime.strftime('%Y-%m-%d %H-%M-%S')
        }"""

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS,
                view_name="shippers",
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    env).SS_SEGMENTATION_DAILY + measurement_datetime_partition,
                view_name="ss_segmentation_daily",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(
                    env).FS_SEGMENTATION_DAILY + measurement_datetime_partition,
                view_name="fs_segmentation_daily",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_DAILY,
                view_name="shipper_completion_vol_daily",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="shipper_completed_milestones",
                jinja_template="""
                -- addition of volume milestones
                select
                    shipper_id
                    , max(shipper_completion_vol_daily.completion_date) as last_order_completion_date
                    , sum(shipper_completion_vol_daily.total_orders) as lifetime_orders_completed
                    , sum(shipper_completion_vol_daily.total_orders) filter (
                        where
                            shipper_completion_vol_daily.completion_date 
                                    >= (date('{{ measurement_datetime_utc }}') - interval 1 year)
                            and shipper_completion_vol_daily.completion_date 
                                    <= (date('{{ measurement_datetime_utc }}'))
                    ) as orders_p1y
                    , sum(shipper_completion_vol_daily.total_orders) filter (
                        where
                            shipper_completion_vol_daily.completion_date 
                                    >= (date('{{ measurement_datetime_utc }}') - interval 6 month)
                            and shipper_completion_vol_daily.completion_date 
                                    <= (date('{{ measurement_datetime_utc }}'))
                    ) as orders_p6m
                    , sum(shipper_completion_vol_daily.total_orders) filter (
                        where
                            shipper_completion_vol_daily.completion_date 
                                    >= (date('{{ measurement_datetime_utc }}') - interval 3 month)
                            and shipper_completion_vol_daily.completion_date 
                                    <= (date('{{ measurement_datetime_utc }}'))
                    ) as orders_p3m
                    , sum(shipper_completion_vol_daily.total_orders) filter (
                        where
                            shipper_completion_vol_daily.completion_date 
                                    >= (date('{{ measurement_datetime_utc }}') - interval 1 month)
                            and shipper_completion_vol_daily.completion_date 
                                    <= (date('{{ measurement_datetime_utc }}'))
                    ) as orders_p1m
                    , sum(shipper_completion_vol_daily.total_orders) filter (
                        where
                            shipper_completion_vol_daily.completion_date 
                                    >= (date('{{ measurement_datetime_utc }}') - interval 15 day)
                            and shipper_completion_vol_daily.completion_date 
                                    <= (date('{{ measurement_datetime_utc }}') - interval 1 day)
                    ) as orders_p2w
                    , sum(shipper_completion_vol_daily.total_orders) filter (
                        where
                            date_trunc('month', shipper_completion_vol_daily.completion_date) 
                                    = date_trunc('month', (date('{{ measurement_datetime_utc }}')))
                    ) as orders_current_month
                from shipper_completion_vol_daily
                group by 1
                """,
                jinja_arguments={
                    "measurement_datetime_utc": measurement_datetime,
                },
            ),
            base.TransformView(
                view_name="ss_interim",
                jinja_template="""
                -- create the base of shippers from ss_segmentation_daily
                select
                   to_date(ss.snapshot_date, 'yyyy-MM-dd') as snapshot_date
                    , ss.sf_lead_id
                    , null as sf_opp_id
                    , ss.sf_acc_id
                    , ss.sf_contact_id
                    , ss.shipper_id
                    , s.external_id as external_shipper_id
                    , ss.shipper_name
                    , ss.shopify_id
                    , ss.shopify_name
                    , ss.email
                    , null as fs_sales_team
                    , null as fs_salesperson_name
                    , null as fs_salesperson_email
                    , null as fs_salesperson_phone
                    , ss.lifecycle
                    , ss.potential_ppm as ss_potential_ppm
                    , ss.shipper_tier as ss_shipper_tier
                    , ss.orders_30d
                    , ss.orders_90d
                    , shipper_completed_milestones.lifetime_orders_completed
                    , shipper_completed_milestones.orders_p1y
                    , shipper_completed_milestones.orders_p6m
                    , shipper_completed_milestones.orders_p3m
                    , shipper_completed_milestones.orders_p1m
                    , shipper_completed_milestones.orders_p2w
                    , shipper_completed_milestones.orders_current_month
                    , 0 as fs_expected_volume_monthly
                    , ss.alive_probability as ss_alive_probability
                    , 0 as fs_first_3m_churn_prediction
                    , null as fs_lead_creation_datetime
                    , disqualified_date
                    , null as fs_opportunity_creation_datetime
                    , ss.onboarded_date
                    , null as fs_ready_to_ship_date
                    , null as fs_won_date
                    , null as fs_lost_date
                    , null as fs_future_opportunity_date
                    , ss.first_order_completion_date
                    , shipper_completed_milestones.last_order_completion_date
                    , ss.first_order_placed_date
                    , ss.last_order_placed_date
                    , null as nr_join_date
                    , null as nr_last_redemption_date
                    , null as nr_last_used_reward_date
                    , null as nb_referral_count
                    , null as nb_last_referral_date
                    , ss.shopify_purchase_count as ss_shopify_purchase_count
                    , to_date(ss.shopify_first_purchase_date, 'yyyy-MM-dd') as ss_shopify_first_purchase_date
                    , to_date(ss.shopify_last_purchase_date, 'yyyy-MM-dd') as ss_shopify_last_purchase_date
                    , null as fs_disqualification_reason
                    , null as fs_loss_reason
                    , ss.lead_gen_channel
                    , ss.lead_source
                    , ss.lead_source_details
                    , ss.sales_channel
                    , ss.system_id
                from ss_segmentation_daily as ss
                left join shipper_completed_milestones
                    on ss.shipper_id = shipper_completed_milestones.shipper_id
                left join shippers as s
                    on ss.shipper_id = s.id
                where ss.system_id != 'mm'
                """,
            ),
            base.TransformView(
                view_name="fs_completed_milestones",
                jinja_template="""
                -- addition of volume milestones

                with fs_account_enriched as (
                    select shipper_id
                        , parent_acc_id_coalesce
                    from salesforce_account_enriched
                    where sales_channel = 'Field Sales'
                )

                select parent_acc_id_coalesce as sf_acc_id
                    , max(last_order_completion_date) as last_order_completion_date
                    , sum(lifetime_orders_completed) as lifetime_orders_completed
                    , sum(orders_p1y) as orders_p1y
                    , sum(orders_p6m) as orders_p6m
                    , sum(orders_p3m) as orders_p3m
                    , sum(orders_p1m) as orders_p1m
                    , sum(orders_p2w) as orders_p2w
                    , sum(orders_current_month) as orders_current_month
                from shipper_completed_milestones
                join fs_account_enriched
                    on shipper_completed_milestones.shipper_id = fs_account_enriched.shipper_id
                group by 1
                """,
            ),
            base.TransformView(
                view_name="fs_interim",
                jinja_template="""
                -- create the base of shippers from fs_segmentation_daily
                select
                    to_date(fs.snapshot_date, 'yyyy-MM-dd') as snapshot_date
                    , fs.sf_lead_id
                    , fs.sf_opp_id
                    , fs.sf_acc_id
                    , fs.sf_contact_id
                    , fs.shipper_id
                    , s.external_id as external_shipper_id
                    , fs.shipper_name
                    , null as shopify_id
                    , null as shopify_name
                    , fs.email
                    , fs.sales_team as fs_sales_team
                    , fs.salesperson_name as fs_salesperson_name
                    , fs.salesperson_email as fs_salesperson_email
                    , fs.salesperson_phone as fs_salesperson_phone
                    , fs.lifecycle
                    , 0.0 as ss_potential_ppm 
                    , null as ss_shipper_tier
                    , fs.orders_30d
                    , fs.orders_90d
                    , case 
                        when lifecycle in ('14. new', '15. stable', '16. uptrader'
                                            , '17. downtrader', '18. lapsed', '19. regained')
                            then fs_completed_milestones.lifetime_orders_completed
                        else 0 end as lifetime_orders_completed
                    , case 
                        when lifecycle in ('14. new', '15. stable', '16. uptrader'
                                            , '17. downtrader', '18. lapsed', '19. regained')
                            then fs_completed_milestones.orders_p1y
                        else 0 end as orders_p1y
                    , case 
                        when lifecycle in ('14. new', '15. stable', '16. uptrader'
                                            , '17. downtrader', '18. lapsed', '19. regained')
                            then fs_completed_milestones.orders_p6m
                        else 0 end as orders_p6m
                    , case 
                        when lifecycle in ('14. new', '15. stable', '16. uptrader'
                                            , '17. downtrader', '18. lapsed', '19. regained')
                            then fs_completed_milestones.orders_p3m
                        else 0 end as orders_p3m
                    , case 
                        when lifecycle in ('14. new', '15. stable', '16. uptrader'
                                            , '17. downtrader', '18. lapsed', '19. regained')
                            then fs_completed_milestones.orders_p1m
                        else 0 end as orders_p1m
                    , case 
                        when lifecycle in ('14. new', '15. stable', '16. uptrader'
                                            , '17. downtrader', '18. lapsed', '19. regained')
                            then fs_completed_milestones.orders_p2w
                        else 0 end as orders_p2w
                    , case 
                        when lifecycle in ('14. new', '15. stable', '16. uptrader'
                                            , '17. downtrader', '18. lapsed', '19. regained')
                            then fs_completed_milestones.orders_current_month
                        else 0 end as orders_current_month
                    , fs.expected_volume_monthly as fs_expected_volume_monthly
                    , 0 as ss_alive_probability
                    , fs.first_3m_churn_prediction as fs_first_3m_churn_prediction
                    , to_date(fs.lead_creation_datetime, 'yyyy-MM-dd') as fs_lead_creation_datetime
                    , disqualified_date
                    , to_date(fs.opportunity_creation_datetime, 'yyyy-MM-dd') as fs_opportunity_creation_datetime
                    , fs.onboarded_date
                    , fs.ready_to_ship_date as fs_ready_to_ship_date
                    , fs.won_date as fs_won_date
                    , fs.lost_date as fs_lost_date
                    , fs.future_opportunity_date as fs_future_opportunity_date
                    , fs.first_order_completion_date
                    , fs_completed_milestones.last_order_completion_date
                    , fs.first_order_placed_date
                    , fs.last_order_placed_date
                    , to_date(fs.nr_join_date, 'yyyy-MM-dd') as nr_join_date
                    , to_date(fs.nr_last_redemption_date, 'yyyy-MM-dd') as nr_last_redemption_date
                    , to_date(fs.nr_last_used_reward_date, 'yyyy-MM-dd') as nr_last_used_reward_date
                    , fs.nb_referral_count
                    , to_date(fs.nb_last_referral_date, 'yyyy-MM-dd') as nb_last_referral_date
                    , 0 as ss_shopify_purchase_count
                    , null as ss_shopify_first_purchase_date
                    , null as ss_shopify_last_purchase_date
                    , fs.disqualification_reason as fs_disqualification_reason
                    , fs.loss_reason as fs_loss_reason
                    , fs.lead_gen_channel
                    , fs.lead_source
                    , fs.lead_source_details
                    , fs.sales_channel
                    , fs.system_id
                from fs_segmentation_daily as fs
                left join fs_completed_milestones
                    on fs.sf_acc_id = fs_completed_milestones.sf_acc_id
                left join shippers as s
                    on fs.shipper_id = s.id
                where fs.system_id != 'mm'
                """,
            ),
            base.TransformView(
                view_name="sfmc_shippers_export",
                jinja_template="""
                -- union ss_interim & fs_interim
                select *
                from ss_interim
                where True
                    and sales_channel is not null

                union

                select *
                from fs_interim
                where True
                    and sales_channel is not null
                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SFMC_SHIPPERS_EXPORT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id",),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run, )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()