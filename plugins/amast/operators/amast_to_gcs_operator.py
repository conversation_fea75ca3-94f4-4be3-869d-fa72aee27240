from airflow.models import BaseOperator, SkipMixin
from datetime import datetime
from amast.hooks.amast_hook import AmastHook


PARTITION_COLUMN = "nv_updated_date"


class AmastToGCSOperator(BaseOperator, SkipMixin):
    def __init__(
        self,
        entity,
        gcs_bucket,
        system_id,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity = entity
        self.gcs_bucket = gcs_bucket
        self.system_id = system_id

    def execute(self, context):
        exec_date = context["execution_date"].strftime("%Y-%m-%d")
        hook = AmastHook(self.system_id)
        print('self.latest_date', exec_date)
        df = hook.daily_pull(self.entity, exec_date)

        if len(df) == 0:
            print(f"No data found for {self.entity} on {exec_date}")
            self._skip_downstream_tasks(context)
            return

        df[PARTITION_COLUMN] = exec_date

        dirname = f"gs://{self.gcs_bucket}/amast/{self.entity}/{self.system_id}"

        df.to_parquet(
            dirname, compression="snappy", engine="pyarrow", index=False, partition_cols=[PARTITION_COLUMN]
        )
        print(f"Uploaded to GCS bucket at {dirname}")

    def _skip_downstream_tasks(self, context):
        downstream_tasks = context["task"].get_flat_relatives(upstream=False)
        if downstream_tasks:
            print(f"Skipping downstream tasks: {downstream_tasks}")
            self.skip(context["dag_run"], context["ti"].execution_date, downstream_tasks)