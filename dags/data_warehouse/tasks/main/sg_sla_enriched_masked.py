import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderSLADAG.Task.SG_SLA_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.OrderSLADAG.Task.SG_SLA_ENRICHED_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID, task_id=data_warehouse.DPDAG.Task.DP_RESERVATION_EVENTS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID, task_id=data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID, task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.POH_ORDER_METRICS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, task_id=data_warehouse.FleetDAG.Task.POH_METRICS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.RESERVE_TRACKING_IDS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID, task_id=data_warehouse.OrdersDAG.Task.ORDER_RTS_TRIGGERS_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID, task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID,
            task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
    ),
    system_ids=(constants.SystemID.SG,),
    execution_timeout=constants.Timeout.THREE_HOURS,
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 5, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATION_EVENTS_ENRICHED,
                view_name="dp_reservation_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATIONS_ENRICHED,
                view_name="dp_reservations",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_DESTINATIONS,
                view_name="order_destinations",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_RTS_TRIGGERS,
                view_name="order_rts_triggers",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_ORDER_METRICS,
                view_name="poh_order_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).POH_METRICS,
                view_name="poh_metrics",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVE_TRACKING_IDS_ENRICHED,
                view_name="reserve_tracking_ids",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).WAREHOUSE_SWEEPS,
                view_name="warehouse_sweeps",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTION_FAILURE_REASON,
                view_name="transaction_failure_reason",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.AAAProdGL(input_env, is_masked).USER_INFO,
                view_name="user_info",
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR,
                view_name="calendar"
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).SG_SLA_DEST_ZONE,
                view_name="dest_zone_map",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="transaction_new_logic",
                jinja_template="""
                select
                    tx.id
                    , tx.type
                    , tx.order_id
                    , tx.status
                    , tx.route_id
                    , (from_utc_timestamp(tx.service_end_time, '{{ local_timezone }}')) as service_end_time
                    , tx_fail.failure_reason_code_id
                    , tx.transaction_time
                    , if(from_utc_timestamp(tx.service_end_time, '{{ local_timezone }}')
                        >= rts_trigger.event_datetime
                        + interval '5' minute , 'rts', 'delivery') trans_type
                    , tx_fail.id transaction_failure_reason_id
                    , tx.distribution_point_id
                    , case
                        when tx.status = 'Success' then 1
                        when tx.status = 'Fail' 
                            and tx.distribution_point_id is null
                            and tx_fail.failure_reason_code_id not in (5,6,13)
                        then 1
                        else 0
                    end as valid_flag
                from transactions as tx
                left join transaction_failure_reason as tx_fail
                    on tx.id = tx_fail.transaction_id
                    and tx_fail.deleted_at is null
                left join order_rts_triggers as rts_trigger
                    on tx.order_id = rts_trigger.order_id
                    and rts_trigger.system_id = 'sg'
                where
                    tx.deleted_at is null
                    and tx.service_end_time is not null
                    and tx.type = 'DD'
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="first_overstay_event",
                jinja_template="""
                with first_overstay as (
                
                    select
                        dp_reservations.order_id
                        , min(dp_reservation_events.created_at) as overstayed_trigger_datetime
                    from dp_reservation_events
                    left join dp_reservations
                        on dp_reservation_events.dp_reservation_id = dp_reservations.id
                    where
                        dp_reservation_events.name = 'OVERSTAYED'
                    group by 1
                
                )
                
                select
                    base.order_id
                    , overstayed_trigger_datetime
                    , min(from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) overstayed_pickup_datetime
                from first_overstay base
                left join inbound_scans inbound
                    on base.order_id = inbound.order_id
                    and base.overstayed_trigger_datetime < from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')
                    and inbound.type = 1
                    and inbound.deleted_at is null
                group by 1,2
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="first_proof_of_handover",
                jinja_template="""
                select
                    poh_order.order_id
                    , min(poh.handover_time) first_handover_datetime
                    , min_by(poh.hub_id, poh.handover_time) first_handover_hub_id
                from poh_order_metrics as poh_order
                left join poh_metrics as poh
                    on poh_order.hub_handover_id = poh.id
                    and poh.deleted_at is null
                where
                    poh_order.deleted_at is null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="inbound_scan_timestamps",
                jinja_template="""
                select
                    inbound.order_id
                    , min(from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) as first_inbound_scan_datetime
                    , min_by(inbound.type, from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) as first_inbound_scan_type
                    
                    --hub inbound related
                    , min(from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 2
                            and inbound.route_id is null
                            and hubs.facility_type = 'CROSSDOCK'
                        ) as hub_inbound_datetime
                    , min_by(inbound.hub_id, from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 2
                            and inbound.route_id is null
                            and hubs.facility_type = 'CROSSDOCK'
                        ) as hub_inbound_hub_id
                    , min_by(inbound.scanned_by, from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 2
                            and inbound.route_id is null
                            and hubs.facility_type = 'CROSSDOCK'
                        ) as hub_inbound_scanned_by_id
                    , min_by(
                        concat(scan_user.first_name, ' ', scan_user.last_name)
                        , from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')
                        ) filter (
                            where inbound.type = 2
                                and inbound.route_id is null
                                and hubs.facility_type = 'CROSSDOCK'
                            ) as hub_inbound_scanned_by_name
                    
                    --route inbound related
                    , min(from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 2
                            and inbound.route_id is not null
                        ) as route_inbound_datetime
                    , min_by(inbound.hub_id, from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 2
                            and inbound.route_id is not null
                        ) as route_inbound_hub_id
                    , min_by(inbound.scanned_by, from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 2
                            and inbound.route_id is not null
                        ) as route_inbound_scanned_by_id
                    , min_by(
                        concat(scan_user.first_name, ' ', scan_user.last_name)
                        , from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')
                        ) filter (
                            where inbound.type = 2
                                and inbound.route_id is not null
                            ) as route_inbound_scanned_by_name

                    --for leg van inbound related
                    , min(from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 4
                            and inbound.route_id = new_tx.route_id
                            and new_tx.trans_type = 'delivery'
                        ) as for_leg_van_inbound_datetime
                    , min_by(inbound.hub_id , from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 4
                            and inbound.route_id = new_tx.route_id
                            and new_tx.trans_type = 'delivery'
                        ) as for_leg_van_inbound_hub_id

                    --rts leg van inbound related
                    , min(from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 4
                            and inbound.route_id = new_tx.route_id
                            and new_tx.trans_type = 'rts'
                        ) as rts_leg_van_inbound_datetime
                    , min_by(inbound.hub_id , from_utc_timestamp(inbound.created_at, '{{ local_timezone }}')) filter (
                        where inbound.type = 4
                            and inbound.route_id = new_tx.route_id
                            and new_tx.trans_type = 'rts'
                        ) as rts_leg_van_inbound_hub_id
                from inbound_scans inbound
                left join hubs_enriched hubs
                    on inbound.hub_id = hubs.id
                    and hubs.system_id = 'sg'
                left join user_info scan_user
                    on inbound.scanned_by = scan_user.user_id
                left join transaction_new_logic new_tx
                    on inbound.order_id = new_tx.order_id
                where
                    inbound.deleted_at is null
                group by 1
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
            base.TransformView(
                view_name="warehouse_sweeps_timestamps",
                jinja_template="""
                select
                    sweep.order_id
                    , min(from_utc_timestamp(sweep.created_at, '{{ local_timezone }}')) as first_warehouse_sweep_datetime
                    
                    --warehouse sweep related
                    , min(from_utc_timestamp(sweep.created_at, '{{ local_timezone }}')) filter (
                        where hubs.facility_type in ('CROSSDOCK_STATION', 'STATION')
                        ) as first_station_sweep_datetime
                    , min_by(sweep.hub_id, from_utc_timestamp(sweep.created_at, '{{ local_timezone }}')) filter (
                        where hubs.facility_type in ('CROSSDOCK_STATION', 'STATION')
                        ) as first_station_sweep_hub_id
                    , min_by(sweep.scanned_by, from_utc_timestamp(sweep.created_at, '{{ local_timezone }}')) filter (
                        where hubs.facility_type in ('CROSSDOCK_STATION', 'STATION')
                        ) as first_station_sweep_scanned_by_id
                    , min_by(
                        concat(scan_user.first_name, ' ', scan_user.last_name)
                        , from_utc_timestamp(sweep.created_at, '{{ local_timezone }}')
                        ) filter (
                            where hubs.facility_type in ('CROSSDOCK_STATION', 'STATION')
                            ) as first_station_sweep_scanned_by_name
                    
                    --dest sweep related
                    , min(from_utc_timestamp(sweep.created_at, '{{ local_timezone }}')) filter (
                        where sweep.hub_id = destination.delivery_dest_hub_id
                        ) as dest_hub_sweep_datetime

                from warehouse_sweeps sweep
                left join hubs_enriched hubs
                    on sweep.hub_id = hubs.id
                    and hubs.system_id = 'sg'
                left join user_info scan_user
                    on sweep.scanned_by = scan_user.user_id
                left join order_destinations destination
                    on sweep.order_id = destination.order_id
                where
                    sweep.deleted_at is null
                group by 1
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },                                                                                                                                                                                                                                                                                  
            ),
            base.TransformView(
                view_name="final",
                jinja_template="""
                with trans_ranked as(

                    select
                        *
                        , row_number() over (
                            partition by order_id,
                            trans_type order by service_end_time asc,
                            transaction_failure_reason_id desc) rnk
                    from transaction_new_logic
                    where 
                        valid_flag = 1

                ),

                sg_defined_first_valid_delivery_attempt as (

                    select
                        order_id
                        , service_end_time sg_defined_first_valid_delivery_attempt
                    from trans_ranked
                    where
                        trans_type = 'delivery'
                        and rnk = 1

                ),

                base as (

                    select
                        om.order_id
                        , om.tracking_id
                        , om.creation_datetime
                        , om.status
                        , om.granular_status
                        , om.service_type
                        , om.parcel_size
                        , om.from_postcode
                        , om.to_postcode
                        , om.dest_postcode
                        , om.insurance_value
                        , om.dest_hub_id
                        , om.dp_dropoff_dp_id
                        , om.dp_dropoff_datetime
                        , om.pickup_attempts
                        , om.first_pickup_attempt_datetime
                        , om.first_pickup_attempt_failure_reason_id
                        , om.pickup_datetime
                        , om.pickup_success_driver_id
                        , drivers.display_name pickup_success_driver_name
                        , handover.first_handover_datetime as hub_handover_datetime
                        , handover.first_handover_hub_id as handover_hub_id
                        , om.inbound_datetime
                        , om.driver_to_dp_dpms_id sg_driver_to_dp_dpms_id
                        , om.driver_to_dp_datetime sg_driver_to_dp_datetime
                        , case
                            when (om.to_country is null
                                or om.to_country not in ('sg','singapore')
                            )
                                and (
                                    om.dest_zone is not null
                                    and dz.dest_zone is null
                                ) then 'sg'
                            else lower(coalesce(om.original_to_country, om.to_country))
                        end as pre_to_country
                        , om.third_party_transfer_datetime
                        , om.third_party_tracking_id
                        , om.delivery_attempts sg_delivery_attempts
                        , custom_logic.sg_defined_first_valid_delivery_attempt
                        , om.first_valid_delivery_attempt_datetime sg_first_valid_delivery_attempt_datetime
                        , om.second_valid_delivery_attempt_datetime sg_second_valid_delivery_attempt_datetime
                        , om.third_valid_delivery_attempt_datetime sg_third_valid_delivery_attempt_datetime
                        , om.delivery_success_datetime sg_delivery_success_datetime
                        , om.delivery_type
                        , shippers.id as shipper_id
                        , shippers.parent_id_coalesce
                        , om.created_month
                        , least(
                            inbound_scan.first_inbound_scan_datetime
                            , warehouse_sweeps.first_warehouse_sweep_datetime
                        ) as original_start_clock
                        , case
                            when least(
                                inbound_scan.first_inbound_scan_datetime, warehouse_sweeps.first_warehouse_sweep_datetime
                            ) = inbound_scan.first_inbound_scan_datetime
                            then case
                                when inbound_scan.first_inbound_scan_type = 1 then 'pickup_scan'
                                when inbound_scan.first_inbound_scan_type = 2 then 'hub_inbound'
                                else 'other'
                            end
                            when least(
                                inbound_scan.first_inbound_scan_datetime, warehouse_sweeps.first_warehouse_sweep_datetime
                            ) = warehouse_sweeps.first_warehouse_sweep_datetime
                            then 'warehouse_sweep'
                        end original_start_clock_type
                        , om.rts_flag sg_rts_flag
                        , om.rts_dest_hub_id
                        , om.rts_dest_zone
                        , om.rts_trigger_datetime sg_rts_trigger_datetime
                        , om.first_valid_rts_attempt_datetime sg_first_valid_rts_attempt_datetime
                        , om.last_valid_rts_attempt_datetime sg_last_valid_rts_attempt_datetime
                        , om.rts_attempts sg_rts_attempts
                        , om.force_success_datetime sg_force_success_datetime
                        , om.force_success_flag sg_force_success_flag
                        , om.dest_zone
                        , rti.is_pickup_required
                        , rti.scheduled_pickup_date
                        , rti.delivery_start_date
                        , rti.delivery_start_time
                        , rti.delivery_end_time
                        , rti.allow_self_collection as dp_collect_intended_flag
                        , inbound_scan.hub_inbound_datetime
                        , inbound_scan.hub_inbound_hub_id
                        , inbound_scan.hub_inbound_scanned_by_id
                        , inbound_scan.hub_inbound_scanned_by_name
                        , inbound_scan.route_inbound_datetime
                        , inbound_scan.route_inbound_hub_id
                        , inbound_scan.route_inbound_scanned_by_id
                        , inbound_scan.route_inbound_scanned_by_name
                        , inbound_scan.for_leg_van_inbound_datetime
                        , inbound_scan.for_leg_van_inbound_hub_id
                        , inbound_scan.rts_leg_van_inbound_datetime
                        , inbound_scan.rts_leg_van_inbound_hub_id
                        , warehouse_sweeps.first_station_sweep_datetime
                        , warehouse_sweeps.first_station_sweep_hub_id
                        , warehouse_sweeps.first_station_sweep_scanned_by_id
                        , warehouse_sweeps.first_station_sweep_scanned_by_name
                        , warehouse_sweeps.dest_hub_sweep_datetime
                        , if(overstay.order_id is not null, 1, 0) overstayed_flag
                        , overstay.overstayed_trigger_datetime
                        , overstay.overstayed_pickup_datetime
                    from order_milestones as om
                    left join reserve_tracking_ids rti
                        on om.tracking_id = rti.tracking_id
                    left join inbound_scan_timestamps as inbound_scan
                        on om.order_id = inbound_scan.order_id
                    left join warehouse_sweeps_timestamps as warehouse_sweeps
                        on om.order_id = warehouse_sweeps.order_id
                    left join first_proof_of_handover handover
                        on om.order_id = handover.order_id
                    left join first_overstay_event overstay
                        on om.order_id = overstay.order_id
                    left join sg_defined_first_valid_delivery_attempt as custom_logic
                        on om.order_id = custom_logic.order_id
                    left join shipper_attributes as shippers
                        on om.shipper_id = shippers.id
                    left join drivers_enriched as drivers
                        on om.pickup_success_driver_id = drivers.id
                    left join dest_zone_map as dz
                        on om.dest_zone = dz.dest_zone
                    where
                        om.system_id = 'sg'

                ),

                base2 as (

                    select
                        *
                        , case
                            when pre_to_country in ('malaysia') then 'my'
                            when pre_to_country in ('thailand') then 'th'
                            when pre_to_country in ('singapore') then 'sg'
                            when pre_to_country in ('philippines') then 'ph'
                            when pre_to_country in ('vietnam') then 'vn'
                            when pre_to_country in ('indonesia') then 'id'
                            else pre_to_country
                        end to_country
                    from base

                ),

                flag as (

                    select
                        *
                        , if(granular_status = 'Cancelled', 1, 0) is_cancelled_flag
                        , if(dp_dropoff_dp_id is null, 0, 1) is_dp_send_flag
                        , case
                            when to_country = 'sg' then 1
                            when to_country is null then null
                            else 0
                        end is_domestic_flow
                        , case
                            when to_country in ('my', 'ph', 'id', 'vn', 'th') then 1
                            when to_country is null then null
                            else 0
                        end is_nv_country_presence_flag
                        , case
                            when delivery_type in ('1 Day - Anytime'
                                                    , '1 Day - Day/Night'
                                                    , '1 Day Timeslot'
                                                    , 'Return 1 Day - Anytime'
                                                    , 'Return 1 Day - Day/Night'
                                                    , 'Return 1 Day - Timeslot') then '1day'
                            when delivery_type in ('3 Days - Anytime'
                                                    , '3 Days - Day/Night'
                                                    , '3 Days - Timeslot'
                                                    , 'Return 3 Days Anytime'
                                                    , 'Return 3 Days Day/Night'
                                                    , 'Return 3 Days Timeslot') then '3day'
                            when delivery_type = 'Normal 2 Days Anytime' then '2day'
                            when delivery_type = 'International' then 'intl'
                            when delivery_type = 'Sameday' then 'sameday'
                            else 'invalid'
                        end as sla_type
                        , if(lower(delivery_type) like '%return%', 1, 0) return_sla_type_flag
                        , if(
                            lower(delivery_type) like '%return%'
                            and (
                                (dest_postcode = '533864' and shipper_id in (291101, 6203207)) --lazada
                                or (dest_postcode = '627545' and shipper_id = 5020842) --shopee
                                or (dest_postcode = '608829' and shipper_id = 1066836) --amazon
                            ), 1, 0) as return_ndrtm_flag
                    from base2

                ),

                start_stop_clock as (

                    select
                        om.*
                        , if(om.is_nv_country_presence_flag = 1
                            and dest.tracking_id is not null, 1, 0
                        ) as is_nv_engaged_flag
                        , case
                            when is_domestic_flow = 1 then if(sg_driver_to_dp_datetime is not null, 1, 0)
                            when dest.tracking_id is not null then if(dest.driver_to_dp_datetime is not null, 1, 0)
                            else 0
                        end is_dp_collect_flag
                        , least(om.dp_dropoff_datetime, om.original_start_clock) start_clock
                        , if(least(om.dp_dropoff_datetime, om.original_start_clock) = om.dp_dropoff_datetime
                            , 'dp_dropoff'
                            , original_start_clock_type
                        ) as start_clock_type 
                        , case
                            when om.is_domestic_flow != 1
                                and not (
                                    om.is_nv_country_presence_flag = 1
                                    and dest.tracking_id is not null
                                ) then null
                            when om.is_nv_country_presence_flag = 1
                                and dest.tracking_id is not null
                                and dest.driver_to_dp_datetime is not null
                                then coalesce(
                                    least(
                                        dest.first_valid_delivery_attempt_datetime,
                                        dest.driver_to_dp_datetime),
                                        if(dest.force_success_datetime < dest.rts_trigger_datetime,
                                        dest.force_success_datetime, null)
                                        )
                            when om.is_nv_country_presence_flag = 1
                                and dest.tracking_id is not null
                                and dest.driver_to_dp_datetime is null
                                then coalesce(
                                    dest.first_valid_delivery_attempt_datetime,
                                    if(
                                        dest.force_success_datetime <
                                        dest.rts_trigger_datetime,
                                        dest.force_success_datetime, null)
                                )
                            when om.is_domestic_flow = 1
                                and sg_driver_to_dp_datetime is not null
                                then coalesce(
                                    least(
                                        om.sg_defined_first_valid_delivery_attempt,
                                        om.sg_driver_to_dp_datetime),
                                        if(
                                            om.sg_force_success_datetime <
                                            om.sg_rts_trigger_datetime,
                                            om.sg_force_success_datetime, null)
                                        )
                            when om.is_domestic_flow = 1
                                then coalesce(
                                    om.sg_defined_first_valid_delivery_attempt,
                                    if(om.sg_force_success_datetime <
                                    om.sg_rts_trigger_datetime,
                                    om.sg_force_success_datetime, null)
                                )
                            else null
                        end as end_clock
                        , dest_hub.address_city dest_hub_address_city
                        , dest_hub.region dest_hub_region
                        , dest.driver_to_dp_dpms_id oc_driver_to_dp_dpms_id
                        , dest.driver_to_dp_datetime oc_driver_to_dp_datetime
                        , dest.delivery_attempts oc_delivery_attempts
                        , dest.first_valid_delivery_attempt_datetime oc_first_valid_delivery_attempt_datetime
                        , dest.second_valid_delivery_attempt_datetime oc_second_valid_delivery_attempt_datetime
                        , dest.third_valid_delivery_attempt_datetime oc_third_valid_delivery_attempt_datetime
                        , dest.delivery_success_datetime oc_delivery_success_datetime
                        , dest.rts_flag oc_rts_flag
                        , dest.rts_trigger_datetime oc_rts_trigger_datetime
                        , dest.first_valid_rts_attempt_datetime oc_first_valid_rts_attempt_datetime
                        , dest.last_valid_rts_attempt_datetime oc_last_valid_rts_attempt_datetime
                        , dest.rts_attempts oc_rts_attempts
                        , case
                            when om.to_country = 'my' then if(dest_hub.address_city in ('Sabah', 'Sarawak'), 10, 5)
                            when om.to_country = 'id' then if(dest_hub.region in ('Greater Jakarta'), 6, 10)
                        end intl_sla_days
                    from flag as om
                    left join order_milestones dest
                        on om.to_country = dest.system_id
                        and om.third_party_tracking_id = dest.tracking_id
                    left join hubs_enriched dest_hub
                        on dest.system_id = dest_hub.system_id
                        and dest.dest_hub_id = dest_hub.id

                ),

                redefined_columns (

                    select
                        original.*
                        , if(is_nv_engaged_flag = 1, oc_delivery_attempts, sg_delivery_attempts) delivery_attempts
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_driver_to_dp_dpms_id,
                            sg_driver_to_dp_dpms_id
                        ) driver_to_dp_dpms_id
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_driver_to_dp_datetime,
                            sg_driver_to_dp_datetime
                        ) driver_to_dp_datetime
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_first_valid_delivery_attempt_datetime,
                            sg_first_valid_delivery_attempt_datetime
                        ) first_valid_delivery_attempt_datetime
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_second_valid_delivery_attempt_datetime,
                            sg_second_valid_delivery_attempt_datetime
                        ) second_valid_delivery_attempt_datetime
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_third_valid_delivery_attempt_datetime,
                            sg_third_valid_delivery_attempt_datetime
                        ) third_valid_delivery_attempt_datetime
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_delivery_success_datetime,
                            sg_delivery_success_datetime
                        ) delivery_success_datetime
                        , if(is_nv_engaged_flag = 1, oc_rts_flag, sg_rts_flag) rts_flag
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_rts_trigger_datetime,
                            sg_rts_trigger_datetime
                        ) rts_trigger_datetime
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_first_valid_rts_attempt_datetime,
                            sg_first_valid_rts_attempt_datetime
                        ) first_valid_rts_attempt_datetime
                        , if(
                            is_nv_engaged_flag = 1,
                            oc_last_valid_rts_attempt_datetime,
                            sg_last_valid_rts_attempt_datetime
                        ) last_valid_rts_attempt_datetime
                        , if(is_nv_engaged_flag = 1, oc_rts_attempts, sg_rts_attempts) rts_attempts
                        , sg_force_success_datetime force_success_datetime
                        , sg_force_success_flag force_success_flag
                    from start_stop_clock as original

                ),

                sla_cal_prep as (

                    select
                        base.*
                        , if(
                            base.is_domestic_flow = 1 and base.delivery_start_time = '09:00',
                            '08:00', base.delivery_start_time
                        ) sla_start_time
                        , if(base.delivery_end_time is null, '22:00', base.delivery_end_time) sla_end_time
                        , case
                            when
                                base.sla_type = '1day'
                                and return_sla_type_flag = 0
                                and (
                                    (base.shipper_id = 16032 and base.start_clock <= date('2022-10-03'))
                                    or (base.shipper_id = 1204962 and base.start_clock <= date('2022-07-01'))
                                    or (base.shipper_id = 605541 and base.start_clock <= date('2022-07-01'))
                                    or (base.shipper_id = 13918 and base.start_clock <= date('2022-05-27'))
                                )
                                and date(delivery_start_date) >= date(base.start_clock)
                                then 1
                            else 0
                        end is_future_dated_order
                        , (intl_sla_days - coalesce(
                            third_party_cal.working_day_cum - start_clock_cal.working_day_cum, 0)
                        ) leftover_days
                        , (
                            rts_attempt_cal.working_day_cum - rts_trigger_cal.working_day_cum
                        ) as days_from_rts_trigger_to_first_rts_attempt
                        , (
                            rts_success_cal.working_day_cum - rts_trigger_cal.working_day_cum
                        ) as days_from_rts_trigger_to_rts_success
                    from redefined_columns as base
                    left join calendar as start_clock_cal
                        on date(base.start_clock) = start_clock_cal.next_working_day_0
                        and start_clock_cal.system_id = 'sg'
                    left join calendar as third_party_cal
                        on date(base.third_party_transfer_datetime) = third_party_cal.next_working_day_0
                        and third_party_cal.system_id = 'sg'
                    left join calendar as rts_trigger_cal
                        on date(base.rts_trigger_datetime) = rts_trigger_cal.next_working_day_0
                        and rts_trigger_cal.system_id = 'sg'
                    left join calendar as rts_attempt_cal
                        on date(base.first_valid_rts_attempt_datetime) = rts_attempt_cal.next_working_day_0
                        and rts_attempt_cal.system_id = 'sg'
                    left join calendar as rts_success_cal
                        on date(base.delivery_success_datetime) = rts_success_cal.next_working_day_0
                        and base.rts_flag = 1
                        and rts_success_cal.system_id = 'sg'

                ),

                sla_date as (

                    select
                        base.*
                        , case
                            when base.is_future_dated_order = 1
                                then if(
                                    future.working_day = 1,
                                    future.next_working_day_0,
                                    future.next_working_day_1
                                )
                            when base.sla_type = '1day' then local.next_working_day_1
                            when base.sla_type = '2day' then local.next_working_day_2
                            when base.sla_type = '3day' then local.next_working_day_3
                            when base.sla_type = 'sameday' then if(
                                hour(base.start_clock) < 14
                                and local.working_day = 1,
                                local.next_working_day_0,
                                local.next_working_day_1
                            )
                            when base.sla_type = 'intl'
                                then case
                                    when base.to_country in ('my', 'id') and base.is_nv_engaged_flag = 1
                                        then case
                                            {%- for day in range(11) %}
                                            when leftover_days = {{ day }} then coalesce(
                                                international_regional.next_working_day_{{ day }},
                                                international_national.next_working_day_{{ day }}
                                            )
                                            {%- endfor %}
                                            when leftover_days < 0
                                                then case
                                                    when intl_sla_days = 5 then local.next_working_day_5
                                                    when intl_sla_days = 6 then local.next_working_day_6
                                                    when intl_sla_days = 10 then local.next_working_day_10
                                                end
                                        end
                                    when base.to_country = 'sg'
                                        then case
                                            when base.service_type = 'NEXTDAY' then local.next_working_day_1
                                            when base.service_type = 'EXPRESS' then local.next_working_day_2
                                            when base.service_type = 'STANDARD' then local.next_working_day_3
                                        end
                                    else null
                                end
                            else null
                        end as sla_date

                        , case
                            when base.is_future_dated_order = 1 then null
                            when base.sla_type = '1day' then 1
                            when base.sla_type = '2day' then 2
                            when base.sla_type = '3day' then 3
                            when base.sla_type = 'sameday' then
                                if(
                                    hour(base.start_clock) < 14
                                    and local.working_day = 1, 0, 1
                                )
                            when base.sla_type = 'intl'
                                then case
                                    when base.to_country in ('my', 'id')
                                        and base.is_nv_engaged_flag = 1
                                        then intl_sla_days
                                    when base.to_country = 'sg'
                                        then case
                                            when base.service_type = 'NEXTDAY' then 1
                                            when base.service_type = 'EXPRESS' then 2
                                            when base.service_type = 'STANDARD' then 3
                                        end
                                end
                            else null
                        end sla_days

                    from sla_cal_prep as base
                    left join calendar as local
                        on date(base.start_clock) = local.next_working_day_0
                        and local.country = 'sg'
                    left join calendar as future
                        on date(base.delivery_start_date) = future.next_working_day_0
                        and future.country = 'sg'
                    left join calendar as international_national
                        on base.to_country = international_national.system_id
                        and international_national.region = 'national'
                        and date(base.third_party_transfer_datetime) = international_national.next_working_day_0
                    left join calendar as international_regional
                        on international_regional.system_id = 'my'
                        and base.to_country = international_regional.system_id
                        and international_regional.region = base.dest_hub_address_city
                        and date(base.third_party_transfer_datetime) = international_regional.next_working_day_0

                ),

                sla_measured_flag as (
                    select
                        *
                        , if(
                            sla_date is not null
                            and start_clock is not null
                            and sla_date < from_utc_timestamp('{{ measurement_datetime }}', '{{ local_timezone }}')
                            and granular_status != 'Cancelled', 1, 0) sla_measured_flag
                        , date_format(end_clock, 'HH:mm') as end_time
                    from sla_date

                ),

                sla_met_flag as (

                    select
                        *
                        , case
                            when sla_measured_flag = 0 then null
                            when is_future_dated_order = 1 and date(end_clock) = sla_date then 1
                            when is_future_dated_order = 0 and date(end_clock) <= sla_date then 1
                            else 0
                        end as sla_date_met_flag
                        , case
                            when sla_measured_flag = 0 then null
                            when is_domestic_flow != 1 then null
                            when end_time >= sla_start_time and end_time <= sla_end_time then 1
                            else 0
                        end as sla_timeslot_met_flag
                    from sla_measured_flag

                ),

                overall_sla_met as (

                    select
                        *
                        , case
                            when sla_measured_flag = 0 then null
                            when is_domestic_flow = 1 and sla_date_met_flag = 1 and sla_timeslot_met_flag = 1 then 1
                            when is_domestic_flow != 1 and sla_date_met_flag = 1 then 1
                            else 0
                        end sla_met_flag
                    from sla_met_flag

                ),

                base3 as (

                    select
                        *
                        , start_clock for_leg_start_clock
                        , start_clock_type for_leg_start_clock_type
                        , end_clock for_leg_end_clock
                        , sla_date for_leg_sla_date
                        , sla_start_time for_leg_sla_timeslot_start
                        , sla_end_time for_leg_sla_timeslot_end
                        , sla_measured_flag for_leg_sla_measured
                        , sla_date_met_flag for_leg_sla_date_met
                        , sla_timeslot_met_flag for_leg_sla_timeslot_met
                        , sla_met_flag for_leg_sla_met
                        , insurance_value cogs
                    from overall_sla_met

                ),

                intermediate as (
                    select
                        *
                        , case
                            when is_future_dated_order = 1 then 0
                            when shipper_id = 5011035 then 0
                            when is_domestic_flow = 0 then 0
                            when sla_type = 'sameday' then 0
                            else 1
                        end as is_measured_for_ttsr
                    from base3
                ),

                basic_setup as (
                    select
                        *
                        , case
                            when is_measured_for_ttsr = 0 then null
                            when sla_days = 1 then 'N+1'
                            when  
                                -- Lazada and doorstep intended
                                (parent_id_coalesce in (283987, 109059, 291101)
                                or tracking_id like ('NLSGD%')
                                or tracking_id like ('NLSGW%')
                                or tracking_id like ('NLSGCN%')
                                or tracking_id like ('NLXSG%')
                                or tracking_id like ('NLRSG%')) and dp_collect_intended_flag = 0 then 'N+1'
                            when
                                -- Lazada and Collect intended
                                (parent_id_coalesce in (283987, 109059, 291101)
                                or tracking_id like ('NLSGD%')
                                or tracking_id like ('NLSGW%')
                                or tracking_id like ('NLSGCN%')
                                or tracking_id like ('NLXSG%')
                                or tracking_id like ('NLRSG%')) and dp_collect_intended_flag = 1 then 'N+3' 
                            else 'N+2'
                        end as speed_service_type
                        , case
                            when is_measured_for_ttsr = 0 then null
                            when sla_days = 1 then 1
                            when 
                                -- Lazada and doorstep intended
                                (parent_id_coalesce in (283987, 109059, 291101)
                                or tracking_id like ('NLSGD%')
                                or tracking_id like ('NLSGW%')
                                or tracking_id like ('NLSGCN%')
                                or tracking_id like ('NLXSG%')
                                or tracking_id like ('NLRSG%')) and dp_collect_intended_flag = 0 then 1
                            when
                                -- Lazada and Collect intended
                                (parent_id_coalesce in (283987, 109059, 291101)
                                or tracking_id like ('NLSGD%')
                                or tracking_id like ('NLSGW%')
                                or tracking_id like ('NLSGCN%')
                                or tracking_id like ('NLXSG%')
                                or tracking_id like ('NLRSG%')) and dp_collect_intended_flag = 1 then 3 
                            else 2
                        end as speed_sla_days
                    from intermediate
                ),

                sla_date2 as (
                    select
                        base.*
                        , case
                            when base.speed_sla_days = 1 then local.next_working_day_1
                            when base.speed_sla_days = 2 then local.next_working_day_2
                            when base.speed_sla_days = 3 then local.next_working_day_3
                            else null
                        end as speed_sla_date
                    from basic_setup base
                    left join calendar local
                        on date(base.for_leg_start_clock) = local.next_working_day_0
                        and local.country = 'sg'
                ),

                completions_measured as (
                    select
                        *
                        , if (is_measured_for_ttsr = 1, delivery_success_datetime, null) as nc_end_clock
                        , case
                            when is_measured_for_ttsr = 0 then 0
                            when speed_sla_date > from_utc_timestamp(
                                '{{ measurement_datetime }}', '{{ local_timezone }}'
                            ) then 0
                            when speed_sla_date is null then 0
                            when granular_status = 'Cancelled' then 0
                            else 1
                        end as speed_sc_measured
                        , case
                            when is_measured_for_ttsr = 0 then 0
                            when speed_sla_date > from_utc_timestamp(
                                '{{ measurement_datetime }}', '{{ local_timezone }}'
                            ) then 0
                            when speed_sla_date is null then 0
                            when granular_status = 'Cancelled' then 0
                            when rts_flag = 1 then 0
                            else 1
                        end as speed_nc_measured
                    from sla_date2
                ),

                completions_met as (
                    select
                        *
                        , case
                            when speed_sc_measured = 0 then null
                            when speed_sc_measured = 1 and date(for_leg_end_clock) <= speed_sla_date then 1
                            else 0
                        end as speed_sc_met
                        , case
                            when speed_nc_measured = 0 then null
                            when speed_nc_measured = 1 and date(nc_end_clock) <= speed_sla_date then 1
                            else 0
                        end as speed_nc_met
                    from completions_measured
                ),

                final as (
                    select distinct
                        --Basic Information
                        order_id
                        , tracking_id
                        , third_party_tracking_id
                        , shipper_id
                        , parent_id_coalesce
                        , status
                        , granular_status
                        , parcel_size
                        , from_postcode
                        , to_postcode
                        , dest_postcode
                        , insurance_value
                        , delivery_type
                        , service_type
                        , sla_days
                        , to_country
                        , dest_hub_id
                        , dest_hub_region
                        , dest_hub_address_city
                        , dest_zone
                        , is_future_dated_order
                        , is_pickup_required
                        , is_dp_send_flag
                        , dp_collect_intended_flag
                        , is_dp_collect_flag
                        , is_domestic_flow
                        , is_nv_country_presence_flag
                        , is_nv_engaged_flag
                        , is_cancelled_flag
                        , sla_type
                        , return_sla_type_flag
                        , return_ndrtm_flag

                        -- FM related columns
                        , creation_datetime
                        , dp_dropoff_datetime
                        , dp_dropoff_dp_id
                        , scheduled_pickup_date
                        , first_pickup_attempt_datetime
                        , first_pickup_attempt_failure_reason_id
                        , pickup_datetime
                        , pickup_success_driver_id
                        , pickup_success_driver_name
                        , pickup_attempts

                        -- MM / Sort related columns
                        , hub_handover_datetime
                        , handover_hub_id
                        , inbound_datetime
                        , hub_inbound_datetime
                        , hub_inbound_hub_id
                        , hub_inbound_scanned_by_id
                        , hub_inbound_scanned_by_name
                        , route_inbound_datetime
                        , route_inbound_hub_id
                        , route_inbound_scanned_by_id
                        , route_inbound_scanned_by_name
                        , first_station_sweep_datetime
                        , first_station_sweep_hub_id
                        , first_station_sweep_scanned_by_id
                        , first_station_sweep_scanned_by_name
                        , dest_hub_sweep_datetime

                        -- LM related columns
                        , for_leg_van_inbound_datetime
                        , for_leg_van_inbound_hub_id
                        , third_party_transfer_datetime
                        , first_valid_delivery_attempt_datetime
                        , second_valid_delivery_attempt_datetime
                        , third_valid_delivery_attempt_datetime
                        , delivery_attempts
                        , driver_to_dp_datetime
                        , driver_to_dp_dpms_id
                        , delivery_success_datetime
                        , force_success_datetime
                        , force_success_flag

                        -- RTS related columns
                        , rts_flag
                        , rts_trigger_datetime
                        , rts_dest_hub_id
                        , rts_dest_zone
                        , rts_leg_van_inbound_datetime
                        , rts_leg_van_inbound_hub_id
                        , first_valid_rts_attempt_datetime
                        , last_valid_rts_attempt_datetime
                        , rts_attempts
                        , days_from_rts_trigger_to_first_rts_attempt
                        , days_from_rts_trigger_to_rts_success

                        --DP overstay related columns
                        , overstayed_flag
                        , overstayed_trigger_datetime
                        , overstayed_pickup_datetime

                        -- Forward leg SLA related columns
                        , for_leg_start_clock
                        , for_leg_start_clock_type
                        , for_leg_end_clock
                        , for_leg_sla_date
                        , for_leg_sla_timeslot_start
                        , for_leg_sla_timeslot_end
                        , for_leg_sla_measured
                        , for_leg_sla_date_met
                        , for_leg_sla_timeslot_met
                        , for_leg_sla_met

                        -- Speed related columns
                        , nc_end_clock
                        , speed_service_type
                        , speed_sla_days
                        , speed_sla_date
                        , speed_sc_measured
                        , speed_sc_met
                        , speed_nc_measured
                        , speed_nc_met

                        -- Misc
                        , created_month

                    from completions_met
                )

                select
                    *
                from final
                """,
                jinja_arguments={
                    "measurement_datetime": measurement_datetime,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SG_SLA_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("created_month",),
        system_id=system_id,
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()