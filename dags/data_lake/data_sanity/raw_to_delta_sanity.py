import json, logging
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.models.xcom import XCom
from airflow.operators.python import PythonOperator
from airflow.hooks.base import BaseHook
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.sensors.external_task import ExternalTaskSensor

from common.utils import alerts
from common.airflow import notifications as notif
from metadata.constants import Timeout
from metadata.data_count_sanity import DATA_COUNT_TABLES

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
ALERT_CHANNEL = "chat_testing_alert"
connection_object = BaseHook.get_connection('chat_testing_alert')
connection = connection_object.extra_dejson
connection['host'] = connection_object.host
env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 2, 17, 0, 0, 0),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": (
        lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
    )
    if env in ("prod", "dev")
    else None,
}

def get_and_validate_delta_table_metrics(**kwargs):
    """
    Fetches and validates delta table metrics from XCom.

    This function fetches delta table metrics from XCom, validates them against two conditions,
    and raises an alert and logs an error if either condition is not satisfied.

    Args:
        kwargs (dict): The context passed from the Airflow task. It should contain 'dag_run' key
                       which holds information about the dag run configuration.

    Returns:
        dict: The delta table metrics, including two new keys indicating whether each condition was satisfied.

    Raises:
        AirflowException: If either condition is not satisfied.
    """
    
    dag_run_conf = kwargs['dag_run'].conf
    logging.info(f"Received dag_run.conf: {dag_run_conf}")

    schema = dag_run_conf.get('schema')
    table = dag_run_conf.get('table')
    execution_date_str = dag_run_conf.get('execution_date')

    logging.info(f"Schema: {schema}, Table: {table}, Execution Date: {execution_date_str}")
    execution_date = datetime.fromisoformat(execution_date_str)

    delta_metrics = XCom.get_one(
        dag_id=f"datalake_cdc_{schema}",
        task_id=f"check_and_extract_delta_pii_{schema}_{table}",
        key=f"delta_metrics_{schema}_{table}",
        execution_date=execution_date,
    )

    logging.info(delta_metrics)
    for key, value in delta_metrics.items():
        logging.info(f"{key}: {value}")

    A, B, C, D, E, F = (
        delta_metrics["numTargetRowsCopied"],
        delta_metrics["numTargetRowsDeleted"],
        delta_metrics["numTargetRowsInserted"],
        delta_metrics["numTargetRowsUpdated"],
        delta_metrics["numOutputRows"],
        delta_metrics["numSourceRows"],
    )

    logging.info(f"numTargetRowsCopied: {A}, numTargetRowsDeleted: {B}, numTargetRowsInserted: {C}, numTargetRowsUpdated: {D}, numOutputRows: {E}, numSourceRows: {F}")

    condition_1 = A + C + D - B == E
    condition_2 = A + C + D == E

    if not (condition_1 or condition_2):
        message = (
            f"For {schema}.{table} at execution_date {execution_date}, the data counts do not match expected values:\n"
            f" The sum of copied {A}, inserted {C}, and updated rows {D}, minus deleted rows {B}, should equal the total output rows {E}. OR\n"
            f" The sum of copied {A}, inserted {C}, and updated rows {D}, should equal the total output rows {E}.\n"
            f"Can neglect this if no data mismatch message arrives for the same table at {execution_date}."
        )
        alerts.raise_gchat_alert(message, connection)
        logging.error(
            f"For {schema}.{table} at execution_date {execution_date}, the data counts do not match expected values:\n"
            f" The sum of copied {A}, inserted {C}, and updated rows {D}, minus deleted rows {B}, should equal the total output rows {E}. OR\n"
            f" The sum of copied {A}, inserted {C}, and updated rows {D}, should equal the total output rows {E}.\n"
            f"Can neglect this if no data mismatch message arrives for the same table at {execution_date}."
        )

    delta_metrics["condition_1_valid"] = condition_1
    delta_metrics["condition_2_valid"] = condition_2

    logging.info(delta_metrics)

    return json.dumps(delta_metrics)

with DAG(
    dag_id="raw_to_delta_sanity_dag",
    default_args=default_args,
    schedule_interval=None,
    max_active_runs=20,
    tags=["data_lake", "db_cdc", "sanity", "integrity", "raw_to_delta"],
    params={"alert_channel": ALERT_CHANNEL},
    start_date=datetime(2024, 5, 15),
    catchup=False,
) as dag:

    get_and_validate_delta_table_metrics_task = PythonOperator(
        task_id=f"get_and_validate_delta_table_metrics",
        python_callable=get_and_validate_delta_table_metrics,
        provide_context=True,
        execution_timeout=Timeout.ONE_HOUR,
    )

    compare_cdc_task_id = f"compare_raw_to_delta_count"
    compare_cdc = SparkSubmitOperator(
        task_id=compare_cdc_task_id,
        application=f"{tasks_path}/raw_to_delta_comparison.py",
        application_args=[
            env,
            "{{ dag_run.conf['schema'] }}",
            "{{ dag_run.conf['table'] }}",
            "{{ dag_run.conf['execution_date'] }}",
            json.dumps(connection),
            f"{{{{ ti.xcom_pull(dag_id='raw_to_delta_sanity_dag', task_ids='get_and_validate_delta_table_metrics') }}}}",
        ],
        execution_timeout=Timeout.ONE_HOUR,
        conn_id="spark_default",
        conf={
            "spark.executor.instances": "4",
            "spark.executor.memory": "8g",
            "spark.driver.memory": "2g",
            "spark.properties.file": "/opt/spark/conf/spark.properties",
        },
    )
    get_and_validate_delta_table_metrics_task >> compare_cdc