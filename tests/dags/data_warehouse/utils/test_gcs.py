import unittest

from data_warehouse.utils.gcs import _append_slash, _get_partition_to_value, get_uri_bucket_and_directory


def test_append_slash():
    test_cases = [
        (None, None),
        ("", ""),
        ("/", "/"),
        (" ", " /"),
        ("foo/", "foo/"),
        ("foo", "foo/"),
        ("foo/bar", "foo/bar/"),
        ("foo/bar/", "foo/bar/"),
    ]
    for tc in test_cases:
        output = _append_slash(tc[0])
        expected = tc[1]
        assert output == expected, f"append_slash('{tc[0]}') = '{output}' != '{expected}'"


class TestGetURIBucketAndDirectory(unittest.TestCase):
    def test_bucket(self):
        test_cases = [
            ("gs://foo/bar", "foo"),
            ("gs://foo/bar/", "foo"),
            ("gs://foo/bar/system_id=id", "foo"),
            ("gs://foo/bar/system_id=id/date=2020-03-01", "foo"),
        ]
        for tc in test_cases:
            output, _ = get_uri_bucket_and_directory(tc[0])
            expected = tc[1]
            self.assertEqual(output, expected, f"get_uri_bucket_and_directory('{tc[0]}') = '{output}' != '{expected}'")

    def test_directory(self):
        test_cases = [
            ("gs://foo/bar", "bar"),
            ("gs://foo/bar/", "bar"),
            ("gs://foo/bar/system_id=id", "bar/system_id=id"),
            ("gs://foo/bar/system_id=id/date=2020-03-01", "bar/system_id=id/date=2020-03-01"),
            ("gs://foo/bar.parquet", "bar.parquet"),
        ]
        for tc in test_cases:
            _, output = get_uri_bucket_and_directory(tc[0])
            expected = tc[1]
            self.assertEqual(output, expected, f"get_uri_bucket_and_directory('{tc[0]}') = '{output}' != '{expected}'")

    def test_exception(self):
        test_cases = ("gs://foo", "gs://foo/")
        for tc in test_cases:
            with self.assertRaises(ValueError):
                get_uri_bucket_and_directory(tc)


def test_get_partition_to_value():
    test_cases = [
        (
            (
                "gs://foo/bar/measurement_datetime=2020-01-01 18-00-00/system_id=id/created_month=2020-01",
                ["system_id", "measurement_datetime", "created_month"],
            ),
            {"measurement_datetime": "2020-01-01 18-00-00", "system_id": "id", "created_month": "2020-01"},
        ),
        (
            ("gs://foo/bar/measurement_datetime=2020-01-01 18-00-00/system_id=id/created_month=2020-01", ["system_id"]),
            {"system_id": "id"},
        ),
        (
            (
                "gs://foo/bar_measurement_datetime=2020-01-01 18-00-00/system_id=id/created_month=2020-01",
                ["created_month", "system_id"],
            ),
            {"system_id": "id", "created_month": "2020-01"},
        ),
        (
            (
                "gs://foo/bar_measurement_datetime=2020-01-01 18-00-00_system_id=id/created_month=2020-01",
                ["created_month"],
            ),
            {"created_month": "2020-01"},
        ),
    ]
    for tc in test_cases:
        output = _get_partition_to_value(*tc[0])
        expected = tc[1]
        assert output == expected
