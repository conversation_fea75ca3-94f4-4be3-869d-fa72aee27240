from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.stringcase import kebab_case
from metadata.long_retention_tables import LONG_RETENTION_DELTA_TABLES
from metadata.pii import SCHEMAS

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
mysql_tables = Variable.get("mysql_tables", deserialize_json=True, default_var={})
pii_schemas = SCHEMAS[env]
LONG_RETENTION_HOURS = 9360
DEFAULT_RETENTION_HOURS = 720


default_args = {
    "owner": "airflow",
    "start_date": "2020-08-05",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}


def create_vacuum_task(env, schema, table, retention_hours):
    task_id = f"pii_vacuum_delta_{schema}_{table}"
    try:
        vacuum_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/vacuum_delta.py",
            application_args=[env, schema, table, retention_hours],
            conn_id="spark_default",
            conf={
                "spark.kubernetes.driver.request.cores": "450m",
                "spark.driver.memory": "2500m",
                "spark.driver.cores": "4",
                "spark.kubernetes.executor.request.cores": "450m",
                "spark.executor.memory": "2500m",
                "spark.executor.cores": "4",
                "spark.executor.instances": "2",
                "spark.sql.shuffle.partitions": "200",
                "spark.databricks.delta.vacuum.parallelDelete.enabled": "true",
            },
        )
    except Exception as e:
        # Handle exception
        print(f"Error creating vacuum task: {e}")
        return None
    return vacuum_delta


with DAG(
    dag_id="datalake_delta_vacuum",
    default_args=default_args,
    schedule_interval="0 2 15 * *",  # 10AM SGT on 15th of the month
    catchup=False,
    concurrency=40,
    tags=["data_lake", "db_cdc"],
) as dag:
    # TODO: include other delta tables (e.g. zendesk)
    for schema, tables in mysql_tables.items():
        for table in tables:
            retention_hours = (
                LONG_RETENTION_HOURS 
                if (schema, table) in LONG_RETENTION_DELTA_TABLES
                else DEFAULT_RETENTION_HOURS
            )
            pii_delta_compact_task = create_vacuum_task(env, schema, table, str(retention_hours))
