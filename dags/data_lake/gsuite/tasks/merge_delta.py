import sys

from delta import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import coalesce, date_format, desc, row_number

from metadata.constants import LegacyGcsBucketUris
from metadata.gsuite import GSUITE_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

SYSTEM_ID = "system_id"
CREATED_AT = "created_at"
UPDATED_AT = "updated_at"
OBJECT_BUCKET = LegacyGcsBucketUris.raw


def get_delta_path(gcs_bucket, entity):
    """
    Returns path at which a G-suite entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      G-suite entity (e.g. users)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/gsuite/delta/{entity}"


def get_obj_path(env, entity):
    """
    Returns path at which incremental change data of a G-suite entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      G-suite entity (e.g. users)
    :return:            GCS path
    """
    return f"{OBJECT_BUCKET.format(env)}/gsuite/objects/{entity}"


def get_object(spark, entity, primary_column, path, date_str):
    """
    Fetches incremental change data of a G-suite entity for a specified updated date.

    :param spark:       Spark session
    :param entity:      G-suite entity
    :param path:        GCS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :return:            Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(coalesce(CREATED_AT, UPDATED_AT), month_fmt))
    )
    # get latest update by id
    updated_at = GSUITE_CONFIG[entity]["updated_at_field"]
    df = (
        df.withColumn(
            "row_number", row_number().over(Window.partitionBy(SYSTEM_ID, primary_column).orderBy(desc(updated_at)))
        )
        .filter("row_number = 1")
        .drop("row_number")
    )
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of G-suite entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)


def merge(spark, entity, primary_column, obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param spark:   Spark session
    :param entity:  G-suite entity
    :param obj_df:  Entity dataframe
    :param path:    Delta table path
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH])
        for row in obj_df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    updated_at = GSUITE_CONFIG[entity]["updated_at_field"]
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) "
            f"AND delta.{SYSTEM_ID} = cdc.{SYSTEM_ID} AND delta.{primary_column} = cdc.{primary_column}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{updated_at} >= delta.{updated_at}")
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_delta(spark, env, gcs_bucket, entity, date_str):
    primary_column = GSUITE_CONFIG[entity]["primary_column"]
    obj_path = get_obj_path(env, entity)
    obj_df = get_object(spark, entity, primary_column, obj_path, date_str)
    if obj_df.count() == 0:
        print("No records to update")
        return

    delta_path = get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, entity, primary_column, obj_df, delta_path)
    else:
        create(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, env, entity, date_str = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, env, gcs_bucket, entity, date_str)
    spark.stop()
