import sys

from pyspark.sql import SparkSession

from dateutil.relativedelta import relativedelta
from datetime import datetime as dt
from pyspark.sql.functions import current_timestamp, date_format, monotonically_increasing_id

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked
from pyspark.sql import functions as F
from pyspark.sql.functions import explode, col
from pyspark.sql.types import <PERSON>ruct<PERSON>ield, ArrayType, StringType, StructType,BooleanType


airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.SHIPPER_REF_DATA_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.SHIPPER_REF_DATA_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.OrdersDAG.Task.SHIPPER_REF_ADDRESSES_MASKED,
        data_warehouse.OrdersDAG.Task.SHIPPER_REF_PLATFORM_INFO_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(spark, env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True

    user_schema = ArrayType(
        StructType([
            StructField("collection_point", StringType(), True),
            StructField("packageType", StringType(), True),
            StructField("tplRegion", StringType(), True),
            StructField("tplName", StringType(), True),
            StructField("deliveryPriority", StringType(), True),
            StructField("packageOriginType", StringType(), True),
            StructField("portCode", StringType(), True),
            StructField("firstMileTplName", StringType(), True),
            StructField("firstMileTplSlug", StringType(), True),
            StructField("pickupType", StringType(), True),
            StructField("dimweight", StringType(), True),
            StructField("packageCode", StringType(), True),
            StructField("payment", StringType(), True)
        ])
    )
    dim_schema = ArrayType(
        StructType([
            StructField("height", StringType(), True),
            StructField("length", StringType(), True),
            StructField("volume", StringType(), True),
            StructField("weight", StringType(), True),
            StructField("width", StringType(), True)
        ])
    )


    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_REF_ADDRESSES,
                view_name="shipper_ref_addresses",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_REF_PLATFORM_INFO,
                view_name="shipper_ref_platform_info",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    end_month = measurement_datetime.strftime('%Y-%m')
    dte = dt.strptime(end_month, '%Y-%m').date()
    re = dte + relativedelta(months=-6)
    start_month = re.strftime('%Y-%m')

    orders = spark.read.format("delta").load(delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS)\
        .filter(F.col("created_month").between(start_month,end_month))\
        .select("id", "shipper_id", "tracking_id", "shipper_ref_metadata", "created_month", "created_at", "global_shipper_id")

    df1 = orders.withColumn("shipper_ref_metadata", F.from_json("shipper_ref_metadata", user_schema)) \
        .selectExpr("inline(shipper_ref_metadata)", "id", "shipper_id", "tracking_id", "created_month","collection_point",
                    "created_at", "global_shipper_id")

    ref_df = df1.select(col("id").alias("order_id"), "shipper_id", "tracking_id", "packageType", "tplRegion","collection_point",
                        "tplName", "deliveryPriority", "packageOriginType", "portCode", "firstMileTplName", "pickupType",
                        "dimweight", "created_month", "created_at", "global_shipper_id", "firstMileTplSlug", "packageCode",
                        "payment")

    dim_df = ref_df.withColumn("dim", F.from_json("dimweight", dim_schema)) \
        .selectExpr("inline(dim)", "*")

    final_df = dim_df.withColumn("timestamp", date_format(current_timestamp(), "yyyyMMddHHmmss")) \
        .withColumn("random_number", monotonically_increasing_id()) \
    .select("order_id", "shipper_id", "tracking_id", "packageType", "tplRegion", "tplName", "deliveryPriority","firstMileTplSlug",
            "packageOriginType", "portCode", "firstMileTplName", "pickupType", "packageCode", col("height").alias("dim_height"),
            col("length").alias("dim_length"), col("volume").alias("dim_volume"), col("weight").alias("dim_weight"),
            col("width").alias("dim_width"), "created_month", "created_at", "global_shipper_id", "timestamp","random_number"
            ,"collection_point", "payment")

    final_df.createOrReplaceTempView("output")

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                    SELECT 
                    case when '{{system_id}}' = 'id' then CONCAT(timestamp,random_number,a.order_id,1)
                    when '{{system_id}}' = 'mm' then CONCAT(timestamp,random_number,a.order_id,2)
                    when '{{system_id}}' = 'my' then CONCAT(timestamp,random_number,a.order_id,3)
                    when '{{system_id}}' = 'ph' then CONCAT(timestamp,random_number,a.order_id,4)
                    when '{{system_id}}' = 'sg' then CONCAT(timestamp,random_number,a.order_id,5)
                    when '{{system_id}}' = 'th' then CONCAT(timestamp,random_number,a.order_id,6)
                    when '{{system_id}}' = 'vn' then CONCAT(timestamp,random_number,a.order_id,7)
                    end as ID
                    , a.order_id 
                    , cast(a.global_shipper_id as int) as shipper_id
                    , shipper_ref_addresses.id as shipper_addresses_id
                    , shipper_ref_platform_info.id as platform_Info_id
                    , a.tracking_id 
                    , a.packageType
                    , a.tplRegion
                    , a.tplName
                    , a.deliveryPriority
                    , a.packageOriginType
                    , a.portCode
                    , a.firstMileTplName
                    , a.firstMileTplSlug
                    , a.collection_point
                    , a.pickupType
                    , a.dim_height
                    , a.dim_length
                    , a.dim_volume
                    , a.dim_weight 
                    , a.dim_width
                    , a.packageCode
                    , payment
                    , a.created_month 
                    , a.created_at
                    , get_json_object(a.payment, '$.shippingType') as shipping_type
                    , '{{system_id}}' as system_id
                    FROM output a
                    left join shipper_ref_addresses
                        on a.order_id = shipper_ref_addresses.order_id
                    left join shipper_ref_platform_info
                        on a.order_id = shipper_ref_platform_info.order_id
                    """,
                jinja_arguments={
                    "system_id": system_id,
                }
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPPER_REF_DATA,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config( spark,
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )


    run(spark, task_config)
    spark.stop()