import sys

from delta import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, row_number

from common.spark import pii
from metadata.constants import LegacyGcsBucketUris
from metadata.zendesk import ZENDESK_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

SYSTEM_ID = "system_id"
ID = "id"
OBJECT_BUCKET = LegacyGcsBucketUris.raw


def get_delta_path(gcs_bucket, entity):
    """
    Returns path at which Zendesk entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Zendesk entity (e.g. tickets)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/zendesk/delta/{entity}"


def get_obj_path(env, entity):
    """
    Returns path at which incremental change data of a Zendesk entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Zendesk entity (e.g. tickets)
    :return:            GCS path
    """
    return f"{OBJECT_BUCKET.format(env)}/zendesk/objects/{entity}"


def get_object(spark, path, date_str, created_at_field, pii_fields):
    """
    Fetches incremental change data of a Zendesk entity for a specified updated date.

    :param spark:       Spark session
    :param path:        GCS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :param pii_fields:  PII fields to be masked
    :return:            Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(created_at_field, month_fmt))
    )
    # get latest update by system_id, id
    updated_at = ZENDESK_CONFIG[entity]["updated_at_field"]
    df = (
        df.withColumn("row_number", row_number().over(Window.partitionBy(SYSTEM_ID, ID).orderBy(desc(updated_at))))
        .filter("row_number = 1")
        .drop("row_number")
    )
    if pii_fields:
        df = pii.mask(spark, df, pii_fields)
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Zendesk entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)


def merge(spark, obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param spark:   Spark session
    :param obj_df:  Entity dataframe
    :param path:    Delta table path
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH])
        for row in obj_df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    updated_at = ZENDESK_CONFIG[entity]["updated_at_field"]
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) "
            f"AND delta.{SYSTEM_ID} = cdc.{SYSTEM_ID} AND delta.{ID} = cdc.{ID}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{updated_at} > delta.{updated_at}")
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_delta(spark, env, gcs_bucket, entity, date_str, created_at_field, pii_fields):
    obj_path = get_obj_path(env, entity)
    obj_df = get_object(spark, obj_path, date_str, created_at_field, pii_fields)

    if obj_df.count() == 0:
        print("No records to update")
        return

    delta_path = get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, obj_df, delta_path)
    else:
        create(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, env, entity, date_str, created_at_field, pii_str = sys.argv[1:]
    pii_fields = [
        f.strip() for f in pii_str[(pii_str.index("[") + 1) : (pii_str.index("]"))].replace("'", "").split(",")
    ]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, env, gcs_bucket, entity, date_str, created_at_field, pii_fields)
    spark.stop()
