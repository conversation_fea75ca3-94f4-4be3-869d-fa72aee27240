import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_ID_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_ID_MASKED,
    system_ids=(constants.SystemID.ID,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FIRST_MILE_VOLUME_ORDERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.SHIPMENT_ORDERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.MiddleMileDAG.DAG_ID,
            task_id=data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.HUB_JOURNEYS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.DIM_WEIGHT_SCANS_BASE_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("cost_segment", "event_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 3, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MM_DRIVERS_ENRICHED,
                view_name="mm_drivers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DPS_ENRICHED,
                view_name="dps_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIRST_MILE_VOLUME_ORDERS,
                view_name="first_mile_volume_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MOVEMENT_TRIPS_ENRICHED,
                view_name="movement_trips_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENT_ORDERS_ENRICHED,
                view_name="shipment_orders_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MIDDLE_MILE_TRIP_RELATIONSHIPS,
                view_name="middle_mile_trip_relationships",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUB_JOURNEYS,
                view_name="hub_journeys",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DIM_WEIGHT_SCANS_BASE,
                view_name="dim_weight_scans_base",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_COST_CONFIG_ID,
                view_name="cost_card_cost_config_id",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_PRIMARY_TRIP_COST_CONFIG_ID,
                view_name="cost_card_primary_trip_cost_config_id",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_DRIVER_COST_CONFIG_ID,
                view_name="cost_card_driver_cost_config_id",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_VEHICLE_COST_CONFIG_ID,
                view_name="cost_card_vehicle_cost_config_id",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).COST_CARD_AIRSEA_COST_CONFIG_ID,
                view_name="cost_card_airsea_cost_config_id",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).TRIP_COST_TIERS,
                view_name="trip_cost_tiers",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_TAGS,
                view_name="order_tags",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="filtered_om",
                jinja_template="""

                select
                    order_milestones.order_id
                    , order_milestones.system_id
                    , least(coalesce(order_milestones.weight,0), 200) as weight
                    , order_milestones.parcel_size as size
                    , order_milestones.nv_width
                    , order_milestones.nv_height
                    , order_milestones.nv_length
                    , order_milestones.nv_weight
                    , order_milestones.pickup_datetime
                    , order_milestones.dp_dropoff_datetime
                    , order_milestones.inbound_datetime
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.delivery_success_datetime
                    , order_milestones.pickup_hub_id
                    , order_milestones.inbound_hub_id
                    , order_milestones.delivery_success_hub_id
                    , order_milestones.dest_hub_id
                    , order_milestones.rts_flag
                    , order_milestones.is_pickup_required
                    , order_milestones.third_party_tracking_id
                    , order_milestones.shipper_id
                    , shippers_enriched.parent_id_coalesce as shipper_parent_id_coalesce
                    , shippers_enriched.parent_name_coalesce as shipper_parent_name_coalesce
                    , shippers_enriched.sales_channel
                    , order_milestones.creation_datetime
                    , date_format(order_milestones.creation_datetime, 'yyyy-MM') as created_month
                from order_milestones
                left join shippers_enriched
                    on order_milestones.shipper_id = shippers_enriched.id
                where shippers_enriched.sales_channel != 'Test'

                """,
            ),
            base.TransformView(
                view_name="trip_orders",
                jinja_template="""
                with base as (
                
                    select distinct
                        trip_id
                        , order_id
                    from middle_mile_trip_relationships

                ),
                trip_orders as (
                    
                    select
                        filtered_om.system_id
                        , filtered_om.created_month
                        , base.trip_id
                        , base.order_id
                        , filtered_om.weight
                        , filtered_om.size
                    from base 
                    left join filtered_om
                        on base.order_id = filtered_om.order_id
                    where filtered_om.order_id is not null

                ),
                trip_orders_details as (

                    select
                        trip_orders.*
                        , coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.completion_datetime) as trip_datetime
                        , hubs_enriched.facility_type
                    from trip_orders
                    left join movement_trips_enriched
                        on trip_orders.trip_id = movement_trips_enriched.trip_id
                    left join hubs_enriched
                        on movement_trips_enriched.origin_hub_id = hubs_enriched.id

                ),
                crossdock_first_event as (
                
                    select
                        order_id
                        , min(trip_datetime) as first_crossdock_event_time
                    from trip_orders_details
                    where 
                        facility_type = 'CROSSDOCK'
                    group by 1

                ),
                final as (

                    select
                        trip_orders_details.*
                    from trip_orders_details

                    -- These First Mile legs to be assigned Middle Mile costs for the time being             
                    -- But this exclusion logic may be added back at a future date 

                    -- left join crossdock_first_event 
                    --     on trip_orders_details.order_id = crossdock_first_event.order_id      
                    -- where trip_orders_details.trip_datetime >= crossdock_first_event.first_crossdock_event_time
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="trip_details",
                jinja_template="""
                with trip_agg as (

                    select
                        movement_trips_enriched.system_id
                        , coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.created_datetime) as event_datetime
                        , date_format(coalesce(movement_trips_enriched.actual_start_datetime, movement_trips_enriched.created_datetime), 'yyyy-MM') as month
                        , movement_trips_enriched.trip_id
                        , movement_trips_enriched.origin_hub_id
                        , lower(movement_trips_enriched.origin_hub_region) as origin_hub_region
                        , movement_trips_enriched.dest_hub_id
                        , lower(movement_trips_enriched.dest_hub_region) as dest_hub_region
                        , movement_trips_enriched.linehaul_purpose_type
                        , movement_trips_enriched.vehicle_number
                        , movement_trips_enriched.primary_driver_id
                        , movement_trips_enriched.secondary_driver_id
                        , case
                            when (movement_trips_enriched.linehaul_purpose_type = 'primary' and movement_trips_enriched.movement_type = 'LAND_HAUL') then 'pri_land_trip'
                            when (movement_trips_enriched.linehaul_purpose_type = 'primary' and movement_trips_enriched.movement_type = 'AIR_HAUL') then 'pri_air_trip'
                            when (movement_trips_enriched.linehaul_purpose_type = 'primary' and movement_trips_enriched.movement_type = 'SEA_HAUL') then 'pri_sea_trip'
                            when (movement_trips_enriched.linehaul_purpose_type = 'secondary') then 'sec_trip'
                            end as type
                        , movement_trips_enriched.estimated_distance as distance
                        , movement_trips_enriched.total_order_volume
                        , movement_trips_enriched.expected_baseline_cbm as truck_capacity
                        , movement_trips_enriched.calculated_utilization
                        , sum(trip_orders.weight) as total_weight
                        , count(trip_orders.order_id) as total_orders
                    from movement_trips_enriched
                    left join trip_orders
                        on movement_trips_enriched.trip_id = trip_orders.trip_id
                    where movement_trips_enriched.status = 'COMPLETED'
                    group by {{ range(1, 18) | join(',') }}

                ),
                base as (
                
                    select 
                        trip_agg.*
                        , origin_hub.facility_type as origin_facility
                        , dest_hub.facility_type as dest_facility
                        , primary_driver.employment_type as primary_driver_employment_type
                        , primary_driver.vendor_id
                        , secondary_driver.employment_type as secondary_driver_employment_type
                    from trip_agg
                    left join hubs_enriched as origin_hub
                        on trip_agg.origin_hub_id = origin_hub.id
                    left join hubs_enriched as dest_hub
                        on trip_agg.dest_hub_id = dest_hub.id
                    left join mm_drivers_enriched as primary_driver
                        on trip_agg.primary_driver_id = primary_driver.driver_id
                    left join mm_drivers_enriched as secondary_driver
                        on trip_agg.secondary_driver_id = secondary_driver.driver_id

                ),
                final as (
                
                    select
                        *
                        , total_weight * distance as total_distance_weight
                    from base

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="pri_cost_config",
                jinja_template="""
                with route_trips_count as (
                
                    select
                        created_month
                        , cast(landhaul_vendor_id as integer) as landhaul_vendor_id
                        , cast(cost as double) as cost
                        , hub_1
                        , hub_2
                        , hub_3
                        , hub_4
                        , hub_5
                        , case when hub_1 != 'na' AND hub_2 != 'na' then 1 else 0 end
                            + case when hub_2 != 'na' AND hub_3 != 'na' then 1 else 0 end
                            + case when hub_3 != 'na' AND hub_4 != 'na' then 1 else 0 end
                            + case when hub_4 != 'na' AND hub_5 != 'na' then 1 else 0 end
                        as trip_count
                    from cost_card_primary_trip_cost_config_id

                ),
                route_trips_allocation as (
                    -- distribute the route level cost to all the component trips
                
                    select
                        created_month
                        , landhaul_vendor_id
                        , hub_1 as from_hub
                        , hub_2 as to_hub
                        , cost / cast(trip_count as double) as cost
                    from route_trips_count
                    where hub_1 != 'na' and hub_2 != 'na'

                    UNION ALL

                    select
                        created_month
                        , landhaul_vendor_id
                        , hub_2 as from_hub
                        , hub_3 as to_hub
                        , cost / cast(trip_count as double) as cost
                    from route_trips_count
                    where hub_2 != 'na' and hub_3 != 'na'

                    UNION ALL

                    select
                        created_month
                        , landhaul_vendor_id
                        , hub_3 as from_hub
                        , hub_4 as to_hub
                        , cost / cast(trip_count as double) as cost
                    from route_trips_count
                    where hub_3 != 'na' and hub_4 != 'na'

                    UNION ALL

                    select
                        created_month
                        , landhaul_vendor_id
                        , hub_4 as from_hub
                        , hub_5 as to_hub
                        , cost / cast(trip_count as double) as cost
                    from route_trips_count
                    where hub_4 != 'na' and hub_5 != 'na'

                ),
                final as (
                    -- O -> D should be equivalent to D -> O
                    -- smaller id always set as the from_hub
                
                    select
                        created_month
                        , landhaul_vendor_id
                        , case 
                            when cast(from_hub as bigint) <= cast(to_hub as bigint) then cast(from_hub as bigint) 
                            else cast(to_hub as bigint)
                        end as from_hub
                        , case when cast(from_hub as bigint) <= cast(to_hub as bigint) then cast(to_hub as bigint)
                            else cast(from_hub as bigint)
                        end as to_hub
                        , sum(cost) as cost
                    from route_trips_allocation
                    group by {{ range(1, 5) | join(',') }}

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="pri_trip_cpp",
                jinja_template="""
                with base as (
                
                    select
                        trip_id
                        , month
                        , vendor_id
                        , case 
                            when cast(origin_hub_id as bigint) <= cast(dest_hub_id as bigint) then cast(origin_hub_id as bigint) 
                            else cast(dest_hub_id as bigint)
                        end as from_hub
                        , case when cast(origin_hub_id as bigint) <= cast(dest_hub_id as bigint) then cast(dest_hub_id as bigint)
                            else cast(origin_hub_id as bigint)
                        end as to_hub
                        , total_distance_weight
                    from trip_details
                    where 
                        type = 'pri_land_trip'
                        and primary_driver_employment_type = 'Outsourced - Vendors'

                ),
                cost_per_trip_prep as (

                    select
                        pri_cost_config.created_month
                        , pri_cost_config.landhaul_vendor_id
                        , pri_cost_config.from_hub
                        , pri_cost_config.to_hub
                        , pri_cost_config.cost
                        , count(base.trip_id) as trip_count
                    from pri_cost_config
                    left join base
                        on pri_cost_config.created_month = base.month
                        and pri_cost_config.landhaul_vendor_id = base.vendor_id
                        and pri_cost_config.from_hub = base.from_hub
                        and pri_cost_config.to_hub = base.to_hub
                    group by {{ range(1, 6) | join(',') }}

                ),
                cost_per_trip as (
                
                    select
                        *
                        , cost / cast(trip_count as double) as cost_per_trip
                    from cost_per_trip_prep
                
                ),
                cost_per_kg as (
                
                    select
                        base.trip_id
                        , base.vendor_id
                        , cost_per_trip.cost_per_trip
                        , sum(base.total_distance_weight) as total_distance_weight
                    from base
                    left join cost_per_trip
                        on base.month = cost_per_trip.created_month
                        and base.vendor_id = cost_per_trip.landhaul_vendor_id
                        and base.from_hub = cost_per_trip.from_hub
                        and base.to_hub = cost_per_trip.to_hub
                    group by {{ range(1, 4) | join(',') }}
                
                ),
                final as (
                
                    select
                        trip_id
                        , vendor_id
                        , cost_per_trip / cast(total_distance_weight as double) as cost_per_kgm
                    from cost_per_kg
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="sec_trip_std_cpp",
                jinja_template="""
                with base as (
                
                    select
                        *
                        , case 
                            when origin_facility = 'CROSSDOCK' then origin_hub_id
                            when dest_facility = 'CROSSDOCK' then dest_hub_id
                            end as crossdock_hub_id
                    from trip_details
                    where type = 'sec_trip'
                        and (origin_facility = 'CROSSDOCK' or dest_facility = 'CROSSDOCK')
                        and primary_driver_employment_type != 'Outsourced - Vendors'

                ),
                order_count as (
                
                    select 
                        month
                        , crossdock_hub_id
                        , sum(total_distance_weight) as total_distance_weight
                    from base
                    group by {{ range(1, 3) | join(',') }}

                ),
                cpp as (
                
                    select
                        order_count.month
                        , order_count.crossdock_hub_id
                        , cast(cost_card_cost_config_id.cost as double) as cost
                        , cast(cost_card_cost_config_id.cost as double)/order_count.total_distance_weight as cpkgm
                    from order_count
                    left join cost_card_cost_config_id
                        on lower(cost_card_cost_config_id.cost_segment) = 'mm'
                        and lower(cost_card_cost_config_id.type) = 'secondary_internal'
                        and order_count.month = cost_card_cost_config_id.created_month
                        and order_count.crossdock_hub_id = cost_card_cost_config_id.hub_id

                ),
                final as (
                
                    select
                        base.trip_id
                        , base.primary_driver_employment_type
                        , cpp.cpkgm
                    from base
                    left join cpp
                        on base.month = cpp.month
                        and base.crossdock_hub_id = cpp.crossdock_hub_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="sec_trip_3pl_cpp",
                jinja_template="""
                with base as (
                
                    select
                        *
                    from trip_details
                    where type = 'sec_trip'
                        and primary_driver_employment_type = 'Outsourced - Vendors'

                ),
                order_count as (
                
                    select 
                        month
                        , origin_hub_region
                        , vendor_id
                        , sum(total_distance_weight) as total_distance_weight
                    from base
                    group by {{ range(1, 4) | join(',') }}

                ),
                cpp as (
                
                    select
                        order_count.month
                        , order_count.origin_hub_region
                        , order_count.vendor_id
                        , cast(cost_card_cost_config_id.cost as double) as cost
                        , cast(cost_card_cost_config_id.cost as double)/order_count.total_distance_weight as cpkgm
                    from order_count
                    left join cost_card_cost_config_id
                        on lower(cost_card_cost_config_id.cost_segment) = 'mm'
                        and lower(cost_card_cost_config_id.type) = 'secondary_3pl'
                        and order_count.month = cost_card_cost_config_id.created_month
                        and lower(order_count.origin_hub_region) = lower(cost_card_cost_config_id.region)
                        and order_count.vendor_id = cost_card_cost_config_id.ref_id

                ),
                final as (
                
                    select
                        base.trip_id
                        , base.primary_driver_employment_type
                        , cpp.cpkgm
                    from base
                    left join cpp
                        on base.month = cpp.month
                        and base.origin_hub_region = cpp.origin_hub_region
                        and base.vendor_id = cpp.vendor_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="vehicle_cost",
                jinja_template="""
                with base as (
                
                    select
                        month
                        , vehicle_number
                        , sum(total_distance_weight) as total_distance_weight
                    from trip_details
                    where primary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                        or secondary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                    group by {{ range(1, 3) | join(',') }}

                ),
                final as (
                
                    select
                        base.month
                        , base.vehicle_number
                        , cast(cost_card_vehicle_cost_config_id.cost as double) as cost
                        , cast(cost_card_vehicle_cost_config_id.cost as double)/base.total_distance_weight as cpkgm
                    from base
                    left join cost_card_vehicle_cost_config_id
                        on base.vehicle_number = cost_card_vehicle_cost_config_id.vehicle_number
                        and base.month = cost_card_vehicle_cost_config_id.created_month
                
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="driver_cost",
                jinja_template="""
                with primary_base as (
                
                    select
                        month
                        , primary_driver_id as driver_id
                        , sum(total_distance_weight) as total_distance_weight
                    from trip_details
                    where 
                        primary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                        or secondary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                    group by {{ range(1, 3) | join(',') }}

                ),
                secondary_base as (
                
                    select
                        month
                        , secondary_driver_id as driver_id
                        , sum(total_distance_weight) as total_distance_weight
                    from trip_details
                    where 
                        primary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                        or secondary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                    group by {{ range(1, 3) | join(',') }}

                ),
                combined_base as(
                
                    select month, driver_id, total_distance_weight from primary_base
                    UNION ALL
                    select month, driver_id, total_distance_weight from secondary_base

                ),
                combined as (
                
                    select
                        month
                        , driver_id
                        , sum(total_distance_weight) as total_distance_weight
                    from combined_base
                    group by {{ range(1, 3) | join(',') }}
                
                ),
                final as (
                
                    select
                        combined.month
                        , combined.driver_id
                        , cast(cost_card_driver_cost_config_id.cost as double) as cost
                        , cast(cost_card_driver_cost_config_id.cost as double)/combined.total_distance_weight as cpkgm
                    from combined
                    left join cost_card_driver_cost_config_id
                        on combined.driver_id = cost_card_driver_cost_config_id.driver_id
                        and combined.month = cost_card_driver_cost_config_id.created_month

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="waybill_cpp",
                jinja_template="""
                with waybill_details_base as (
                
                    select distinct
                        shipment_orders_enriched.system_id
                        , shipment_orders_enriched.order_id
                        , coalesce(shipments_enriched.mawb, shipments_enriched.swb) as ref_id
                    from shipment_orders_enriched
                    left join shipments_enriched
                        on shipment_orders_enriched.shipment_id = shipments_enriched.shipment_id
                    where coalesce(shipments_enriched.mawb, shipments_enriched.swb) is not null
                        and shipments_enriched.status = 'Completed'

                ),
                waybill_details as (
                
                    select
                        waybill_details_base.ref_id
                        , sum(order_milestones.weight) as total_weight
                        , count(order_milestones.order_id) as total_orders
                    from waybill_details_base
                    left join order_milestones
                        on waybill_details_base.order_id = order_milestones.order_id
                    group by 1

                ),
                final as (
                
                    select
                        waybill_details.ref_id
                        , waybill_details.total_weight
                        , waybill_details.total_orders
                        , cost_card_airsea_cost_config_id.cost
                        , cast(cost_card_airsea_cost_config_id.cost as double)/cast(waybill_details.total_weight as double) as cpkg
                        , cast(cost_card_airsea_cost_config_id.cost as double)/cast(waybill_details.total_orders as double) as cpp
                    from waybill_details
                    left join cost_card_airsea_cost_config_id
                        on waybill_details.ref_id = cost_card_airsea_cost_config_id.ref_id

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="fm_base",
                jinja_template="""
                with base as (

                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.rts_flag
                        , filtered_om.rts_trigger_datetime
                        , coalesce(filtered_om.pickup_datetime, filtered_om.inbound_datetime) as event_datetime
                        , lower(first_mile_volume_orders.first_scan_region) as region
                        , coalesce(filtered_om.pickup_hub_id, filtered_om.inbound_hub_id) as hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , case 
                            when filtered_om.pickup_datetime is not null then 'pickup'
                            when filtered_om.inbound_datetime is not null then 'inbound'
                            end as remarks
                        , first_mile_volume_orders.waypoint_id
                        , case 
                            when lower(first_mile_volume_orders.driver_type) like '%rider%' then '2w'
                            when lower(first_mile_volume_orders.driver_type) like '%driver%' then '4w'
                            else 'unknown'
                        end as vehicle_type
                        , dps_enriched.id as dp_id
                    from filtered_om
                    left join first_mile_volume_orders
                        on filtered_om.order_id = first_mile_volume_orders.order_id
                    left join dps_enriched
                        on first_mile_volume_orders.dp_dropoff_dp_id = dps_enriched.dpms_id

                ),
                month as (

                    select
                        base.*
                        , date(event_datetime) as event_day
                        , date_format(event_datetime, 'yyyy-MM') as event_month
                    from base

                ),
                final as (

                    select
                        month.*
                        , coalesce(cost_card_cost_config_id.type, 'non_mitra') as mitra_type
                    from month
                    left join cost_card_cost_config_id
                        on cost_card_cost_config_id.cost_segment = 'fm'
                        and cost_card_cost_config_id.type in ('mitra_dropoff', 'mitra_non_dropoff')
                        and month.dp_id = cost_card_cost_config_id.ref_id
                        and month.event_month = cost_card_cost_config_id.created_month

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="fm_waypoint",
                jinja_template="""
                with waypoint_base as (
                
                    select
                        *
                    from fm_base
                    where mitra_type != 'mitra_dropoff'
                        and waypoint_id is not null

                ),
                waypoint_hub_prep as (
                
                    select
                        date(event_datetime) as event_date
                        , waypoint_id
                        , vehicle_type
                        , hub_id
                        , count(*) as parcels
                        , min(event_datetime) as earliest_timestamp
                    from fm_base
                    group by {{ range(1, 5) | join(',') }}
                
                ),
                waypoint_hub_rank as (

                    select
                        event_date
                        , waypoint_id
                        , vehicle_type
                        , hub_id
                        , earliest_timestamp
                        , parcels
                        , row_number() over (
                                partition by (event_date, waypoint_id, vehicle_type) 
                                order by parcels desc, earliest_timestamp) 
                        as ranked
                    from waypoint_hub_prep
                
                ),
                waypoint_hub as (

                    select
                        event_date
                        , waypoint_id
                        , vehicle_type
                        , min_by(hub_id, ranked) as hub_id
                        , sum(parcels) as parcel_count
                    from waypoint_hub_rank
                    group by {{ range(1, 4) | join(',') }}

                ),
                allocation_month_prep as (
                
                    select
                        date_format(event_date, 'yyyy-MM') as event_month
                        , vehicle_type
                        , hub_id
                        , count(distinct (event_date, waypoint_id)) as waypoint_day_count
                    from waypoint_hub
                    group by {{ range(1, 4) | join(',') }}

                ),
                allocation_month as (
                
                    select
                        allocation_month_prep.event_month
                        , allocation_month_prep.vehicle_type
                        , allocation_month_prep.hub_id
                        , allocation_month_prep.waypoint_day_count
                        , cost_card_cost_config_id.cost
                        , cost_card_cost_config_id.cost / cast(allocation_month_prep.waypoint_day_count as double) as cpwd
                    from allocation_month_prep
                    left join cost_card_cost_config_id
                        on lower(cost_card_cost_config_id.cost_segment) = 'fm'
                        and allocation_month_prep.vehicle_type = lower(cost_card_cost_config_id.type)
                        and allocation_month_prep.event_month = cost_card_cost_config_id.created_month
                        and allocation_month_prep.hub_id = cost_card_cost_config_id.hub_id

                ),
                allocation_waypoint_day as (
                
                    select
                        allocation_month.event_month
                        , waypoint_hub.event_date
                        , waypoint_hub.waypoint_id
                        , waypoint_hub.vehicle_type
                        , waypoint_hub.hub_id
                        , waypoint_hub.parcel_count
                        , case
                            when parcel_count = 1 then 'a. 1 Parcel'
                            when parcel_count <= 5 then 'b. 2 to 5 Parcels'
                            when parcel_count <= 10 then 'c. 5 to 10 Parcels'
                            when parcel_count <= 15 then 'd. 10 to 15 Parcels'
                            when parcel_count > 15 then 'e. >15 Parcels'
                            else 'problem'
                            end as group
                        , allocation_month.cpwd
                        , allocation_month.cpwd / cast(waypoint_hub.parcel_count as double) as cpp
                    from waypoint_hub
                    left join allocation_month
                        on date_format(waypoint_hub.event_date, 'yyyy-MM') = allocation_month.event_month
                        and waypoint_hub.vehicle_type = allocation_month.vehicle_type
                        and waypoint_hub.hub_id = allocation_month.hub_id

                ),
                final as (
                
                    select
                        waypoint_base.order_id
                        , waypoint_base.created_month
                        , waypoint_base.system_id
                        , 'fm' as cost_segment
                        , waypoint_base.event_datetime
                        , waypoint_base.event_month as month
                        , waypoint_base.region as origin_hub_region
                        , waypoint_base.hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , waypoint_base.weight
                        , waypoint_base.size
                        , waypoint_base.vehicle_type as type
                        , allocation_waypoint_day.cpp as cost
                        , waypoint_base.remarks
                        , waypoint_base.waypoint_id as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from waypoint_base
                    left join allocation_waypoint_day
                        on waypoint_base.waypoint_id = allocation_waypoint_day.waypoint_id
                        and waypoint_base.vehicle_type = allocation_waypoint_day.vehicle_type
                        and waypoint_base.event_day = allocation_waypoint_day.event_date

                )
                    
            select * from final

                """,
            ),
            base.TransformView(
                view_name="fm_parcel",
                jinja_template="""
                with implant_base as (
                
                    select
                        *
                    from fm_base
                    where mitra_type = 'non_mitra'

                ),
                implant_prep as (

                    select
                        implant_base.*
                        , cost_card_cost_config_id.cost as group_cost
                    from implant_base
                    left join cost_card_cost_config_id
                        on lower(cost_card_cost_config_id.cost_segment) = 'fm'
                        and lower(cost_card_cost_config_id.type) = 'implant'
                        and implant_base.hub_id = cost_card_cost_config_id.hub_id
                        and implant_base.event_month = cost_card_cost_config_id.created_month
                    where cost_card_cost_config_id.hub_id is not null

                ),
                implant_hub_cpp as (
                
                    select
                        event_month
                        , hub_id
                        , max(group_cost) / cast(count(distinct order_id) as double) as cpp
                    from implant_prep
                    group by {{ range(1, 3) | join(',') }}
                
                ),
                implant as (

                    select
                        implant_prep.*
                        , 'implant' as type
                        , null as ref_id
                        , implant_hub_cpp.cpp
                    from implant_prep
                    left join implant_hub_cpp
                        on implant_prep.hub_id = implant_hub_cpp.hub_id
                        and implant_prep.event_month = implant_hub_cpp.event_month

                ),
                mitra_base as (
                
                    select
                        *
                    from fm_base
                    where mitra_type in ('mitra_dropoff', 'mitra_non_dropoff')

                ),
                mitra_prep as (

                    select
                        mitra_base.*
                        , cost_card_cost_config_id.cost as group_cost
                    from mitra_base
                    left join cost_card_cost_config_id
                        on lower(cost_card_cost_config_id.cost_segment) = 'fm'
                        and mitra_base.mitra_type = lower(cost_card_cost_config_id.type)
                        and mitra_base.dp_id = cost_card_cost_config_id.ref_id
                        and mitra_base.event_month = cost_card_cost_config_id.created_month

                ),
                mitra_dp_cpp as (
                
                    select
                        event_month
                        , dp_id
                        , max(group_cost) / cast(count(distinct order_id) as double) as cpp
                    from mitra_prep
                    group by {{ range(1, 3) | join(',') }}
                
                ),
                mitra as (

                    select
                        mitra_prep.*
                        , mitra_prep.mitra_type as type
                        , mitra_prep.dp_id as ref_id
                        , mitra_dp_cpp.cpp
                    from mitra_prep
                    left join mitra_dp_cpp
                        on mitra_prep.dp_id = mitra_dp_cpp.dp_id
                        and mitra_prep.event_month = mitra_dp_cpp.event_month
                ),
                union as (
                
                    select * from implant
                    UNION ALL
                    select * from mitra
                
                ),
                final as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'fm' as cost_segment
                        , event_datetime
                        , event_month as month
                        , region as origin_hub_region
                        , hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , weight
                        , size
                        , type
                        , cpp as cost
                        , remarks
                        , ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from union

                )
                    
            select * from final

                """,
            ),
            base.TransformView(
                view_name="fm_costs",
                jinja_template="""

                select * from fm_waypoint
                UNION ALL
                select * from fm_parcel

                """,
            ),
            base.TransformView(
                view_name="lm_costs",
                jinja_template="""
                with latest_dws as (
                
                    select
                        order_id
                        , max_by(new_parcel_size, scan_datetime) as latest_size
                        , max_by(new_width, scan_datetime) as latest_width
                        , max_by(new_height, scan_datetime) as latest_height
                        , max_by(new_length, scan_datetime) as latest_length
                        , max_by(new_weight, scan_datetime) as latest_weight
                    from dim_weight_scans_base
                    group by 1

                ),
                lm_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.delivery_success_datetime as event_datetime
                        , filtered_om.sales_channel
                        , coalesce(delivery_success_hub.region, dest_hub.region) as region
                        , coalesce(filtered_om.delivery_success_hub_id, filtered_om.dest_hub_id) as hub_id
                        , coalesce(latest_dws.latest_size, filtered_om.size) as size
                        , coalesce(latest_dws.latest_width, filtered_om.nv_width) as width
                        , coalesce(latest_dws.latest_height, filtered_om.nv_height) as height
                        , coalesce(latest_dws.latest_length, filtered_om.nv_length) as length
                        , coalesce(latest_dws.latest_weight, filtered_om.nv_weight) as weight
                        , filtered_om.rts_flag
                        , case when order_tags.order_id is not null then 1 else 0 end as bulky_revise_flag
                        , case 
                            when filtered_om.delivery_success_hub_id is not null then 'delivery' 
                            when filtered_om.dest_hub_id is not null then 'proxy'
                            end as remarks
                    from filtered_om
                    left join hubs_enriched as delivery_success_hub
                        on filtered_om.delivery_success_hub_id = delivery_success_hub.id
                    left join hubs_enriched as dest_hub
                        on filtered_om.dest_hub_id = dest_hub.id
                    left join latest_dws
                        on filtered_om.order_id = latest_dws.order_id
                    left join order_tags
                        on filtered_om.order_id = order_tags.order_id
                        and order_tags.tag_id = 97
                    where
                        filtered_om.delivery_success_datetime is not null
                        and filtered_om.third_party_tracking_id is null

                ),
                lm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'lm' as cost_segment
                        , 'not applicable' as origin_hub_region
                        , 'not applicable' as origin_hub_id
                        , region as dest_hub_region
                        , hub_id as dest_hub_id
                        , weight
                        , size
                        , case
                            when rts_flag = 1 
                                and size in ('xs','s','m') then 'rts regular'
                            when rts_flag = 1 
                                and size in ('l','xl','xxl') then 'rts bulky'
                            when rts_flag = 0 
                                and (width > 50 or height > 50 or length >50 or weight >5) then 'superbulky'
                            when rts_flag = 0 
                                and bulky_revise_flag = 1 then 'bulky revise'
                            when rts_flag = 0 
                                and lower(sales_channel) not in ('field sales', 'corp sales', 'retail') 
                                and size in ('xs', 's', 'm') then 'standard regular'
                            when rts_flag = 0 
                                and lower(sales_channel) not in ('field sales', 'corp sales', 'retail') 
                                and size in ('l', 'xl', 'xxl') then 'standard bulky'
                            when rts_flag = 0 
                                and lower(sales_channel) in ('field sales', 'corp sales', 'retail') 
                                and size in ('xs', 's', 'm') then 'non standard regular'
                            when rts_flag = 0 
                                and lower(sales_channel) in ('field sales', 'corp sales', 'retail') 
                                and size in ('l', 'xl', 'xxl') then 'non standard bulky'
                        end as type
                        , remarks
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from lm_base

                ),
                final as (
                
                    select
                        lm_events.order_id
                        , lm_events.created_month
                        , lm_events.system_id
                        , lm_events.cost_segment
                        , lm_events.event_datetime
                        , lm_events.month
                        , lm_events.origin_hub_region
                        , lm_events.origin_hub_id
                        , lm_events.dest_hub_region
                        , lm_events.dest_hub_id
                        , lm_events.weight
                        , lm_events.size
                        , lm_events.type
                        , cost_card_cost_config_id.cost
                        , lm_events.remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from lm_events
                    left join cost_card_cost_config_id
                        on lm_events.cost_segment = lower(cost_card_cost_config_id.cost_segment)
                        and lm_events.dest_hub_id = cost_card_cost_config_id.hub_id
                        and lm_events.type = lower(cost_card_cost_config_id.type)
                        and lm_events.month = cost_card_cost_config_id.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="sort_costs",
                jinja_template="""
                with sort_events as (
                
                    select
                        hub_journeys.order_id
                        , filtered_om. created_month
                        , hub_journeys.system_id
                        , 'sort' as cost_segment
                        , hub_journeys.event_datetime
                        , date_format(hub_journeys.event_datetime, 'yyyy-MM') as month
                        , hub_journeys.region as origin_hub_region
                        , hub_journeys.coalesce_hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , 'not applicable' as type
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null
                        and hub_journeys.facility_type = 'CROSSDOCK'

                ),
                final as (
                
                    select
                        sort_events.order_id
                        , sort_events.created_month
                        , sort_events.system_id
                        , sort_events.cost_segment
                        , sort_events.event_datetime
                        , sort_events.month
                        , sort_events.origin_hub_region
                        , sort_events.origin_hub_id
                        , sort_events.dest_hub_region
                        , sort_events.dest_hub_id
                        , sort_events.weight
                        , sort_events.size
                        , sort_events.type
                        , cost_card_cost_config_id.cost
                        , cost_card_cost_config_id.remarks
                        ,'not_applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from sort_events
                    left join cost_card_cost_config_id
                        on sort_events.cost_segment = lower(cost_card_cost_config_id.cost_segment)
                        and sort_events.origin_hub_id = cost_card_cost_config_id.hub_id
                        and sort_events.month = cost_card_cost_config_id.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="mm_costs",
                jinja_template="""
                with mm_base as (
                
                    select
                        trip_orders.order_id
                        , trip_orders.created_month
                        , trip_orders.system_id
                        , trip_orders.weight
                        , trip_orders.size
                        , trip_details.trip_id
                        , trip_details.event_datetime
                        , trip_details.origin_hub_region
                        , trip_details.origin_hub_id
                        , trip_details.dest_hub_region
                        , trip_details.dest_hub_id
                        , trip_details.type
                        , trip_details.distance
                        , trip_details.total_order_volume
                        , trip_details.truck_capacity
                        , trip_details.calculated_utilization
                        , trip_details.vehicle_number
                        , trip_details.origin_facility
                        , trip_details.dest_facility
                        , trip_details.primary_driver_id
                        , trip_details.primary_driver_employment_type
                        , trip_details.secondary_driver_id
                        , trip_details.secondary_driver_employment_type
                    from trip_orders
                    left join trip_details
                        on trip_orders.trip_id = trip_details.trip_id

                ),
                mm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'mm' as cost_segment
                        , trip_id
                        , origin_hub_region
                        , origin_hub_id
                        , dest_hub_region
                        , dest_hub_id
                        , weight
                        , size
                        , type
                        , distance
                        , total_order_volume
                        , truck_capacity
                        , calculated_utilization
                        , vehicle_number
                        , origin_facility
                        , dest_facility
                        , primary_driver_id
                        , primary_driver_employment_type
                        , secondary_driver_id
                        , secondary_driver_employment_type
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from mm_base
                    where event_datetime is not null

                ),
                mm_stitched_route_costs as (
                
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , mm_events.type
                        , mm_events.weight * mm_events.distance * pri_trip_cpp.cost_per_kgm as cost
                        , pri_trip_cpp.vendor_id as remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join pri_trip_cpp
                        on mm_events.trip_id = pri_trip_cpp.trip_id
                    where mm_events.type = 'pri_land_trip'
                        and (mm_events.primary_driver_employment_type = 'Outsourced - Vendors' 
                            or mm_events.secondary_driver_employment_type = 'Outsourced - Vendors')

                ),
                mm_waybill_shipments as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.weight
                        , filtered_om.size
                        , coalesce(shipments_enriched.mawb, shipments_enriched.swb) as ref_id
                        , coalesce(shipments_enriched.mawb_vendor_id, shipments_enriched.swb_vendor_id) as vendor_id
                        , coalesce(shipments_enriched.mawb_vendor_name, shipments_enriched.swb_vendor_name) as vendor_name
                        , case 
                            when shipments_enriched.mawb is not null then 'awb'
                            when shipments_enriched.swb is not null then 'swb'
                            end as type
                        , coalesce(shipments_enriched.orig_shipment_close_datetime, shipments_enriched.shipment_completion_datetime) as event_datetime
                        , shipments_enriched.orig_hub_region as origin_hub_region
                        , shipments_enriched.orig_hub_id as origin_hub_id
                        , shipments_enriched.dest_hub_region
                        , shipments_enriched.dest_hub_id
                    from filtered_om
                    left join shipment_orders_enriched
                        on filtered_om.order_id = shipment_orders_enriched.order_id
                    left join shipments_enriched
                        on shipment_orders_enriched.shipment_id = shipments_enriched.shipment_id
                    where coalesce(shipments_enriched.mawb, shipments_enriched.swb) is not null
                        and shipments_enriched.status = 'Completed'

                ),
                mm_waybill_shipments_costs as (
                
                    select
                        mm_waybill_shipments.order_id
                        , mm_waybill_shipments.created_month
                        , mm_waybill_shipments.system_id
                        , 'mm' as cost_segment
                        , mm_waybill_shipments.event_datetime
                        , date_format(mm_waybill_shipments.event_datetime, 'yyyy-MM') as month
                        , mm_waybill_shipments.origin_hub_region
                        , mm_waybill_shipments.origin_hub_id
                        , mm_waybill_shipments.dest_hub_region
                        , mm_waybill_shipments.dest_hub_id
                        , mm_waybill_shipments.weight
                        , mm_waybill_shipments.size
                        , case 
                            when mm_waybill_shipments.type = 'awb' then 'pri_air_trip'
                            when mm_waybill_shipments.type = 'swb' then 'pri_sea_trip'
                            end as type
                        , waybill_cpp.cpkg * mm_waybill_shipments.weight as cost
                        , mm_waybill_shipments.vendor_id as remarks
                        , mm_waybill_shipments.ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from mm_waybill_shipments
                    left join waybill_cpp
                        on mm_waybill_shipments.ref_id = waybill_cpp.ref_id

                ),
                mm_sec_std_costs as (
                
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , 'secondary' as type
                        , mm_events.weight * mm_events.distance * sec_trip_std_cpp.cpkgm as cost
                        , sec_trip_std_cpp.primary_driver_employment_type as remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join sec_trip_std_cpp
                        on mm_events.trip_id = sec_trip_std_cpp.trip_id
                    where 
                        mm_events.type = 'sec_trip'
                        and (mm_events.origin_facility = 'CROSSDOCK' or mm_events.dest_facility = 'CROSSDOCK')
                        and mm_events.primary_driver_employment_type != 'Outsourced - Vendors'

                ),
                mm_sec_3pl_costs as (
                
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , 'sec_trip' as type
                        , mm_events.weight * mm_events.distance * sec_trip_3pl_cpp.cpkgm as cost
                        , sec_trip_3pl_cpp.primary_driver_employment_type as remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join sec_trip_3pl_cpp
                        on mm_events.trip_id = sec_trip_3pl_cpp.trip_id
                    where 
                        mm_events.type = 'sec_trip'
                        and mm_events.primary_driver_employment_type = 'Outsourced - Vendors' 
                ),
                mm_vehicle_costs as (

                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , mm_events.type
                        , mm_events.weight * mm_events.distance * vehicle_cost.cpkgm as cost
                        , 'vehicle_costs' as remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join vehicle_cost
                        on mm_events.vehicle_number = vehicle_cost.vehicle_number
                        and mm_events.month = vehicle_cost.month
                    where 
                        mm_events.primary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                        or mm_events.secondary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')

                ),
                mm_driver_costs as (
                
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , mm_events.type
                        , coalesce((mm_events.weight * mm_events.distance * primary_driver_costs.cpkgm),0)
                            + coalesce((mm_events.weight * mm_events.distance * secondary_driver_costs.cpkgm),0) as cost
                        , 'driver_costs' as remarks
                        , mm_events.trip_id as ref_id
                        , mm_events.distance as utilization_weight
                        , mm_events.total_order_volume as utilization_numerator
                        , mm_events.truck_capacity as utilization_denominator
                        , mm_events.calculated_utilization as utilization_rate
                    from mm_events
                    left join driver_cost as primary_driver_costs
                        on mm_events.primary_driver_id = primary_driver_costs.driver_id
                        and mm_events.month = primary_driver_costs.month
                    left join driver_cost as secondary_driver_costs
                        on mm_events.secondary_driver_id = secondary_driver_costs.driver_id
                        and mm_events.month = secondary_driver_costs.month
                    where 
                        mm_events.primary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')
                        or mm_events.secondary_driver_employment_type in ('In-House - Full-Time','In-House - Part-Time')

                ),
                final as (
                
                    select * from mm_stitched_route_costs
                    UNION ALL
                    select * from mm_waybill_shipments_costs
                    UNION ALL
                    select * from mm_sec_std_costs
                    UNION ALL
                    select * from mm_sec_3pl_costs
                    UNION ALL
                    select * from mm_vehicle_costs
                    UNION ALL
                    select * from mm_driver_costs

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="hub_costs",
                jinja_template="""
                with hub_throughput_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.weight
                        , filtered_om.size
                        , hub_journeys.event_datetime
                        , hub_journeys.coalesce_hub_id
                        , hub_journeys.region
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null

                ),
                hub_throughput_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'hub' as cost_segment
                        , region as origin_hub_region
                        , coalesce_hub_id as origin_hub_id
                        , 'not applicable' as dest_hub_region
                        , 'not applicable' as dest_hub_id
                        , weight
                        , size
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from hub_throughput_base

                ),
                hub_throughput as (
                
                    select
                        hub_throughput_events.order_id
                        , hub_throughput_events.created_month
                        , hub_throughput_events.system_id
                        , hub_throughput_events.cost_segment
                        , hub_throughput_events.event_datetime
                        , hub_throughput_events.month
                        , hub_throughput_events.origin_hub_region
                        , hub_throughput_events.origin_hub_id
                        , hub_throughput_events.dest_hub_region
                        , hub_throughput_events.dest_hub_id
                        , hub_throughput_events.weight
                        , hub_throughput_events.size
                        , cost_card_cost_config_id.remarks as type
                        , cost_card_cost_config_id.cost
                        , hubs_enriched.facility_type as remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from hub_throughput_events
                    left join cost_card_cost_config_id
                        on hub_throughput_events.cost_segment = lower(cost_card_cost_config_id.cost_segment)
                        and lower(cost_card_cost_config_id.type) = 'throughput'
                        and hub_throughput_events.origin_hub_id = cost_card_cost_config_id.hub_id
                        and hub_throughput_events.month = cost_card_cost_config_id.created_month
                    left join hubs_enriched
                        on hub_throughput_events.origin_hub_id = hubs_enriched.id
                
                ),
                hub_parcel_base as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , weight
                        , size
                        , pickup_datetime as event_datetime
                        , pickup_hub_id as hub_id
                    from filtered_om
                    where pickup_hub_id is not null

                ),
                hub_parcel_count as (
                
                    select
                        date_format(event_datetime, 'yyyy-MM') as month
                        , hub_id
                        , count(*) as parcels
                    from hub_parcel_base
                    group by {{ range(1, 3) | join(',') }}
                
                ),
                hub_parcel_allocation as (

                    select
                        hub_parcel_count.month
                        , hub_parcel_count.hub_id
                        , hub_parcel_count.parcels
                        , cost_card_cost_config_id.cost / cast(hub_parcel_count.parcels as double) as cpp
                        , cost_card_cost_config_id.remarks
                    from hub_parcel_count
                    left join cost_card_cost_config_id
                        on lower(cost_card_cost_config_id.cost_segment) = 'hub'
                        and lower(cost_card_cost_config_id.type) = 'parcels'
                        and hub_parcel_count.hub_id = cost_card_cost_config_id.hub_id
                        and hub_parcel_count.month = cost_card_cost_config_id.created_month

                ),
                hub_parcel_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'hub' as cost_segment
                        , hub_id as origin_hub_id
                        , 'not applicable' as dest_hub_region
                        , 'not applicable' as dest_hub_id
                        , weight
                        , size
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from hub_parcel_base

                ),
                hub_parcel as (
                
                    select
                        hub_parcel_events.order_id
                        , hub_parcel_events.created_month
                        , hub_parcel_events.system_id
                        , hub_parcel_events.cost_segment
                        , hub_parcel_events.event_datetime
                        , hub_parcel_events.month
                        , hubs_enriched.region as origin_hub_region
                        , hub_parcel_events.origin_hub_id
                        , hub_parcel_events.dest_hub_region
                        , hub_parcel_events.dest_hub_id
                        , hub_parcel_events.weight
                        , hub_parcel_events.size
                        , hub_parcel_allocation.remarks as type
                        , hub_parcel_allocation.cpp as cost
                        , hubs_enriched.facility_type as remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from hub_parcel_events
                    left join hub_parcel_allocation
                        on hub_parcel_events.month = hub_parcel_allocation.month
                        and hub_parcel_events.origin_hub_id = hub_parcel_allocation.hub_id
                    left join hubs_enriched
                        on hub_parcel_events.origin_hub_id = hubs_enriched.id
                
                ),
                final as (

                    select * from hub_throughput
                    UNION ALL
                    select * from hub_parcel

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with combined_costs as (
                
                    select * from fm_costs
                    UNION ALL
                    select * from lm_costs
                    UNION ALL
                    select * from sort_costs
                    UNION ALL
                    select * from mm_costs
                    UNION ALL
                    select * from hub_costs

                ),
                sequence as (
                    select 
                        *
                        , row_number() over (partition by order_id order by event_datetime, cost_segment, type) as event_sequence
                    from combined_costs

                ),
                final as (
                
                    select 
                        system_id
                        , order_id
                        , event_sequence
                        , event_datetime
                        , lower(origin_hub_region) as origin_hub_region
                        , cast(origin_hub_id as int) as origin_hub_id
                        , lower(dest_hub_region) as dest_hub_region
                        , cast(dest_hub_id as int) as dest_hub_id
                        , weight
                        , lower(size) as size
                        , lower(type) as type
                        , cast(cost as double) as cost
                        , lower(remarks) as remarks
                        , ref_id
                        , utilization_weight
                        , utilization_numerator
                        , utilization_denominator
                        , utilization_rate
                        , lower(cost_segment) as cost_segment
                        , month as event_month
                        , month as created_month
                        , created_month as order_created_month
                    from sequence
                    where month >= '2023-01' 
                    order by {{ range(1, 4) | join(',') }}

                )

                select * from final
                
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COST_CARD_EVENTS_ID,
        measurement_datetime=measurement_datetime,
        partition_by=("cost_segment", "event_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
