import logging
from datetime import datetime, timedelta

from airflow import DAG
from airflow.models import Variable
from airflow.operators.bash import Ba<PERSON><PERSON>perator
from airflow.operators.python import PythonOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.google.cloud.sensors.gcs import GCSObjectsWithPrefixExistenceSensor

from common.airflow import notifications as notif
from common.utils.gcs import get_nv_data_bucket_uri
from metadata.constants import Timeout
from metadata.ticdc import TICDC_SCHEMAS
from metadata.cdc_data_integrity import DB_MW_REPLICATION,MW_DB_MAPPING

INTERVAL_DURATION = 60
ALERT_CHANNEL = "high_priority_alert"

env = Variable.get("env")
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)
num_intervals = INTERVAL_DURATION // 15

default_args = {
    "owner": "airflow",
    "start_date": datetime(2020, 1, 17, 0, 0, 0),
    "retries": 3,
    "retry_delay": timedelta(minutes=2),
    "executor_config": {"KubernetesExecutor": {"request_memory": "128Mi"}},
    "priority_weight": 10,
    "pool": "sensor_pool",
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

def raise_maxwell_alert(**kwargs):
    '''
    if we dont have data for a hour at maxwell instance replication level, grafana alert shall be raised
    '''
    disable_schemas = ["data-prod-global-data-engr-misc-gl"]
    mw_instance = kwargs['mw_instance']
    schemas = DB_MW_REPLICATION.get(mw_instance)
    instance_down = 0
    for schema in schemas:
        tbls = kwargs['task_instance'].xcom_pull(task_ids=f'{schema}.list_tables_with_changes', key='return_value')
        print(tbls)
        if tbls is None or len(tbls) == 0:
            instance_down += 1
            print(f"{'No tables returned from upstream for schema: {schema}:{mw_instance}'.format(schema=schema,mw_instance=mw_instance) if tbls is None else 'No changes found for the tables in schema {schema}:{mw_instance}'.format(schema=schema,mw_instance=mw_instance) }")
    
    if len(schemas) == instance_down and mw_instance not in disable_schemas:
        alertname = f'{mw_instance} is down for the hour'
        summary = f"{mw_instance} is down following schemas {schemas} are affected in execution hour {kwargs['dag_run']}"
        try:
            raise_grafana_alert(alertname,summary)
        except Exception as e:
            print(e)
            notif.chat.send_ti_failure_alert(kwargs)

def raise_ticdc_alert(**kwargs):
    '''
    for all ticdc schemas one alert will be raised
    '''
    stale_schemas = []
    for ti_schema in TICDC_SCHEMAS[env]:
        tbls = kwargs['task_instance'].xcom_pull(task_ids=f'{ti_schema}.list_tables_with_changes', key='return_value')
        print(ti_schema)
        print(tbls)
        if tbls is None or len(tbls) == 0:
            stale_schemas.append(ti_schema)
            print(f"{'No tables returned from upstream for schema: {schema}'.format(schema=ti_schema) if tbls is None else 'No changes found for the tables in schema {schema}'.format(schema=ti_schema) }")
    alertname = f'TiCDC schemas stale alert'
    summary = f"Ticdc is down following for schemas {stale_schemas} are affected in execution hour {kwargs['dag_run']} "
    try:
        pass
        # raise_grafana_alert(alertname,summary)
    except Exception as e:
        print(e)
        notif.chat.send_ti_failure_alert(kwargs)

def raise_grafana_alert(alertname,summary):
    import requests
    import json

    url = "https://api.ninjavan.co/global/sda/2.0/grafana/webhook"

    payload = json.dumps({
    "alerts": [
        {
        "panelURL": ""
        }
    ],
    "commonAnnotations": {
        "summary": f'{summary}'
    },
    "commonLabels": {
        "alertname": f'{alertname}'
    },
    "state": "Alerting",
    "message": "Data"
    })
    headers = {
    'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)


def list_changes(bucket_name, schema, path, execution_date, **kwargs):
    """Creates a list of tables with changes per DB."""
    delimiter = "/part"
    tables_with_changes = []
    blob_list = []
    hook = GCSHook()

    if schema.startswith("ninjamart"):
        _schema = schema.replace("ninjamart_", "")
    else:
        _schema = schema

    for i in range(num_intervals):
        ts = execution_date + timedelta(minutes=i * 15)
        prefix = f"{path}/data/date={ts:%Y-%m-%d}/time={ts:%H-%M-%S}/database={_schema}"
        logging.info(f"Listing changes in '{bucket_name}/{prefix}'")
        # Note: the delimiter param is used so that the list_blobs returns results in a directory-like mode.
        #  See https://cloud.google.com/storage/docs/json_api/v1/objects/list
        blobs = hook.list(bucket_name=bucket_name, prefix=prefix, delimiter=delimiter)
        blob_list.extend(blobs)

    for blob in blob_list:
        if "table" in blob:
            table = blob.split("/")[-2].split("=")[-1]
            if table not in tables_with_changes:
                tables_with_changes.append(table)
    return tables_with_changes


def _get_path(connection, schema):
    """
    Check CDC message stream based on connection and schema
    """
    return "ticdc_stream" if (schema in TICDC_SCHEMAS[env] or connection == "ninjamart") else "cdc_stream"


gcs_sensors = {}
wait_tasks = {}
mw_check_tasks = {}
with DAG(
    dag_id="datalake_cdc_sensor",
    schedule_interval=f"*/{INTERVAL_DURATION} * * * *",
    default_args=default_args,
    max_active_runs=1,
    catchup=False,
    on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
    tags=["data_lake", "db_cdc"],
) as dag:
    ticdc_alert = PythonOperator(
                task_id=f"check_ticdc_schemas",
                python_callable=raise_ticdc_alert,
                retries=1,
            )
    mw_check_tasks['defualt_alert'] = ticdc_alert

    for connection in mysql_connections.keys():
        gs_bucket_raw = get_nv_data_bucket_uri(env=env, bucket_type="raw", schema=connection, strip=True)

        for database in mysql_connections[connection]:
            if connection == "ninjamart":
                _database = database.replace("ninjamart_", "")
            else:
                _database = database

            task_id = f"{database}.check_next_partition"

            if database not in gcs_sensors:
                gcs_sensors[database] = GCSObjectsWithPrefixExistenceSensor(
                    task_id=task_id,
                    bucket=gs_bucket_raw,
                    prefix=f"{_get_path(connection, database)}/data/date={{{{ next_ds }}}}/"
                    + f"time={{{{ next_execution_date.strftime('%H-%M-%S') }}}}/database={_database}",
                    execution_timeout=Timeout.FIVE_MINUTES,
                    on_failure_callback=None,
                    retries=1,
                )

            wait_tasks[database] = BashOperator(
                task_id=f"{database}.wait",
                bash_command="sleep 900",
                trigger_rule="none_skipped",
            )

            gcs_sensors[database] >> wait_tasks[database]

    for connection, schemas in mysql_connections.items():
        gs_bucket_raw = get_nv_data_bucket_uri(env=env, bucket_type="raw", schema=connection, strip=True)

        for schema in schemas:
            list_tables_from_gs_bucket_raw = PythonOperator(
                task_id=f"{schema}.list_tables_with_changes",
                python_callable=list_changes,
                op_kwargs={
                    "bucket_name": gs_bucket_raw,
                    "schema": schema,
                    "path": _get_path(connection, schema),
                },
                retries=1,
            )
            
            mw_instance = MW_DB_MAPPING.get(schema)
            if mw_instance and mw_instance not in mw_check_tasks:
                mw_check_tasks[mw_instance] = PythonOperator(
                    task_id=f"{mw_instance}.check_instance",
                    op_kwargs={
                    "mw_instance": mw_instance
                },
                    python_callable=raise_maxwell_alert,
                    retries=1,
                )
            wait_tasks[schema] >> list_tables_from_gs_bucket_raw >>  (mw_check_tasks.get(mw_instance) or mw_check_tasks['defualt_alert'])