import json
import logging
import sys

import gspread
import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql import functions as F
from pyspark.sql.types import DecimalType, DoubleType


def extract_df(spark: SparkSession, format: str, gcs_path: str, *columns: str) -> pd.DataFrame:
    """
    Extracts data from a file in the specified format and returns a DataFrame.

    Args:
        spark (SparkSession): The SparkSession object.
        format (str): To specify file format.
        gcs_path (str): To give gcs path.
        columns (str): To select columns to be written.

    Returns:
        DataFrame: The extracted data as a DataFrame.

    Raises:
        ValueError: If the file format is not supported.
    """
    supported_formats = ["parquet", "csv", "delta", "json"]

    if format not in supported_formats:
        raise ValueError("Unsupported file format: {}".format(format))

    spark_df = spark.read.format(format).load(gcs_path)
    if columns:  # If columns are provided, select only the specified columns
        spark_df = spark_df.select(*columns)

    # Limit the number of cells and columns as per gsheet
    max_cells_limit = 10000000
    max_columns_limit = 18278

    num_cells = spark_df.count() * len(spark_df.columns)

    if num_cells > max_cells_limit:
        raise ValueError(f"Number of cells exceeds the limit of {max_cells_limit}.")

    if len(spark_df.columns) > max_columns_limit:
        raise ValueError(f"Number of columns exceeds the limit of {max_columns_limit}.")

    for field in spark_df.schema.fields:
        if isinstance(field.dataType, DecimalType):
            spark_df = spark_df.withColumn(field.name, F.col(field.name).cast(DoubleType()))

    return spark_df.toPandas()


def write_to_gsheet(
    spark: SparkSession,
    data: pd.DataFrame,
    operation_type: str,
    sheet_name: str,
    sheet_url: str,
    creds: str,
    batch_size: int = 30000,
) -> None:
    """
    Function for establishing Google Sheet connection and writing query result to it.

    Args:
        spark (SparkSession): The SparkSession object.
        data (DataFrame): The data to write to Google Sheet.
        operation_type (str): The operation type.
        sheet_name (str): The name of the sheet.
        sheet_url (str): The URL of the Google Sheet.
        creds (str): The credentials string.
    """
    try:
        client = gspread.service_account_from_dict(json.loads(creds))
        sheet = client.open_by_url(sheet_url)
        worksheet = sheet.worksheet(sheet_name)

        if operation_type == "overwrite":
            worksheet.clear()

        columns = data.select_dtypes(include=["number"]).columns.tolist()
        num_rows = len(data)
        for i in range(0, num_rows, batch_size):
            batch_data = data.iloc[i : i + batch_size]
            batch_data_str = batch_data.applymap(lambda x: str(x))

            # Only include headers if it's the first batch
            if i == 0:
                values = [data.columns.tolist()] + batch_data_str.values.tolist()
            else:
                values = batch_data_str.values.tolist()

            worksheet.append_rows(values, value_input_option="USER_ENTERED")
    except Exception as e:
        logging.error("Error appending to Google Sheet: {}".format(str(e)))
        raise RuntimeError("Error appending to Google Sheet")


if __name__ == "__main__":
    gcs_path, format, operation_type, sheet_name, sheet_url, *columns, creds = sys.argv[1:]
    spark = SparkSession.builder.getOrCreate()

    try:
        df = extract_df(spark, format, gcs_path, *columns)
        write_to_gsheet(spark, df, operation_type, sheet_name, sheet_url, creds)

    finally:
        spark.stop()
