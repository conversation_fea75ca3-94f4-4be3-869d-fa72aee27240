import sys

from pyspark.sql import SparkSession

from common import date
from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.C2CDAG.Task.C2C_CSAT_MASKED + ".py",
    task_name=data_warehouse.C2CDAG.Task.C2C_CSAT_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
    ),
)

def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.ConsigneeProdGL(input_env, is_masked).CONSIGNEE_TO_CONSIGNEE_REQUESTS,
                view_name="consignee_to_consignee_requests",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ConsigneeProdGL(input_env, is_masked).RATINGS,
                view_name="ratings",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="c2c_csat",
                jinja_template=""" 
                select 
                        consignee_id
                        , r.created_at
                        , r.deleted_at
                        , r.updated_at
                        , r.rating
                        , r.feedback
                        , lower(r.system_id) as system_id
                        , ccr.plan
                        , type
                        , r.created_month

                    from ratings as r
                    join consignee_to_consignee_requests ccr
                        on r.tracking_id = ccr.tracking_id
                    where 
                        plan in ('DROPOFF', 'PICKUP')    
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).C2C_CSAT,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
     )
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()