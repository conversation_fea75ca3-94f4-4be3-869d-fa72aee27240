import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.COST_CARD_INTERMEDIATE_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.COST_CARD_INTERMEDIATE_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.VN,
    ),
    depends_on=(
        data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_ID_MASKED,
        data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_MY_MASKED,
        data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_PH_MASKED,
        data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_SG_MASKED,
        data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_VN_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FIRST_MILE_VOLUME_ORDERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.CANCELLED_EVENTS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"

    cost_card_events_path = f"gs://nv-data-{env}-bi-sensitive-reports/cost_card_events_{system_id}/measurement_datetime=latest"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIRST_MILE_VOLUME_ORDERS,
                view_name="first_mile_volume_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).CANCELLED_EVENTS,
                view_name="cancelled_events",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=cost_card_events_path,
                view_name="cost_card_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="cancel_datetimes",
                jinja_template="""

                select
                    order_id
                    , min(created_at) as cancelled_datetime
                from cancelled_events
                group by 1

                """,
            ),
            base.TransformView(
                view_name="first_fm_region",
                jinja_template="""

                select
                    order_id
                    , min_by(origin_hub_region, event_datetime) as first_fm_region
                from cost_card_events
                where cost_segment = 'fm'
                group by 1

                """,
            ),
            base.TransformView(
                view_name="costs",
                jinja_template="""

                select
                    order_id
                    , max(case when cost_segment = 'mm' and (type = 'pri_air_trip') then 1 else 0 end) as airhaul_flag
                    , max(case when cost_segment = 'mm' and (type = 'pri_sea_trip') then 1 else 0 end) as seahaul_flag
                    , max(case when cost_segment = 'mm' and (type = 'dd') then 1 else 0 end) as direct_dispatch_flag

                    , sum(case when cost_segment = 'fm' then cost else 0 end) as fm_cost
                    , sum(case when cost_segment = 'sort' then cost else 0 end) as sort_cost
                    , sum(case when cost_segment = 'mm' then cost else 0 end) as mm_cost
                    , sum(case when cost_segment = 'lm' then cost else 0 end) as lm_cost
                    , sum(case when cost_segment = 'hub' then cost else 0 end) as hub_cost
                    , sum(cost) as total_ops_cost

                    , count(order_id) as total_raw_events

                    , sum(case when cost_segment = 'fm' and cost is not null then 1 else 0 end) as fm_non_null_count
                    , sum(case when cost_segment = 'sort' and cost is not null then 1 else 0 end) as sort_non_null_count
                    , sum(case when cost_segment = 'mm' and cost is not null then 1 else 0 end) as mm_non_null_count
                    , sum(case when cost_segment = 'lm' and cost is not null then 1 else 0 end) as lm_non_null_count
                    , sum(case when cost_segment = 'hub' and cost is not null then 1 else 0 end) as hub_non_null_count
                    , sum(case when cost is not null then 1 else 0 end) as total_non_null_count

                    , sum(case when cost_segment = 'fm' and cost is null then 1 else 0 end) as fm_null_count
                    , sum(case when cost_segment = 'sort' and cost is null then 1 else 0 end) as sort_null_count
                    , sum(case when cost_segment = 'mm' and cost is null then 1 else 0 end) as mm_null_count
                    , sum(case when cost_segment = 'lm' and cost is null then 1 else 0 end) as lm_null_count
                    , sum(case when cost_segment = 'hub' and cost is null then 1 else 0 end) as hub_null_count
                    , sum(case when cost is null then 1 else 0 end) null_cost_count

                    , sum(case when cost_segment = 'hub' and remarks = 'fm' then cost else 0 end) as hub_fm_cost
                    , sum(case when cost_segment = 'hub' and remarks = 'wh' then cost else 0 end) as hub_wh_cost
                    , sum(case when cost_segment = 'hub' and remarks = 'lm' then cost else 0 end) as hub_lm_cost

                    , sum(case when cost_segment = 'hub' and remarks = 'fm' and cost is not null then 1 else 0 end) as hub_fm_non_null_count
                    , sum(case when cost_segment = 'hub' and remarks = 'wh' and cost is not null then 1 else 0 end) as hub_wh_non_null_count
                    , sum(case when cost_segment = 'hub' and remarks = 'lm' and cost is not null then 1 else 0 end) as hub_lm_non_null_count

                    , sum(case when cost_segment = 'hub' and remarks = 'fm' and cost is null then 1 else 0 end) as hub_fm_null_count
                    , sum(case when cost_segment = 'hub' and remarks = 'wh' and cost is null then 1 else 0 end) as hub_wh_null_count
                    , sum(case when cost_segment = 'hub' and remarks = 'lm' and cost is null then 1 else 0 end) as hub_lm_null_count

                    , sum(case when cost_segment = 'mm' and (type = 'intersort') then cost else 0 end) as mm_intersort_cost
                    , sum(case when cost_segment = 'mm' and (type in ('primary','pri_land_trip')) then cost else 0 end) as mm_pri_land_cost
                    , sum(case when cost_segment = 'mm' and (type = 'pri_air_trip') then cost else 0 end) as mm_pri_air_cost
                    , sum(case when cost_segment = 'mm' and (type = 'pri_sea_trip') then cost else 0 end) as mm_pri_sea_cost
                    , sum(case when cost_segment = 'mm' and (type in ('secondary','sec_trip')) then cost else 0 end) as mm_sec_cost
                    , sum(case when cost_segment = 'mm' and (type = 'dd') then cost else 0 end) as mm_dd_cost

                    , sum(case when cost_segment = 'mm' and (type = 'intersort') and cost is not null then 1 else 0 end) as mm_intersort_non_null_count
                    , sum(case when cost_segment = 'mm' and (type in ('primary','pri_land_trip')) and cost is not null then 1 else 0 end) as mm_pri_land_non_null_count
                    , sum(case when cost_segment = 'mm' and (type = 'pri_air_trip') and cost is not null then 1 else 0 end) as mm_pri_air_non_null_count
                    , sum(case when cost_segment = 'mm' and (type = 'pri_sea_trip') and cost is not null then 1 else 0 end) as mm_pri_sea_non_null_count
                    , sum(case when cost_segment = 'mm' and (type in ('secondary','sec_trip')) and cost is not null then 1 else 0 end) as mm_sec_non_null_count
                    , sum(case when cost_segment = 'mm' and (type = 'dd') and cost is not null then 1 else 0 end) as mm_dd_non_null_count

                    , sum(case when cost_segment = 'mm' and (type = 'intersort') and cost is null then 1 else 0 end) as mm_intersort_null_count
                    , sum(case when cost_segment = 'mm' and (type in ('primary','pri_land_trip')) and cost is null then 1 else 0 end) as mm_pri_land_null_count
                    , sum(case when cost_segment = 'mm' and (type = 'pri_air_trip') and cost is null then 1 else 0 end) as mm_pri_air_null_count
                    , sum(case when cost_segment = 'mm' and (type = 'pri_sea_trip') and cost is null then 1 else 0 end) as mm_pri_sea_null_count
                    , sum(case when cost_segment = 'mm' and (type in ('secondary','sec_trip')) and cost is null then 1 else 0 end) as mm_sec_null_count
                    , sum(case when cost_segment = 'mm' and (type = 'dd') and cost is null then 1 else 0 end) as mm_dd_null_count

                    , sum(case when cost_segment = 'mm' and utilization_rate is not null then utilization_numerator end) as mm_sum_utilization_numerator
                    , sum(case when cost_segment = 'mm' and utilization_rate is not null then utilization_denominator end) as mm_sum_utilization_denominator
                    , avg(case when cost_segment = 'mm' and utilization_rate is not null then utilization_rate end) as mm_avg_utilization_rate
                from cost_card_events
                group by 1

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    oms.order_id
                    , oms.tracking_id
                    , oms.creation_datetime
                    , oms.created_month
                    , case
                        when oms.third_party_tracking_id is not null then coalesce(oms.third_party_transfer_datetime, oms.force_success_datetime)
                        when oms.granular_status = 'Completed' then coalesce(oms.delivery_success_datetime, oms.force_success_datetime)
                        when oms.granular_status = 'Returned to Sender' then coalesce(oms.delivery_success_datetime, oms.force_success_datetime)
                        when oms.granular_status = 'Cancelled' then cancel_datetimes.cancelled_datetime
                        else null
                    end as terminal_datetime
                    , case
                        when oms.third_party_tracking_id is not null and oms.third_party_transfer_datetime is not null then 'third_party_transfer'
                        when oms.third_party_tracking_id is not null and oms.third_party_transfer_datetime is null and oms.force_success_datetime is not null then 'force_success'
                        when oms.granular_status = 'Completed' and oms.delivery_success_datetime is not null then 'delivery_success'
                        when oms.granular_status = 'Completed' and oms.delivery_success_datetime is null and oms.force_success_datetime is not null then 'force_success'
                        when oms.granular_status = 'Returned to Sender' and oms.delivery_success_datetime is not null then 'delivery_success'
                        when oms.granular_status = 'Returned to Sender' and oms.delivery_success_datetime is null and oms.force_success_datetime is not null then 'force_success'
                        when oms.granular_status = 'Cancelled' and cancel_datetimes.cancelled_datetime is not null then 'cancelled'
                        else 'missing'
                    end as terminal_datetime_type
                    , oms.system_id
                    , oms.granular_status
                    , if(oms.third_party_tracking_id is not null,1,0) as third_party_flag
                    , oms.force_success_flag
                    , costs.airhaul_flag
                    , costs.seahaul_flag
                    , costs.direct_dispatch_flag

                    , first_fm_region.first_fm_region
                    , first_mile_volume_orders.waypoint_id as fm_waypoint_id
                    , first_mile_volume_orders.dp_dropoff_dp_id as fm_pudo_id

                    , shipper_attributes.sales_channel
                    , shipper_attributes.id as shipper_id
                    , shipper_attributes.shipper_name
                    , shipper_attributes.parent_id_coalesce as shipper_parent_id_coalesce
                    , shipper_attributes.parent_name_coalesce as shipper_parent_name_coalesce
                    , oms.order_type
                    , oms.delivery_type
                    , oms.parcel_size as om_size
                    , oms.nv_width as om_width
                    , oms.nv_height as om_height
                    , oms.nv_length as om_length
                    , oms.nv_width * oms.nv_height * oms.nv_length as om_cubic_measure
                    , oms.weight as om_weight
                    , oms.delivery_fee as om_delivery_fee
                    , oms.estimated_weight as om_estimated_weight
                    , oms.estimated_volume as om_estimated_volume

                    , costs.fm_cost
                    , costs.sort_cost
                    , costs.mm_cost
                    , costs.lm_cost
                    , costs.hub_cost
                    , costs.total_ops_cost

                    , costs.total_raw_events

                    , costs.fm_non_null_count
                    , costs.sort_non_null_count
                    , costs.mm_non_null_count
                    , costs.lm_non_null_count
                    , costs.hub_non_null_count
                    , costs.total_non_null_count

                    , costs.fm_null_count
                    , costs.sort_null_count
                    , costs.mm_null_count
                    , costs.lm_null_count
                    , costs.hub_null_count
                    , costs.null_cost_count

                    , costs.hub_fm_cost
                    , costs.hub_wh_cost
                    , costs.hub_lm_cost

                    , costs.hub_fm_non_null_count
                    , costs.hub_wh_non_null_count
                    , costs.hub_lm_non_null_count

                    , costs.hub_fm_null_count
                    , costs.hub_wh_null_count
                    , costs.hub_lm_null_count

                    , costs.mm_intersort_cost
                    , costs.mm_pri_land_cost
                    , costs.mm_pri_air_cost
                    , costs.mm_pri_sea_cost
                    , costs.mm_sec_cost
                    , costs.mm_dd_cost

                    , costs.mm_intersort_non_null_count
                    , costs.mm_pri_land_non_null_count
                    , costs.mm_pri_air_non_null_count
                    , costs.mm_pri_sea_non_null_count
                    , costs.mm_sec_non_null_count
                    , costs.mm_dd_non_null_count

                    , costs.mm_intersort_null_count
                    , costs.mm_pri_land_null_count
                    , costs.mm_pri_air_null_count
                    , costs.mm_pri_sea_null_count
                    , costs.mm_sec_null_count
                    , costs.mm_dd_null_count

                    , costs.mm_sum_utilization_numerator
                    , costs.mm_sum_utilization_denominator
                    , costs.mm_avg_utilization_rate
                from order_milestones as oms
                left join costs
                    on oms.order_id = costs.order_id
                left join shipper_attributes
                    on oms.shipper_id = shipper_attributes.id
                left join cancel_datetimes
                    on oms.order_id = cancel_datetimes.order_id
                left join first_fm_region
                    on oms.order_id = first_fm_region.order_id
                left join first_mile_volume_orders
                    on oms.order_id = first_mile_volume_orders.order_id
                where 
                    costs.total_raw_events != 0
                    and shipper_attributes.sales_channel != 'Test'

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COST_CARD_INTERMEDIATE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()