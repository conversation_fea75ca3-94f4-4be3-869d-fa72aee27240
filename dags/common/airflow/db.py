import codecs

from airflow.hooks.base import BaseHook
from airflow.providers.mysql.hooks.mysql import MySqlHook


def get_conn(schema, mysql_connections):
    """
    Gets an Airflow connection configuration with host, port and login details.

    :param schema:                  Database name
    :type schema:                   string
    :param mysql_connections:       MySQL connections configuration of form:
                                    {
                                        <conn_id>: [<schema_name> ...]
                                    }
    :type mysql_connections:        dict
    :return:                        Airflow connection configuration
    :rtype:                         airflow.models.Connection
    """
    conn_id = get_conn_id(schema, mysql_connections)
    return BaseHook.get_connection(conn_id)


def get_conn_id(schema, mysql_connections):
    schema = schema.lower().strip()
    for cluster, dbs in mysql_connections.items():
        if schema in dbs:
            return cluster
    return ""


def get_schema_conn(schema, mysql_connections):
    """
    Returns an Airflow connection configuration, with the credentials encoded, as a dictionary.
    """

    conn = get_conn(schema, mysql_connections)
    conn_dict = {
        "host": conn.host,
        "port": conn.port,
        "user": conn.login,
        "password": codecs.encode(conn.password, "rot-13"),
    }
    return conn_dict


def get_column_info(schema, table, mysql_connections):
    """
    Returns column details from MySQL information_schema
    """
    conn_id = get_conn_id(schema, mysql_connections)
    mysql_hook = MySqlHook(mysql_conn_id=conn_id)
    query = f"""select * from information_schema.columns
        where table_schema = '{schema}' and table_name = '{table}'"""
    fields = mysql_hook.get_records(query)
    return fields
