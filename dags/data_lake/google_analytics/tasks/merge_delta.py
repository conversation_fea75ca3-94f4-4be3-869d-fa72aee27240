import logging
import sys

from delta import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, row_number

from common.stringcase import snake_case
from metadata.constants import LegacyGcsBucketUris
from metadata.google_analytics import GA_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

SYSTEM_ID = "system_id"
DATE_DIMENSION = "date"
OBJECT_BUCKET = LegacyGcsBucketUris.raw


def _get_delta_path(gcs_bucket, entity):
    """
    Returns path at which a Google Analytics entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      google_analytics entity (e.g. users)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/google_analytics/delta/{entity}"


def _get_obj_path(env, entity):
    """
    Returns path at which incremental change data of a Google Analytics entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      google_analytics entity (e.g. users, transactions)
    :return:            GCS path
    """
    return f"{OBJECT_BUCKET.format(env)}/google_analytics/objects/{entity}"


def _get_object(spark, path, date_str, updated_at, primary_columns):
    """
    Fetches incremental change data of a Google Analytics entity for a specified updated date.

    :param spark:       Spark session
    :param entity:      google_analytics entity
    :param path:        GCS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :return:            Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(DATE_DIMENSION, month_fmt))
    )
    # get latest update by id
    df = (
        df.withColumn(
            "row_number", row_number().over(Window.partitionBy([SYSTEM_ID, *primary_columns]).orderBy(desc(updated_at)))
        )
        .filter("row_number = 1")
        .drop("row_number")
    )
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Google Analytics entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)
    logging.info(f"Results are save in {path}")


def merge(spark, obj_df, path, updated_at, primary_columns):
    """
    Merges entity data into an existing Delta table.

    :param spark:       Spark session
    :param obj_df:      Entity dataframe
    :param path:        Delta table path
    :param updated_at:  Column name containing updated_at info
    :param updated_at:  List of primaru colimns
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH])
        for row in obj_df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    merge_str = ""
    for key in primary_columns:
        if merge_str:
            merge_str += f" and delta.{key} = cdc.{key}"
        else:
            merge_str = f"delta.{key} = cdc.{key}"
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) "
            f"AND delta.{SYSTEM_ID} = cdc.{SYSTEM_ID} and {merge_str}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{updated_at} > delta.{updated_at}")
        .whenNotMatchedInsertAll()
        .execute()
    )
    logging.info("Results are updated")


def merge_delta(spark, env, gcs_bucket, entity, date_str):
    updated_at = GA_CONFIG[entity]["updated_at_field"]
    primary_columns = [snake_case(c) for c in GA_CONFIG[entity]["dimensions"]]

    obj_path = _get_obj_path(env, entity)
    obj_df = _get_object(spark, obj_path, date_str, updated_at, primary_columns)
    if obj_df.count() == 0:
        logging.info("No records to update")
        return

    delta_path = _get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, obj_df, delta_path, updated_at, primary_columns)
    else:
        logging.info("Table does not exist, creating...")
        create(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, env, entity, date_str = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, env, gcs_bucket, entity, date_str)
    spark.stop()
