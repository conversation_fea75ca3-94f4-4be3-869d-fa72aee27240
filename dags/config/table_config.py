from airflow.models import Variable
from metadata.data_count_sanity import DATA_COUNT_TABLES

class TableConfig:
    TABLE_SIZES = Variable.get("table_sizes", deserialize_json=True)["cdc"]
    PII_TABLES = Variable.get("pii_fields", deserialize_json=True)
    TABLES_TO_SIZE = {table: size for size, tables in TABLE_SIZES.items() for table in tables}
    CRITICAL_TABLES = [table for table_dict in DATA_COUNT_TABLES.values() for table in table_dict]