import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FieldSalesDAG.Task.FIELD_SALES_SHORT_TERM_RETENTION_BASE_MASKED + ".py",
    task_name=data_warehouse.FieldSalesDAG.Task.FIELD_SALES_SHORT_TERM_RETENTION_BASE_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.EberDAG.DAG_ID,
            task_id=data_warehouse.EberDAG.Task.NINJA_REWARDS_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.EberDAG.DAG_ID,
            task_id=data_warehouse.EberDAG.Task.NINJA_REWARDS_USERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderAggregatesDAG.DAG_ID,
            task_id=data_warehouse.OrderAggregatesDAG.Task.SHIPPER_COMPLETION_VOL_MONTHLY_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.TRANSIT_TIME_REPORT_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.RecoveryDAG.DAG_ID,
            task_id=data_warehouse.RecoveryDAG.Task.PETS_TICKETS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.SalesforceDAG.DAG_ID,
            task_id=data_warehouse.SalesforceDAG.Task.SALESFORCE_OPPORTUNITY_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShipperLifetimeValueDAG.DAG_ID,
            task_id=data_warehouse.ShipperLifetimeValueDAG.Task.SHIPPER_LIFETIME_VALUES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 4)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).PETS_TICKETS_ENRICHED,
                view_name="pets_tickets_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_COMPLETION_VOL_MONTHLY,
                view_name="shipper_completion_vol_monthly",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).TRANSIT_TIME_REPORT,
                view_name="transit_time_report",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_REWARDS_MONTHLY,
                view_name="ninja_rewards_monthly",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).NINJA_REWARDS_USERS,
                view_name="ninja_rewards_users",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_ACCOUNT_ENRICHED,
                view_name="salesforce_account_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CASE_ENRICHED,
                view_name="salesforce_case_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_OPPORTUNITY_ENRICHED,
                view_name="salesforce_opportunity_enriched",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASONS,
                view_name="failure_reasons",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.DriverProdGL(input_env, is_masked).FAILURE_REASON_CODES,
                view_name="failure_reason_codes",
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_LIFETIME_VALUES,
                view_name="shipper_lifetime_values",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="field_sales_shippers",
                jinja_template="""
                select distinct
                    base.parent_id_coalesce
                    , base.sf_parent_acc_id_coalesce
                    , cohort_month
                from shipper_completion_vol_monthly as base
                left join shippers_enriched on
                    base.sf_parent_acc_id_coalesce = shippers_enriched.sf_parent_acc_id_coalesce
                where sf_sales_channel = 'Field Sales'
                and base.created_month >= '2020-01-01'
                """,
            ),
            base.TransformView(
                view_name="shipper_attributes_pre",
                jinja_template="""
                with base as (
                    select
                        base.sf_parent_acc_id_coalesce
                        , base.cohort_month
                        , base.cohort_month as m0_start_date

                        {%- for month in range(1, 5) %}

                             , base.cohort_month + interval {{ month }} month as m{{ month }}_start_date

                        {%- endfor %}

                        {%- for name, col in name_col_mappings.items() %}

                            , max(if(completion_month = base.cohort_month, {{ col }}, null)) as m0_{{ name }}

                            {%- for month in range(1, 5) %}

                                , max(if(
                                    completion_month = base.cohort_month + interval {{ month }} month
                                    , {{ col }}
                                    , null
                                )) as m{{ month }}_{{ name }}

                            {%- endfor %}

                        {%- endfor %}

                        , avg(if(
                            completion_month <= base.cohort_month + interval 1 month
                            , sf_parent_coalesce_ppd
                            , null
                        )) as m0_m1_avg_ppd
                        , sum(if(
                            completion_month < base.cohort_month + interval 2 month
                            , sf_parent_coalesce_total_orders
                            , null
                        )) as m0_m1_parcel_vol
                    from field_sales_shippers as base
                    left join shipper_completion_vol_monthly as shipper_monthly on
                        base.sf_parent_acc_id_coalesce = shipper_monthly.sf_parent_acc_id_coalesce
                   group by {{ range(1, 7) | join(',') }}
                )
                select
                    sf_parent_acc_id_coalesce
                    , cohort_month
                    , m0_m1_avg_ppd
                    , m0_m1_parcel_vol
                    , (m1_ppd - m0_ppd) / m0_ppd as ppd_change_rate

                    {%- for name in ['ppd', 'parcel_vol', 'category', 'start_date'] %}

                        {%- for month in range(5) %}

                            , m{{ month }}_{{ name }}

                        {%- endfor %}

                    {%- endfor %}

                    , case
                        when m0_m1_avg_ppd <= 5 then if(m3_ppd = 0 or m3_ppd is null, 1, 0)
                        else if(m3_ppd <= 5 or m3_ppd is null, 1, 0)
                    end as churn_ind

                    , case  --determine shipper size
                        when m0_m1_avg_ppd <= 5 then 'small'
                        when m0_m1_avg_ppd > 5 and m0_m1_avg_ppd <= 20 then 'medium'
                        when m0_m1_avg_ppd > 20 then 'large'
                        end as shipper_size

                    , case
                        when m1_category = 'Lapsed' then -3
                        when m1_category = 'Downtrader' then -1
                        when m1_category = 'Uptrader' then 3
                        else 1
                    end as m1_category_rank

                from base
                where (m0_ppd + m1_ppd) / 2 > 0
                """,
                jinja_arguments={
                    "name_col_mappings": {
                        "ppd": "sf_parent_coalesce_ppd",
                        "parcel_vol": "sf_parent_coalesce_total_orders",
                        "category": "sf_parent_coalesce_category",
                    },
                },
            ),
            base.TransformView(
                view_name="discounts",
                jinja_template="""
                with fs_shippers as (

                    select
                        sf_parent_acc_id_coalesce
                        , m0_start_date
                        , m1_start_date
                        , m2_start_date

                    from shipper_attributes_pre
                )

                select
                    sf_parent_acc_id_coalesce
                    ,count(discount) as m0_m1_no_of_discount
                    ,avg(discount) as m0_m1_avg_discount
                    ,max(discount) as m0_m1_max_discount
                from salesforce_opportunity_enriched as opportunity
                join salesforce_account_enriched as account on
                    opportunity.account_id = account.id
                join fs_shippers on
                    fs_shippers.sf_parent_acc_id_coalesce = account.parent_acc_id_coalesce
                where won_date < m2_start_date
                group by sf_parent_acc_id_coalesce
                """,
            ),
            base.TransformView(
                view_name="shipper_attributes",
                jinja_template="""
                with
                    ninja_rewards as (
                        select
                            shipper_id
                            , current_member_tier
                            , join_datetime as true_nr_join_date
                        from ninja_rewards_users
                    )
                    , nr_shippers as (
                        select
                            sf_parent_acc_id_coalesce
                            , date(min(true_nr_join_date)) as true_nr_join_date
                        from ninja_rewards
                        left join shippers_enriched on
                            ninja_rewards.shipper_id = shippers_enriched.id
                        group by sf_parent_acc_id_coalesce
                    )

                select
                    base.*
                    , true_nr_join_date
                    , if(
                        datediff(m2_start_date, true_nr_join_date) < 0
                            or datediff(m2_start_date, true_nr_join_date) is null
                        , 0
                        , datediff(m2_start_date, true_nr_join_date)
                    ) as nr_age_pre
                    , if(true_nr_join_date is not null and true_nr_join_date < m2_start_date, 1, 0) as is_join_nr
                from shipper_attributes_pre as base
                left join nr_shippers on
                    base.sf_parent_acc_id_coalesce = nr_shippers.sf_parent_acc_id_coalesce
                """,
            ),
            base.TransformView(
                view_name="ops_table",
                jinja_template="""
                with
                    ttr_data as (
                        select
                            attributes.sf_parent_acc_id_coalesce
                            , m0_start_date
                            , m1_start_date
                            , m2_start_date
                            , base.delivery_success_datetime
                            , date_trunc('month', base.delivery_success_datetime) as delivery_success_month
                            , base.rts_flag
                            , sc_met
                            , sc_measured
                            , base.rts_trigger_datetime
                            , codes.liability
                        from transit_time_report as base
                        join shippers_enriched as shippers on
                            base.shipper_id = shippers.id
                        join shipper_attributes as attributes on
                            attributes.sf_parent_acc_id_coalesce = shippers.sf_parent_acc_id_coalesce
                        left join order_milestones as orders on
                            base.order_id = orders.order_id
                        left join failure_reasons on
                            failure_reasons.id = orders.last_valid_delivery_failure_reason_id
                        left join failure_reason_codes as codes on
                            codes.id = failure_reasons.failure_reason_code_id
                    )
                    , first_x_days_sla_rts as (

                        select
                            sf_parent_acc_id_coalesce

                            {%- for name, col in name_col_mappings.items() %}

                                , sum(if(delivery_success_month = m0_start_date, {{ col }}, null)) as m0_{{ name }}
                                , sum(if(delivery_success_month = m1_start_date, {{ col }}, null)) as m1_{{ name }}
                                , sum(
                                    if(delivery_success_month < m2_start_date, {{ col }}, null)
                                ) as m0_m1_{{ name }}

                            {%- endfor %}

                            , avg(if(
                                delivery_success_month = m0_start_date and rts_flag = 1
                                , datediff(delivery_success_datetime, rts_trigger_datetime)
                                , null
                            )) as m0_avg_rts_spd
                            , avg(if(
                                delivery_success_month = m1_start_date and rts_flag = 1
                                , datediff(delivery_success_datetime, rts_trigger_datetime)
                                , null
                            )) as m1_avg_rts_spd
                            , avg(if(
                                delivery_success_month < m2_start_date and rts_flag = 1
                                , datediff(delivery_success_datetime, rts_trigger_datetime)
                                , null
                            )) as m0_m1_avg_rts_spd

                            {%- for name, liability in name_liability_mappings.items() %}

                                , sum(if(
                                    delivery_success_month = m0_start_date and {{ liability }}, rts_flag, null
                                )) as m0_rts_orders_{{ name }}_liable
                                , sum(if(
                                    delivery_success_month = m1_start_date and {{ liability }}, rts_flag, null
                                )) as m1_rts_orders_{{ name }}_liable
                                , sum(if(
                                    delivery_success_month < m2_start_date and {{ liability }}, rts_flag, null
                                )) as m0_m1_rts_orders_{{ name }}_liable

                            {%- endfor %}
                        from ttr_data
                        group by sf_parent_acc_id_coalesce
                    )
                    , overall_cal as (

                        select
                            base.sf_parent_acc_id_coalesce

                            {%- for col in ['m0', 'm1', 'm0_m1'] %}

                                , {{ col }}_orders_sc_met / {{ col }}_orders_sc_measured as {{ col }}_sla_rate
                                , coalesce({{ col }}_rts_orders_overall,0)
                                    / {{ col }}_parcel_vol as {{ col }}_rts_rate_overall
                                , {{ col }}_avg_rts_spd
                                , {{ col }}_rts_orders_overall

                                {%- for name in name_liability_mappings.keys() %}

                                    , coalesce({{ col }}_rts_orders_{{ name }}_liable,0)
                                        / {{ col }}_parcel_vol as {{ col }}_rts_rate_{{ name }}_liable

                                {%- endfor %}

                            {%- endfor %}
                        from shipper_attributes as base
                        left join first_x_days_sla_rts as sla_rts on
                            base.sf_parent_acc_id_coalesce = sla_rts.sf_parent_acc_id_coalesce
                    )
                    select
                        sf_parent_acc_id_coalesce
                        {%- for col in ['m0', 'm1', 'm0_m1'] %}

                            , {{ col }}_sla_rate
                            , {{ col }}_rts_rate_overall
                            , {{ col }}_rts_orders_overall

                            {%- for name in name_liability_mappings.keys() %}

                                , {{ col }}_rts_rate_{{ name }}_liable

                            {%- endfor %}

                        {%- endfor %}
                        , if(m0_rts_rate_overall = 0, 0, m0_avg_rts_spd) as m0_avg_rts_spd
                        , if(m1_rts_rate_overall = 0, 0, m1_avg_rts_spd) as m1_avg_rts_spd
                        , if(m0_m1_rts_rate_overall = 0, 0, m0_m1_avg_rts_spd) as m0_m1_avg_rts_spd
                    from overall_cal
                """,
                jinja_arguments={
                    "name_col_mappings": {
                        "orders_sc_met": "sc_met",
                        "orders_sc_measured": "sc_measured",
                        "rts_orders_overall": "rts_flag",
                    },
                    "name_liability_mappings": {
                        "nv": "liability = 'NINJAVAN'",
                        "external": "liability = 'EXTERNAL'",
                        "undefined": "liability is null",
                    },
                },
            ),
            base.TransformView(
                view_name="pets_table",
                jinja_template="""
                with
                    base as (
                        select
                            shippers.sf_parent_acc_id_coalesce
                            , m0_start_date
                            , m1_start_date
                            , m2_start_date
                            , m3_start_date
                            , m4_start_date
                            , resolution_datetime
                            , date_trunc('month', resolution_datetime) as resolution_month
                            , pets.type
                            , row_number() over (
                                partition by pets.order_id, pets.type order by pets.id desc
                            ) as ticket_order
                        from pets_tickets_enriched as pets
                        join order_milestones as orders on
                            pets.order_id = orders.order_id
                            and orders.created_month >= date_format('{cohort}' - interval 1 month, 'yyyy-MM')
                            and orders.created_month <=  date_format('{cohort}' + interval 4 month, 'yyyy-MM')
                        join shippers_enriched as shippers on
                             orders.shipper_id = shippers.id
                        join shipper_attributes as attributes on
                            shippers.sf_parent_acc_id_coalesce = attributes.sf_parent_acc_id_coalesce
                        where
                            pets.type in ('DAMAGED', 'MISSING')
                            and pets.status = 'RESOLVED'
                            and shippers.sf_sales_channel = 'Field Sales'
                            and date_trunc('month',resolution_datetime) < attributes.m2_start_date
                            and date_trunc('month',resolution_datetime) >= attributes.m0_start_date
                    )

                    , pets_filtered as (
                        select
                            *
                        from base
                        where ticket_order = 1
                    )

                    , pickup_table as (
                        select
                            attributes.sf_parent_acc_id_coalesce

                            {%- for type in ['ontime', 'early', 'late'] %}

                                , count(if(pickup_timeliness_indicator = '{{ type }}', reservation_id, null))
                                    / count(reservation_id)
                                as m0_m1_{{ type }}_pickup_rate

                            {%- endfor %}
                        from reservations_enriched as reservations
                        join shippers_enriched as shippers on
                            reservations.shipper_id = shippers.id
                        join shipper_attributes as attributes on
                            shippers.sf_parent_acc_id_coalesce = attributes.sf_parent_acc_id_coalesce
                        where
                            pickup_timeliness_indicator not in ('not attempted', 'pickup window not set')
                            and date_trunc('month',reservations.creation_datetime) >= attributes.m0_start_date
                            and date_trunc('month',reservations.creation_datetime) < attributes.m2_start_date
                        group by attributes.sf_parent_acc_id_coalesce
                    )

                    , first_x_days_lost_dmg as (
                        select
                            sf_parent_acc_id_coalesce
                            {%- for name, type in name_type_mappings.items() %}

                                , sum(if(
                                    resolution_month = m0_start_date and type = '{{ type }}', 1, 0
                                )) as m0_{{ name }}_orders
                                , sum(if(
                                    resolution_month = m1_start_date and type = '{{ type }}', 1, 0
                                )) as m1_{{ name }}_orders
                                , sum(if(
                                    resolution_month < m2_start_date and type = '{{ type }}', 1, 0
                                )) as m0_m1_{{ name }}_orders

                            {%- endfor %}
                        from pets_filtered
                        group by sf_parent_acc_id_coalesce
                    )
                select
                    attributes.sf_parent_acc_id_coalesce
                    {%- for col in ['m0', 'm1', 'm0_m1'] %}
                        {%- for type in name_type_mappings.keys() %}
                            , if(
                                {{ col }}_parcel_vol != 0 and {{ col }}_{{ type }}_orders is null
                                , 0
                                , {{ col }}_{{ type }}_orders/ {{ col }}_parcel_vol
                            ) as {{ col }}_{{ type }}_rate
                        {%- endfor %}
                    {%- endfor %}
                    , m0_m1_ontime_pickup_rate
                    , m0_m1_early_pickup_rate
                    , m0_m1_late_pickup_rate
                from shipper_attributes as attributes
                left join first_x_days_lost_dmg on
                    attributes.sf_parent_acc_id_coalesce = first_x_days_lost_dmg.sf_parent_acc_id_coalesce
                left join pickup_table on
                    attributes.sf_parent_acc_id_coalesce = pickup_table.sf_parent_acc_id_coalesce
                """,
                jinja_arguments={
                    "name_type_mappings": {
                        "damaged": "DAMAGED",
                        "missing": "MISSING",
                    },
                },
            ),
            base.TransformView(
                view_name="cod_table",
                jinja_template="""
                with base as (
                    select
                        attributes.sf_parent_acc_id_coalesce
                        , m0_parcel_vol
                        , m1_parcel_vol
                        , m2_parcel_vol
                        , m0_m1_parcel_vol
                        , sum(if(completion_month = m0_start_date, cod_orders - non_rts_cod_orders, null)) as m0_cod_rts
                        , sum(if(completion_month = m1_start_date, cod_orders - non_rts_cod_orders, null)) as m1_cod_rts
                        , sum(if(
                            completion_month < m2_start_date, cod_orders - non_rts_cod_orders, null
                        )) as m0_m1_cod_rts
                        , sum(if(completion_month = m0_start_date, cod_orders, null)) as m0_cod_vol
                        , sum(if(completion_month = m1_start_date, cod_orders, null)) as m1_cod_vol
                        , sum(if(completion_month < m2_start_date, cod_orders, null)) as m0_m1_cod_vol
                    from shipper_attributes as attributes
                    join shipper_completion_vol_monthly as shipper_monthly on
                        attributes.sf_parent_acc_id_coalesce = shipper_monthly.sf_parent_acc_id_coalesce
                    group by {{ range(1, 6) | join(',') }}
                )
                select
                    sf_parent_acc_id_coalesce
                    , m0_cod_rts / m0_parcel_vol as m0_cod_rts_rate
                    , m1_cod_rts / m1_parcel_vol as m1_cod_rts_rate
                    , m0_m1_cod_rts /m0_m1_parcel_vol as m0_m1_cod_rts_rate


                    , m0_cod_vol / m0_parcel_vol as m0_cod_rate
                    , m1_cod_vol / m1_parcel_vol as m1_cod_rate
                    , m0_m1_cod_vol / m0_m1_parcel_vol as m0_m1_cod_rate
                    ,m0_cod_vol
                    ,m1_cod_vol
                    ,m0_m1_cod_vol
                    ,m0_parcel_vol
                    ,m1_parcel_vol
                    ,m0_m1_parcel_vol
                from base
                """,
            ),
            base.TransformView(
                view_name="alive_prob_table",
                jinja_template="""
                select
                    attributes.sf_parent_acc_id_coalesce
                    {%- for type in ['max', 'min', 'avg'] %}

                            , round(
                                {{ type }}(if(
                                    date_trunc('month',report_date) = m0_start_date
                                    , shipper_lifetime_values.alive_probability
                                    , null
                                ))
                                ,2
                            ) as m0_alive_prob_{{ type }}
                            , round(
                                {{ type }}(if(
                                    date_trunc('month',report_date) = m1_start_date
                                    , shipper_lifetime_values.alive_probability
                                    , null
                                ))
                                ,2
                            ) as m1_alive_prob_{{ type }}
                            , round(
                                {{ type }}(if(
                                    date_trunc('month',report_date) < m2_start_date
                                    , shipper_lifetime_values.alive_probability
                                    , null
                                ))
                                ,2
                            ) as m0_m1_alive_prob_{{ type }}

                    {%- endfor %}

                from shipper_lifetime_values
                join shippers_enriched as shippers on
                    shipper_lifetime_values.shipper_id = shippers.id
                join shipper_attributes as attributes on
                    shippers.sf_parent_acc_id_coalesce = attributes.sf_parent_acc_id_coalesce
                group by attributes.sf_parent_acc_id_coalesce
                """,
            ),
            base.TransformView(
                view_name="fsr_table",
                jinja_template="""
                with case_on_parent_level as (

                    select
                          case.id
                        , case.sla_measured
                        , case.sla_met
                        , case.is_closed
                        , date_trunc('month',case.creation_datetime) as creation_month
                        , date_trunc('month',case.closed_datetime) as closed_month
                        , case.response_sla_measured
                        , case.response_sla_met
                        , case.cs_issue_type
                        , if(ces_survey_submission_date is not null, 1,0) as is_responded_to_survey
                        , coalesce(case.ces_score_raw) as ces_score_raw

                        -- hours to resolution
                        , (unix_timestamp(case.closed_datetime) - unix_timestamp(case.creation_datetime))
                            / 3600 as hours_to_resolution

                        -- hours to ops response
                        , (unix_timestamp(case.ops_response_datetime) - unix_timestamp(case.escalation_datetime))
                            / 3600 as hours_to_ops_response

                        , account.parent_acc_id_coalesce as sf_parent_acc_id_coalesce
                    from salesforce_case_enriched as case
                    left join salesforce_account_enriched as account on
                        account.id = case.account_id
                    where
                        case.account_name not like '%FSR Generic E2C Account%'
                        and fsr_issue_type != 'Delayed Delivery'

                )
                select

                    attributes.sf_parent_acc_id_coalesce

                    {%- for month, date in month_date_mappings.items() %}

                        , count(if({{ date }}, id, null)) as {{ month }}_case_count

                        -- case sla adherence percentage
                        , sum(if({{ date }} and is_closed = 1, sla_met, null)) /
                        sum(if({{ date }} and is_closed = 1, sla_measured, null)) as {{ month }}_case_sla_adherence_rate

                        -- case response rate
                        , sum(if({{ date }}, response_sla_met, null)) /
                        sum(if({{ date }}, response_sla_measured, null)) as {{ month }}_case_response_rate

                        -- hours to resolution
                        , avg(if({{ date }}, hours_to_resolution, null)) as {{ month }}_avg_hour_to_resolution

                        -- hours to ops response
                        , avg(if({{ date }}, hours_to_ops_response, null)) as {{ month }}_avg_hour_to_ops_response

                        -- ces score
                        , avg(if({{ date }}, ces_score_raw, null)) as {{ month }}_avg_ces_score
                        , sum(if({{ date }}, is_responded_to_survey, null)) as {{ month }}_survey_submission_count

                        -- driver complaints count
                        , count(
                            if({{ date }} and cs_issue_type = 'Complaints: Rider-related', id, null)
                        ) as {{ month }}_driver_complaints_count

                        -- finance-related complaints
                        , count(
                            if({{ date }} and cs_issue_type = 'Complaints: Finance', id, null)
                        ) as {{ month }}_finance_complaints_count

                    {%- endfor %}

                from shipper_attributes as attributes
                left join case_on_parent_level as case on
                    attributes.sf_parent_acc_id_coalesce = case.sf_parent_acc_id_coalesce
                group by attributes.sf_parent_acc_id_coalesce
                """,
                jinja_arguments={
                    "month_date_mappings": {
                        "m0": "creation_month = m0_start_date",
                        "m1": "creation_month = m1_start_date",
                        "m0_m1": "creation_month < m2_start_date",
                    },
                },
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select
                    base.*
                    {%- for col in ['m0', 'm1', 'm0_m1'] %}

                        , {{ col }}_missing_rate
                        , {{ col }}_damaged_rate
                        , {{ col }}_cod_rts_rate
                        , {{ col }}_cod_rate
                        , {{ col }}_alive_prob_avg
                        , {{ col }}_alive_prob_max
                        , {{ col }}_alive_prob_min
                        , {{ col }}_case_count
                        , {{ col }}_case_sla_adherence_rate
                        , {{ col }}_case_response_rate
                        , {{ col }}_avg_hour_to_resolution
                        , {{ col }}_avg_hour_to_ops_response
                        , {{ col }}_avg_ces_score
                        , {{ col }}_survey_submission_count
                        , {{ col }}_driver_complaints_count
                        , {{ col }}_finance_complaints_count
                        , {{ col }}_sla_rate
                        , {{ col }}_rts_rate_overall
                        , {{ col }}_rts_rate_nv_liable
                        , {{ col }}_rts_rate_external_liable
                        , {{ col }}_rts_rate_undefined_liable
                        , {{ col }}_avg_rts_spd

                    {%- endfor %}

                    , m0_m1_ontime_pickup_rate
                    , m0_m1_early_pickup_rate
                    , m0_m1_late_pickup_rate
                    , m0_m1_no_of_discount
                    , m0_m1_avg_discount
                    , m0_m1_max_discount
                    , date_format(base.cohort_month, 'yyyy-MM') as created_month
                from shipper_attributes as base
                left join ops_table on
                    base.sf_parent_acc_id_coalesce = ops_table.sf_parent_acc_id_coalesce
                left join pets_table on
                    base.sf_parent_acc_id_coalesce = pets_table.sf_parent_acc_id_coalesce
                left join cod_table on
                    base.sf_parent_acc_id_coalesce = cod_table.sf_parent_acc_id_coalesce
                left join alive_prob_table on
                    base.sf_parent_acc_id_coalesce = alive_prob_table.sf_parent_acc_id_coalesce
                left join discounts on
                    base.sf_parent_acc_id_coalesce = discounts.sf_parent_acc_id_coalesce
                left join fsr_table on
                    base.sf_parent_acc_id_coalesce = fsr_table.sf_parent_acc_id_coalesce
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FIELD_SALES_SHORT_TERM_RETENTION_BASE,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
