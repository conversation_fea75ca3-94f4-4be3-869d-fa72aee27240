import os
from enum import Enum
from pathlib import Path
from airflow.models import Variable

class Config:
    ALERT_CHANNEL = "chat_testing_alert"
    TASKS_PATH = "local://" + str(Path(__file__).resolve().parent.parent / "data_lake/db_cdc/tasks")
    REGISTRY = os.environ["IMAGE_REGISTRY"]
    SPARK_OPERATOR = Variable.get("spark_operator", default_var=False)
    IMAGE_NAME = os.environ["SPARK_IMAGE_NAME"]
    TAG = os.environ["SPARK_IMAGE_TAG"]
    ENV = Variable.get("env")
    INTERVAL_DURATION = 60
    NUM_INTERVALS = INTERVAL_DURATION // 15
    WAIT_TIME_DYNAMIC = Variable.get("cdc_wait_time")
    NO_WAIT_TIME_SCHEMA = Variable.get("no_wait_time_schema", deserialize_json=True, default_var=[])
    WAIT_TIME_DYNAMIC = Variable.get("cdc_wait_time", deserialize_json=True, default_var={
        "HIGH": 300,
        "MEDIUM": 900,
        "LOW": 1800
    })

class JobType(Enum):
    HIVE = "cdc_hive"
    CDC = "cdc"
    COMPACTION = "compaction"
    DWH = "dwh"