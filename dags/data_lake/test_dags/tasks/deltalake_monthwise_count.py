import logging
import sys

from pyspark.sql import SparkSession
from pyspark.sql import functions as F

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

bucket_name = "nv-data-prod-data-lake"


def save_count(spark, schema, table):
    cutoff_month = "2022-01"
    delta_table_path = f"gs://{bucket_name}/db/{schema}/{table}"
    df = spark.read.format("delta").load(delta_table_path)

    # Month wise count
    df = (
        df.filter(F.col("created_month") >= cutoff_month)
        .groupBy("created_month")
        .agg(F.count("*").alias("gcp_count"))
        .withColumn("table", F.lit(f"{schema}.{table}"))
    )

    output_path = f"gs://{bucket_name}/month_wise_counts/{schema}/{table}"
    (df.write.mode("overwrite").format("parquet").option("compression", "snappy").save(output_path))


if __name__ == "__main__":
    if len(sys.argv) < 2:
        logger.error("Please provide a table")
        sys.exit(1)

    spark = SparkSession.builder.getOrCreate()
    schema = sys.argv[1]
    table = sys.argv[2]

    save_count(spark, schema, table)
    spark.stop()
