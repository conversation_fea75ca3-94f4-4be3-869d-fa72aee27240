import sys
from delta import DeltaTable
from data_lake.db_cdc.tasks.post_process_cdc import _flatten_data
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, TimestampType
from pyspark.sql.functions import date_format, max as spark_max
from metadata.amast import AMAST_CONFIG
from pyspark.sql.window import Window
from pyspark.sql.functions import row_number, col
import traceback

NV_CREATED_MONTH = "nv_created_month"

def get_object(spark, path, date_str, partition_field, rate=False):
    try:
        month_fmt = "yyyy-MM"
        object_path = f"{path}/nv_updated_date={date_str}"
        print(f"Attempting to read from path: {object_path}")

        test_path = spark._jvm.org.apache.hadoop.fs.Path(object_path)
        fs = test_path.getFileSystem(spark._jsc.hadoopConfiguration())

        if not fs.exists(test_path):
            print(f"Path does not exist: {object_path}")
            # Define a basic schema for the empty DataFrame
            schema = StructType([
                StructField("raw_data", StringType(), True),
                StructField("_ingestion_timestamp", TimestampType(), True)
            ])
            return spark.createDataFrame([], schema=schema)

        df = spark.read.option("mergeSchema", "true").parquet(object_path)
        print(f"Successfully read parquet file. Schema: {df.schema}")

        if df.count() == 0:
            print("Empty DataFrame detected")
            return df

        if rate:
            df = df.select("raw_data")
            print("Attempting to flatten data...")
            df = _flatten_data(spark, df, "raw_data")
            print("Data flattening successful. Sample data:")
            df.show(2)

        if partition_field:
            print(f"Adding partition field: {partition_field}")
            df = df.withColumn(NV_CREATED_MONTH, date_format(partition_field, month_fmt))

        return df
    except Exception as e:
        print(f"Error in get_object function: {str(e)}")
        print(f"Full traceback: {traceback.format_exc()}")
        raise
def deduplicate_source_data(spark, df, primary_keys, updated_column):
    """
    Deduplicate source data by keeping only the latest record for each primary key combination.
    Uses window partitioning for more efficient processing.
    """
    try:
        print(f"Deduplicating source data using keys: {primary_keys}")
        print(f"Using update column for latest record selection: {updated_column}")

        window_spec = Window.partitionBy(*primary_keys).orderBy(col(updated_column).desc())

        deduplicated_df = df.withColumn(
            "row_num",
            row_number().over(window_spec)
        ).filter(col("row_num") == 1).drop("row_num")

        original_count = df.count()
        dedup_count = deduplicated_df.count()
        print(f"Original record count: {original_count}")
        print(f"Deduplicated record count: {dedup_count}")
        print(f"Removed {original_count - dedup_count} duplicate records")

        return deduplicated_df
    except Exception as e:
        print(f"Error in deduplicate_source_data: {str(e)}")
        print(f"Full traceback: {traceback.format_exc()}")
        raise

def merge(spark, entity, obj_df, path, system_id):
    try:
        print(f"Starting merge operation for entity: {entity}")
        print(f"Target Delta table path: {path}")

        delta_table = DeltaTable.forPath(spark, path)
        primary_keys = AMAST_CONFIG[system_id][entity]["primary_keys"]
        print(f"Using primary keys: {primary_keys}")

        updated_column = AMAST_CONFIG[system_id][entity].get("filter_column")
        print(f"Using update column: {updated_column}")

        print("\nSource data sample before deduplication:")
        obj_df.select(*primary_keys, updated_column).show(5)

        obj_df = deduplicate_source_data(spark, obj_df, primary_keys, updated_column)

        print("\nSource data sample after deduplication:")
        obj_df.select(*primary_keys, updated_column).show(5)

        merge_conditions = []
        for pk in primary_keys:
            merge_conditions.append(f"delta.{pk} = cdc.{pk}")
        merge_condition = " AND ".join(merge_conditions)
        print(f"Merge condition: {merge_condition}")

        conflicts_check = (
            obj_df.groupBy(*primary_keys)
            .count()
            .filter("count > 1")
        )

        if conflicts_check.count() > 0:
            print("WARNING: Conflicts remain after deduplication!")
            print("Sample of remaining conflicts:")
            conflicts_check.show(5)
            raise ValueError("Data validation failed - conflicts remain after deduplication")

        # Perform the merge
        (
            delta_table.alias("delta")
            .merge(
                obj_df.alias("cdc"),
                merge_condition
            )
            .whenMatchedUpdateAll(condition=f"cdc.{updated_column} >= delta.{updated_column}")
            .whenNotMatchedInsertAll()
            .execute()
        )
        print("Merge operation completed successfully")
    except Exception as e:
        print(f"Error in merge function: {str(e)}")
        print(f"Full traceback: {traceback.format_exc()}")
        raise

def merge_delta(spark, env, gcs_bucket, entity, system_id, date_str):
    try:
        print(f"Starting merge_delta with parameters: env={env}, entity={entity}, system_id={system_id}, date={date_str}")

        obj_path = f"gs://nv-data-{env}-data-lake-raw/amast/{entity}/{system_id}"
        print(f"Source path: {obj_path}")

        partition_field = AMAST_CONFIG[system_id][entity].get("partition_column")
        rate = AMAST_CONFIG[system_id][entity].get("rate", False)
        print(f"Configuration - partition_field: {partition_field}, rate: {rate}")

        obj_df = get_object(spark, obj_path, date_str, partition_field, rate)

        if obj_df is None or obj_df.rdd.isEmpty():
            print("No records to update")
            return

        record_count = obj_df.count()
        print(f"Retrieved {record_count} records")

        if record_count == 0:
            print("No records to update")
            return

        delta_path = f"gs://{gcs_bucket}/amast/{entity}/{system_id}"
        print(f"Target Delta path: {delta_path}")

        if DeltaTable.isDeltaTable(spark, delta_path):
            print("Found existing Delta table, performing merge")
            merge(spark, entity, obj_df, delta_path, system_id)
        else:
            print("No existing Delta table found, performing initial write")
            if partition_field:
                obj_df.write.mode("overwrite").format("delta").partitionBy(NV_CREATED_MONTH).save(delta_path)
            else:
                obj_df.write.mode("overwrite").format("delta").save(delta_path)
            print("Initial write completed successfully")

    except Exception as e:
        print(f"Error in merge_delta function: {str(e)}")
        print(f"Full traceback: {traceback.format_exc()}")
        raise


if __name__ == "__main__":
    try:
        print("Starting Spark job")
        env, entity, system_id, date_str = sys.argv[1:]
        print(f"Received arguments: env={env}, entity={entity}, system_id={system_id}, date_str={date_str}")

        gcs_bucket = f"nv-data-{env}-data-lake"
        print(f"Using GCS bucket: {gcs_bucket}")

        spark = SparkSession.builder.getOrCreate()
        print("Created Spark session successfully")

        merge_delta(spark, env, gcs_bucket, entity, system_id, date_str)
        spark.stop()
        print("Job completed successfully")
    except Exception as e:
        print(f"Fatal error in main: {str(e)}")
        print(f"Full traceback: {traceback.format_exc()}")
        sys.exit(1)
