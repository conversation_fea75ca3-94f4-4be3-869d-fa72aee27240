version: '3.7'
services:

  postgres:
    image: postgres:9.6.23-bullseye
    restart: unless-stopped
    environment:
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow
      - POSTGRES_DB=airflow
    volumes:
      - "dbdata:/var/lib/postgresql/data"
    ports:
      - "5432:5432"

  initdb:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    depends_on:
      - postgres
    volumes:
      - ../airflow.cfg:/opt/airflow/airflow.cfg
      - ../variables:/opt/airflow/variables
    entrypoint: /bin/bash
    command:
      - -c
      - |
        airflow db init
        if [[ -e /opt/airflow/variables/dev/all.json ]]; then
          airflow variables import /opt/airflow/variables/dev/all.json
        fi
        airflow users create -r Admin -u airflow -e <EMAIL> -f Airflow -l Airflow -p airflow

  webserver:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    restart: unless-stopped
    depends_on:
      - initdb
    volumes:
      - ../airflow.cfg:/opt/airflow/airflow.cfg
      - ../dags:/opt/airflow/dags
      - ../great_expectations:/opt/airflow/great_expectations
      - ../plugins:/opt/airflow/plugins
      - ../logs:/opt/airflow/logs
      - ./spark-conf:/spark-conf
    ports:
      - "8080:8080"
    command: webserver
    healthcheck:
      test: ["CMD-SHELL", "[ -f /opt/airflow/airflow-webserver.pid ]"]
      interval: 30s
      timeout: 30s
      retries: 3

  scheduler:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    restart: unless-stopped
    depends_on:
      - initdb
    volumes:
      - ../airflow.cfg:/opt/airflow/airflow.cfg
      - ../dags:/opt/airflow/dags
      - ../great_expectations:/opt/airflow/great_expectations
      - ../logs:/opt/airflow/logs
      - ../plugins:/opt/airflow/plugins
      - ./spark-conf:/spark-conf
    ports:
      - "4040:4040"
    command: scheduler

volumes:
  dbdata:
