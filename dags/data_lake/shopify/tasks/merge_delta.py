import logging
import sys

from delta import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, row_number, from_unixtime, col

from common.spark import pii
from metadata.constants import LegacyGcsBucketUris
from metadata.shopify import SHOPIFY_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

SYSTEM_ID = "system_id"
ID = "id"
CREATED_AT = "created_at"
OBJECT_BUCKET = LegacyGcsBucketUris.raw


def _get_delta_path(gcs_bucket, entity):
    """
    Returns path at which an Shopify entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Shopify entity (e.g. users)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/shopify/delta/{entity}"


def _get_obj_path(env, entity):
    """
    Returns path at which incremental change data of an Shopify entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Shopify entity (e.g. users, transactions)
    :return:            GCS path
    """
    return f"{OBJECT_BUCKET.format(env)}/shopify/objects/{entity}"


def _get_object(spark, entity, path, date_str, pii_fields):
    """
    Fetches incremental change data of an Shopify entity for a specified updated date.

    :param spark:       Spark session
    :param entity:      Shopify entity
    :param path:        GCS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :return:            Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    
    # Set Spark configurations to handle schema mismatches
    spark.conf.set("spark.sql.parquet.int96RebaseModeInRead", "CORRECTED")
    spark.conf.set("spark.sql.parquet.enableVectorizedReader", "false")
    
    # Read the data with schema merging disabled
    df = (spark.read
          .option("mergeSchema", "false")
          .option("timeZone", "UTC")
          .option("vectorizedReader", "false")
          .parquet(path))
    
    # Convert timestamp fields if they are bigint
    if "created_at" in df.columns and df.schema["created_at"].dataType.typeName() == "long":
        df = df.withColumn("created_at", from_unixtime(col("created_at")))
    if "updated_at" in df.columns and df.schema["updated_at"].dataType.typeName() == "long":
        df = df.withColumn("updated_at", from_unixtime(col("updated_at")))

    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(CREATED_AT, month_fmt))
    )
    
    # get latest update by id
    updated_at = SHOPIFY_CONFIG[entity]["updated_at_field"]
    df = (
        df.withColumn("row_number", row_number().over(Window.partitionBy(SYSTEM_ID, ID).orderBy(desc(updated_at))))
        .filter("row_number = 1")
        .drop("row_number")
    )
    if pii_fields:
        df = pii.mask(spark, df, pii_fields)
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Shopify entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)
    logging.info(f"Results are save in {path}")


def merge(spark, entity, obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param spark:   Spark session
    :param entity:  Shopify entity
    :param obj_df:  Entity dataframe
    :param path:    Delta table path
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH])
        for row in obj_df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    updated_at = SHOPIFY_CONFIG[entity]["updated_at_field"]
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) "
            f"AND delta.{SYSTEM_ID} = cdc.{SYSTEM_ID} AND delta.{ID} = cdc.{ID}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{updated_at} > delta.{updated_at}")
        .whenNotMatchedInsertAll()
        .execute()
    )
    logging.info("Results are updated")


def merge_delta(spark, env, gcs_bucket, entity, date_str, pii_fields):
    obj_path = _get_obj_path(env, entity)
    obj_df = _get_object(spark, entity, obj_path, date_str, pii_fields)
    if obj_df.count() == 0:
        logging.info("No records to update")
        return

    delta_path = _get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, entity, obj_df, delta_path)
    else:
        logging.info("Table does not exist, creating...")
        create(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, env, entity, date_str, pii_str = sys.argv[1:]
    pii_fields = [
        f.strip() for f in pii_str[(pii_str.index("[") + 1) : (pii_str.index("]"))].replace("'", "").split(",")
    ]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, env, gcs_bucket, entity, date_str, pii_fields)
    spark.stop()
