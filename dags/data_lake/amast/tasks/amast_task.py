import sys
import logging
import requests
from datetime import datetime, timed<PERSON>ta
from typing import Dict, <PERSON>, Op<PERSON>, <PERSON><PERSON>, Iterator
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from pyspark.sql import SparkSession, DataFrame
from functools import reduce
from pyspark.sql.functions import current_timestamp, col, udf, struct
import json

@dataclass
class EntityConfig:
    """Configuration for entity processing."""
    filter_column: str
    primary_keys: List[str]
    partition_column: Optional[str] = None
    batch_size: int = 35000
    max_retries: int = 3
    retry_delay: int = 60
    concurrent_requests: int = 5
    rate: Optional[bool] = True


@udf
def get_json_value(struct_col):
    """Get JSON value from Dataframe struct column."""
    try:
        if struct_col is None:
            return None
        return json.dumps(struct_col.asDict())
    except Exception:
        return None



def setup_logging() -> logging.Logger:
    """Configure logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

class APIClient:
    """Handles API communication."""
    def __init__(self, base_url: str, api_token: str, logger: logging.Logger):
        self.base_url = base_url
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': api_token
        }
        self.logger = logger
        self.session = requests.Session()

    def _make_request(self, url: str) -> Optional[List[Dict]]:
        """Make API request with basic error handling."""
        self.logger.info("Making request to URL: %s", url)
        try:
            response = self.session.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            data = response.json()
            self.logger.info(f"Request successful, received {len(data)} records")
            return data
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API request failed: {str(e)}")
            raise

    def fetch_batch(self, filter_column: str, entity: str, start_date: datetime,
                    end_date: datetime, batch_size: int, offset: int) -> List[Dict]:
        """Fetch batch of data from API with pagination."""
        url = (f"{self.base_url}/{entity}?"
               f"{filter_column}=gt.'{start_date.strftime('%Y-%m-%d')}'&"
               f"{filter_column}=lt.'{end_date.strftime('%Y-%m-%d')}'&"
               f"limit={batch_size}&offset={offset}")
        return self._make_request(url)

class AmastSparkExtractor:
    """Handles data loading from API into Spark."""

    def __init__(self, spark: SparkSession, api_client: APIClient,
                 entity_config: EntityConfig, logger: logging.Logger):
        self.spark = spark
        self.api_client = api_client
        self.entity_config = entity_config
        self.logger = logger
        self.batch_size = entity_config.batch_size
        self.processing_batch_size = 5000

    def _process_chunk(self, chunk: List[Dict]) -> Optional[DataFrame]:
        """Process a single chunk of data into a DataFrame."""
        try:
            json_records = [json.dumps(record) for record in chunk]
            rdd = self.spark.sparkContext.parallelize(json_records)
            df = (self.spark.read.json(rdd)
                  .withColumn("raw_data", get_json_value(struct(*[col(c) for c in self.spark.read.json(rdd).columns]))))
            return df
        except Exception as e:
            self.logger.error(f"Error processing chunk: {str(e)}")
            return None

    def _fetch_data_generator(self, entity: str, start_date: datetime,
                              end_date: datetime) -> Iterator[List[Dict]]:
        """Generator to fetch data in batches."""
        offset = 0
        while True:
            try:
                batch = self.api_client.fetch_batch(
                    self.entity_config.filter_column, entity,
                    start_date, end_date,
                    batch_size=self.batch_size,
                    offset=offset
                )
                if not batch:
                    break
                yield batch
                offset += len(batch)
                if len(batch) < self.batch_size:
                    break
            except Exception as e:
                self.logger.error(f"Error fetching batch at offset {offset}: {str(e)}")
                break

    def _fetch_and_process_batches(self, entity: str, start_date: datetime,
                                   end_date: datetime) -> Tuple[List[DataFrame], int]:
        """Fetch and process data in batches using parallel execution."""
        batch_dfs = []
        total_processed = 0

        with ThreadPoolExecutor(max_workers=self.entity_config.concurrent_requests) as executor:
            # Process chunks in parallel within each batch
            for batch in self._fetch_data_generator(entity, start_date, end_date):
                chunk_futures = []
                for i in range(0, len(batch), self.processing_batch_size):
                    chunk = batch[i:i + self.processing_batch_size]
                    chunk_futures.append(
                        executor.submit(self._process_chunk, chunk)
                    )

                for future in as_completed(chunk_futures):
                    try:
                        chunk_df = future.result()
                        if chunk_df is not None:
                            batch_dfs.append(chunk_df)
                            total_processed += self.processing_batch_size
                    except Exception as e:
                        self.logger.error(f"Error processing chunk: {str(e)}")
                        continue

        return batch_dfs, total_processed

    def process_entity(self, env: str, country: str, entity: str, date: str) -> Optional[DataFrame]:
        """Process a single entity."""
        self.logger.info(f"Processing {entity} for {country} on {date}")
        start_date = datetime.strptime(date, '%Y-%m-%d')
        end_date = start_date + timedelta(days=1)

        batch_dfs, total_processed = self._fetch_and_process_batches(entity, start_date, end_date)

        if not batch_dfs:
            self.logger.info(f"No data found for {entity} on {date}")
            return None

        final_df = reduce(DataFrame.unionAll, batch_dfs).withColumn("_ingestion_timestamp", current_timestamp())

        final_df.cache()

        output_path = f"gs://nv-data-{env}-data-lake-raw/amast/{entity}/{country}/nv_updated_date={start_date.strftime('%Y-%m-%d')}"
        self._write_to_gcs(final_df, output_path)

        final_df.unpersist()

        self.logger.info(f"Processed {total_processed} records. Output path: {output_path}")
        print(f"Processed {total_processed} records. Output path: {output_path}")
        return final_df

    def _write_to_gcs(self, df: DataFrame, output_path: str):
        """Write DataFrame to GCS in partitioned Parquet format with optimizations."""
        self.logger.info("Writing DataFrame to GCS at path: %s", output_path)
        (df.write
         .mode("overwrite")
         .format("parquet")
         .option("compression", "snappy")
         .option("spark.sql.files.maxRecordsPerFile", 10000)
         .save(output_path))
        self.logger.info("DataFrame written successfully to GCS.")


def validate_args(args: List[str]) -> Tuple[str, str, str, str, str, str]:
    """Validate and parse command line arguments."""
    if len(args) != 7:
        raise ValueError(f"Expected 6 arguments: env, entity, system_id, date, config, api_token. Got {len(args) - 1}.")
    
    env, entity, system_id, date_str, config_str, api_token = args[1:]
    logging.info("Validating arguments: env=%s, entity=%s, system_id=%s, date=%s", env, entity, system_id, date_str)

    try:
        datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Expected YYYY-MM-DD.")

    return env, entity, system_id, date_str, config_str, api_token


def main():
    """Main function."""
    logger = setup_logging()

    try:
        env, entity, system_id, date_str, config_str, api_token = validate_args(sys.argv)
        logger.info(f"Date is: {date_str}")
        config = json.loads(config_str)
        entity_config = EntityConfig(**config)
        logger.info(f"Entity configuration loaded: {entity_config}")
        spark = SparkSession.builder.appName(f"load_raw_amast_{entity}_{system_id}").getOrCreate()
        api_client = APIClient("https://pnvdb-api.amastsales.com", api_token, logger)
        extractor = AmastSparkExtractor(spark, api_client, entity_config, logger)
        extractor.process_entity(env, system_id, entity, date_str)

    except Exception as e:
        logger.error(f"Amast pipeline failed: {str(e)}")
        sys.exit(1)

    finally:
        if 'spark' in locals():
            spark.stop()
            logger.info("Spark session stopped.")


if __name__ == "__main__":
    main()
