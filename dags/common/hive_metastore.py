import re

from delta import DeltaTable
from pyspark.sql.utils import AnalysisException

from data_warehouse.utils.gcs import get_partition_combination_df

TABLE_PROPERTIES_FORMAT = {
    "delta": """
        ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'
        STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.SymlinkTextInputFormat'
        OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
        LOCATION '{table_path}/_symlink_format_manifest/'
    """,
    "parquet": """
        STORED AS PARQUET
        LOCATION '{table_path}'
    """,
}


def update_metastore(spark, hive_schema, partition_columns, table, table_path):
    table_format = _get_table_format(spark, table_path)
    if table_format == "delta":
        # Always update manifest as part of refresh Delta tables.
        generate_delta_manifest(spark, table_path)

    should_recreate_table = False
    should_msck = False

    # Keep as list for later use because set messes up the column order
    df_dtypes = _get_df_dtypes(spark, table_path, table_format)
    df_dtypes_set = set(df_dtypes)
    hive_dtypes_set = set(_get_hive_dtypes(spark, hive_schema, table))

    if df_dtypes_set != hive_dtypes_set:
        print(
            "Detected table schema mismatch between DF and hive external table\n"
            f"DF table schema: {df_dtypes_set}\n"
            f"Hive table schema: {hive_dtypes_set}\n"
            f"Difference: {df_dtypes_set ^ hive_dtypes_set}\n"
        )
        should_recreate_table = True
    elif partition_columns:
        has_new_partitions, has_deleted_partitions = _get_has_new_and_deleted_partitions(
            spark, hive_schema, table, table_path, partition_columns, table_format
        )
        if has_deleted_partitions:
            print("Detected deleted partitions")
            should_recreate_table = True
        elif has_new_partitions:
            print("Detected new partitions")
            should_msck = True

    if not should_recreate_table and hive_dtypes_set:
        hive_location = _get_hive_location(spark, hive_schema, table)
        if hive_location != table_path:
            print(f"Update table location from {hive_location} to {table_path}")
            should_recreate_table = True

    if should_recreate_table:
        _recreate_table(spark, table_path, df_dtypes, hive_schema, table, partition_columns, table_format)
    # MSCK REPAIR works only for partitioned tables.
    # Should always MSCK REPAIR if table needs to be recreated.
    if partition_columns and (should_msck or should_recreate_table):
        print("Triggering MSCK on the hive table to trigger partition discovery")
        # Force metastore to discover the partitions
        spark.sql(f"MSCK REPAIR TABLE `{hive_schema}`.`{table}`")


def generate_delta_manifest(spark, table_path):
    """
    Generates manifest files for the Delta table. This will update Delta version accessed from the Metastore to the
    latest version.
    """
    print("Generating Delta symlink manifest")
    delta_table = DeltaTable.forPath(spark, table_path)
    delta_table.generate("symlink_format_manifest")


def _get_table_format(spark, table_path):
    is_delta = DeltaTable.isDeltaTable(spark, table_path)
    return "delta" if is_delta else "parquet"


def _get_df_dtypes(spark, table_path, table_format):
    if table_format == "parquet":
        df = spark.read.option("mergeSchema", "true").parquet(table_path)
    else:
        df = spark.read.format(table_format).load(table_path)
    return [tup for tup in df.dtypes if not tup[0].startswith("nv_data")]


def _get_hive_dtypes(spark, hive_db, table):
    try:
        hive_dtypes_rows = (
            spark.sql(f"DESCRIBE TABLE `{hive_db}`.`{table}`").where("col_name not like '#%'").distinct().collect()
        )
        return [(i.col_name, i.data_type) for i in hive_dtypes_rows]
    except AnalysisException as err:
        if "not found" in str(err):  # implies new table
            return []  # a new table have empty dtypes
        else:
            raise err


def _get_hive_location(spark, hive_db, table):
    location_regex = "LOCATION '(.*)'\nTBLPROPERTIES"
    create_table_ddl = spark.sql(f"SHOW CREATE TABLE `{hive_db}`.`{table}` AS SERDE").collect()[0][0]
    location = re.search(location_regex, create_table_ddl).group(1)
    return location


def _get_create_table_ddl(table_path, df_dtypes, hive_schema, table, partition_columns, table_format):
    col_to_dtype = dict(df_dtypes)

    partition_cols_def = []
    for col in partition_columns:
        dtype = col_to_dtype.pop(col)
        partition_cols_def.append(f"`{col}` {dtype}")

    cols_def = []
    for (col, dtype) in col_to_dtype.items():
        cols_def.append(f"`{col}` {dtype}")

    partition_cols_def_str = ",".join(partition_cols_def)
    cols_def_str = ",".join(cols_def)

    partitioned_by_str = ""
    if partition_cols_def_str:
        partitioned_by_str = f"PARTITIONED BY ( {partition_cols_def_str} )"

    table_properties = TABLE_PROPERTIES_FORMAT[table_format].format(table_path=table_path)

    return f"""
        CREATE EXTERNAL TABLE `{hive_schema}`.`{table}` (
            {cols_def_str}
        )
        {partitioned_by_str}
        {table_properties}
    """


def _recreate_table(spark, table_path, df_dtypes, hive_schema, table, partition_columns, table_format):
    print("Recreating the hive table")
    spark.sql(f"CREATE DATABASE IF NOT EXISTS `{hive_schema}`")
    spark.sql(f"DROP TABLE IF EXISTS `{hive_schema}`.`{table}`")
    create_table_ddl = _get_create_table_ddl(table_path, df_dtypes, hive_schema, table, partition_columns, table_format)
    spark.sql(create_table_ddl)


def _get_delta_partitions_df(spark, table_path):
    """
    Returns a Pandas DF containing value combinations for the table partitions. Requires that the Delta table has a
    symlink format manifest already generated.
    """
    manifest_df = spark.read.format("text").load(f"{table_path}/_symlink_format_manifest")
    return manifest_df.drop("value").toPandas()


def _get_has_new_and_deleted_partitions(spark, hive_schema, table, table_path, partition_columns, table_format):
    if table_format == "delta":
        table_partitions_df = _get_delta_partitions_df(spark, table_path)
    else:
        table_partitions_df = get_partition_combination_df(table_path, partition_columns)
    table_partitions_list = table_partitions_df.to_dict(orient="records")
    table_partitions = []
    for column_to_values in table_partitions_list:
        partitions = [f"{column}={value}" for column, value in column_to_values.items()]
        table_partitions.append("/".join(partitions))
    table_partitions = set(table_partitions)

    hive_partition_records = spark.sql(f"SHOW PARTITIONS `{hive_schema}`.`{table}`").collect()
    hive_partitions = {row.partition for row in hive_partition_records}

    has_new_partitions = bool(table_partitions - hive_partitions)
    has_deleted_partitions = bool(hive_partitions - table_partitions)
    return has_new_partitions, has_deleted_partitions
