import logging
import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import ShortCircuitOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.providers.apache.spark.hooks.spark_submit import SparkSubmitHook
from google.cloud import storage

from common.airflow import notifications as notif
from common.stringcase import kebab_case, snake_case
from metadata.constants import Timeout


class CustomSparkSubmitHook(SparkSubmitHook):
    def submit(self, application, **kwargs):
        self._prepare_command([
            self._spark_submit_cmd,
            '--packages', 'simple-salesforce==1.11.1'
        ])
        super().submit(application, **kwargs)

class SalesforceSparkConfig:
    def __init__(self, env: str):
        self.env = env
        self.sf_connection = Variable.get(key="sf_connection_secret", deserialize_json=True)
        self.tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
        self.gcs_bucket = f"nv-data-{env}-data-lake-raw"
        self.gcs_object_base_path = "salesforce/sales_cloud/objects"
        
    def get_spark_conf(self) -> Dict[str, str]:
        return {
            "spark.executor.instances": "15",
            "spark.executor.memory": "5g",
            "spark.driver.memory": "6g",
            "spark.executor.cores": "4",
            "spark.driver.cores": "4",
            "spark.default.parallelism": "60",
            "spark.sql.shuffle.partitions": "120",
            "spark.databricks.delta.schema.autoMerge.enabled": "true",
            "spark.databricks.delta.merge.repartitionBeforeWrite.enabled": "true",
            "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
            "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "CORRECTED",
            "spark.sql.legacy.parquet.int96RebaseModeInWrite": "CORRECTED",
        }

    def get_extract_args(self, obj_config: Dict) -> Dict[str, str]:
        """Generate arguments for Salesforce extraction task"""
        return {
            "obj_name": obj_config["name"],
            "updated_at_field": obj_config["updated_at_field"],
            "gcs_bucket": self.gcs_bucket,
            "gcs_path": self.gcs_object_base_path,
            "sf_username": self.sf_connection["username"],
            "sf_password": self.sf_connection["password"],
            "sf_security_token": self.sf_connection["security_token"],
            "sf_domain": self.sf_connection["extras"]["domain"].strip(),
            "sf_instance_url": self.sf_connection["extras"]["instance_url"].strip(),
            "sf_version": self.sf_connection["extras"]["version"],
            "execution_date": "{{ execution_date }}",
            "next_execution_date": "{{ next_execution_date }}"
        }
    
    def get_merge_args(self, obj_name: str, created_at_field: str, updated_at_field: str) -> list:
        """Generate arguments for merge delta task"""
        return [
            f"nv-data-{self.env}-data-lake",
            self.env,
            obj_name,
            created_at_field,
            updated_at_field,
            "{{ ds }}",
            "{{ params.refresh_data }}",
            f"""{{{{ var.json.pii_fields_salesforce | extract('{obj_name}', {None}) }}}}"""
        ]

def create_salesforce_dag(
    dag_id: str = "datalake_salesforce_sales_cloud_spark",
    schedule: str = "0 16 * * *",
    env: str = Variable.get("env"),
    salesforce_config: Dict = None
):
    """Factory function to create Salesforce DAG with proper configuration"""
    
    config = SalesforceSparkConfig(env)
    
    default_args = {
        "owner": "airflow",
        "start_date": "2024-10-01",
        "retries": 2,
        "retry_delay": timedelta(minutes=5),
        "sla": Timeout.TWO_HOURS if env == "prod" else None,
        "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
    }

    with DAG(
        dag_id=dag_id,
        default_args=default_args,
        schedule_interval=schedule,
        sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
        on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
        catchup=False,
        user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
        tags=["data_lake"],
    ) as dag:
        
        def create_extract_task(obj_config: Dict) -> SparkSubmitOperator:
            task_id = f"extract_salesforce_{snake_case(obj_config['name'])}"
            args = config.get_extract_args(obj_config)
            
            formatted_args = []
            for k, v in args.items():
                formatted_args.extend([f"--{k}", str(v)])
            logging.info(f"Spark submit command for {task_id}: {' '.join(formatted_args)}")
            
            return SparkSubmitOperator(
                task_id=task_id,
                name=kebab_case(task_id),
                application=f"{config.tasks_path}/salesforce_spark_etl.py",
                application_args=formatted_args,
                conf=config.get_spark_conf(),
                conn_id="spark_default",
                execution_timeout=Timeout.THREE_HOURS,
                retries=2,
                retry_delay=timedelta(minutes=5),
            )
        
        def create_merge_task(obj_name: str, created_at_field: str, updated_at_field: str) -> SparkSubmitOperator:
            task_id = f"merge_delta_{obj_name}"
            return SparkSubmitOperator(
                task_id=task_id,
                name=kebab_case(task_id),
                task_concurrency=1,
                application=f"{config.tasks_path}/merge_delta.py",
                application_args=config.get_merge_args(obj_name, created_at_field, updated_at_field),
                params={"refresh_data": False},
                conn_id="spark_default",
                conf=config.get_spark_conf(),
                execution_timeout=Timeout.ONE_HOUR,
            )

        for obj_config in salesforce_config["objects"]:
            obj_name = snake_case(obj_config["name"])
            created_at_field = snake_case(obj_config["created_at_field"]) or ""
            updated_at_field = snake_case(obj_config["updated_at_field"])

            extract_task = create_extract_task(obj_config)
            
            check_task = ShortCircuitOperator(
                task_id=f"check_object_task_{obj_name}",
                python_callable=lambda obj_name, **context: storage.Client().get_bucket(config.gcs_bucket).blob(
                    f"{config.gcs_object_base_path}/{obj_name}/nv_updated_date={context['execution_date'].date()}/"
                    f"{obj_name}_{context['ts_nodash']}.snappy.parquet"
                ).exists(),
                op_args=[obj_name],
                provide_context=True,
            )
            
            merge_task = create_merge_task(obj_name, created_at_field, updated_at_field)
            
            extract_task >> check_task >> merge_task
            
        return dag

SALESFORCE_CONFIG = {
    "objects": [
        {
            "name": "Attachment",
            "created_at_field": "CreatedDate",
            "updated_at_field": "LastModifiedDate"
        },
        {
            "name": "CaseShare",
            "created_at_field": None,
            "updated_at_field": "LastModifiedDate"
        }
    ]
}

dag = create_salesforce_dag(salesforce_config=SALESFORCE_CONFIG)