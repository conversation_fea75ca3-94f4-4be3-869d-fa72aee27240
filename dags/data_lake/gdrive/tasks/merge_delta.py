import logging
import sys
from delta import DeltaTable

from pyspark.sql import SparkSession
from pyspark.sql.functions import last

GDRIVE_FOLDER = "gdrive"
APPEND = "append"
UPSERT = "upsert"
SRC_PARTITION_COLUMN = "nv_updated_date"


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Eber entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").save(path)


def merge(spark, obj_df, path, primary_keys):
    delta_table = DeltaTable.forPath(spark, path)

    merge_str = None
    for key in primary_keys:
        if merge_str:
            merge_str += f" and delta.{key} <=> cdc.{key}"
        else:
            merge_str = f"delta.{key} <=> cdc.{key}"

    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"{merge_str}",
        )
        .whenMatchedUpdateAll()
        .whenNotMatchedInsertAll()
        .execute()
    )


def _keep_last(df, keys):
    df = df.groupBy(keys).agg(*[last(c).alias(c) for c in df.columns if c not in keys])
    return df


def get_obj_path(object_gcs_bucket, entity, date_str):
    return f"gs://{object_gcs_bucket}/{GDRIVE_FOLDER}/{entity}/{date_str}.snappy.parquet"


def merge_delta(spark, object_gcs_bucket, main_gcs_bucket, entity, date_str, primary_keys):
    obj_path = get_obj_path(object_gcs_bucket, entity, date_str)
    obj_df = spark.read.option("mergeSchema", "true").parquet(obj_path)
    obj_df = _keep_last(obj_df, primary_keys)

    if obj_df.count() == 0:
        print("No records to update")
        return

    delta_path = f"gs://{main_gcs_bucket}/{GDRIVE_FOLDER}/delta/{entity}"
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, obj_df, delta_path, primary_keys)
    else:
        create(obj_df, delta_path)

    logging.info("Results are updated")


if __name__ == "__main__":
    object_gcs_bucket, main_gcs_bucket, entity, ts_nodash, primary_keys = sys.argv[1:]
    primary_keys = eval(primary_keys)
    obj_path = get_obj_path(object_gcs_bucket, entity, ts_nodash)
    spark = SparkSession.builder.config('spark.databricks.delta.schema.autoMerge.enabled', True).getOrCreate()
    merge_delta(spark, object_gcs_bucket, main_gcs_bucket, entity, ts_nodash, primary_keys)

    spark.stop()
