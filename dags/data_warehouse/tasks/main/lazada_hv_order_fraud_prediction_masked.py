import sys

from pyspark.sql import SparkSession
from pyspark.ml import PipelineModel
from pyspark.ml.classification import GBTClassificationModel

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FRAUD_PREDICTION_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FRAUD_PREDICTION_MASKED,
    depends_on=(data_warehouse.FakePhysicalParcelDAG.Task.LAZADA_HV_ORDER_FEATURES_MASKED,),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "inbound_date")),
    ),
)

PREPROCESSOR_PATH = "gs://nv-data-{env}-data-warehouse/spark_ml_model/preprocessor"
GBT_MODEL_PATH = "gs://nv-data-{env}-data-warehouse/spark_ml_model/gbt_model"

def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 1, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_HV_ORDER_FEATURES,
                view_name="lazada_hv_order_features",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="previous_day_inbound_orders",
                jinja_template="""
                    -- Only predict on orders inbounded yesterday and not in final status
                    
                    select 
                        *
                    from lazada_hv_order_features
                    where 
                        inbound_date = date('{{measurement_datetime}}')
                        and granular_status not in ('Completed', 'Returned to Sender')
                        
                """,
                jinja_arguments={"measurement_datetime": measurement_datetime},
            ),

        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).LAZADA_HV_ORDER_FRAUD_PREDICTION,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("inbound_date",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    base.load_data(spark, config.input)

    # Get previous day inbounded orders
    df = base.transform_data(spark, config.transform)

    # Use input table path to infer tasking running environment to be dev or prod
    if 'prod' in config.input.versioned_parquet_tables[0].path:
        env = 'prod'
    else:
        env = 'dev'

    # Preprocessor transform inbound orders
    preprocessor = PipelineModel.read().load(PREPROCESSOR_PATH.format(env=env))
    df_preprocessed = preprocessor.transform(df)

    # GBT model predict fraud orders
    gbt_model = GBTClassificationModel.read().load(GBT_MODEL_PATH.format(env=env))
    df_predicted = gbt_model.transform(df_preprocessed)

    # Drop vector columns so that table can be exposed to Metabase
    df_final = df_predicted.drop('features', 'rawPrediction', 'probability')

    base.write_data(df_final, config.output, spark)

    return df_final


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()