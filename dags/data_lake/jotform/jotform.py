from datetime import timedelta
from pathlib import Path

import pendulum
from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from jotform.operators.jotform_form_submissions_to_gcs_operator import JotformFormSubmissionsToGCSOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.constants import Timeout

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
jotform_forms = Variable.get("jotform_forms", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.ONE_HOUR if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


for topic, details in jotform_forms.items():
    dag_id = f"datalake_{topic}"
    start_date = pendulum.parse(details["start_date"])
    description = details["description"]
    dag = DAG(
        dag_id=dag_id,
        default_args=default_args,
        start_date=start_date,
        catchup=False,
        schedule_interval="@daily",
        sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
        on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
        tags=["data_lake", "data_lake_jotform"],
    )
    globals()[dag_id] = dag

    with dag:
        gcs_bucket = f"nv-data-{env}-data-lake"
        merge_task_id = f"merge_{description}"
        merge_delta = SparkSubmitOperator(
            task_id=merge_task_id,
            name=kebab_case(merge_task_id),
            task_concurrency=1,
            application=f"{tasks_path}/merge_delta.py",
            application_args=[gcs_bucket, description, "{{ds}}"],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "1",
                "spark.executor.memory": "5g",
                "spark.driver.memory": "5g",
                "spark.sql.shuffle.partitions": "1",
            },
        )

        for form in details["forms"]:
            load_jotform_form_submissions = JotformFormSubmissionsToGCSOperator(
                task_id=f"load_{description}_{form['system_id']}_{form['id']}",
                system_id=form["system_id"],
                jotform_form_id=form["id"],
                jotform_form_name=form["name"],
                gcs_bucket=gcs_bucket,
                gcs_object_base_path=f"jotform/objects/{description}/",
            )

            load_jotform_form_submissions >> merge_delta
