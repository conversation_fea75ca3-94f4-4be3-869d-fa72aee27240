import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_SG_MASKED + ".py",
    task_name=data_warehouse.CostCardDAG.Task.COST_CARD_EVENTS_SG_MASKED,
    system_ids=(constants.SystemID.SG,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.RESERVATIONS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.ROUTE_LOGS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FLEET_PERFORMANCE_BASE_DATA_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.FIRST_MILE_VOLUME_ORDERS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID,
            task_id=data_warehouse.FleetDAG.Task.WAYPOINTS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.HUB_JOURNEYS_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPERS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderSLADAG.DAG_ID,
            task_id=data_warehouse.OrderSLADAG.Task.SG_SLA_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.DPDAG.DAG_ID,
            task_id=data_warehouse.DPDAG.Task.DP_RESERVATIONS_ENRICHED_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_PICKUP_SUCCESS_MASKED,
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("cost_segment", "event_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 6)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    measurement_datetime_partition = f"/measurement_datetime={date.to_measurement_datetime_str(measurement_datetime)}"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED,
                view_name="hubs_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPERS_ENRICHED,
                view_name="shippers_enriched",
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ROUTE_LOGS_ENRICHED,
                view_name="route_logs_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).RESERVATIONS_ENRICHED,
                view_name="reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DP_RESERVATIONS_ENRICHED,
                view_name="dp_reservations_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FLEET_PERFORMANCE_BASE_DATA,
                view_name="fleet_performance_base_data",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).WAYPOINTS_ENRICHED,
                view_name="waypoints_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIRST_MILE_VOLUME_ORDERS,
                view_name="first_mile_volume_orders",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUB_JOURNEYS,
                view_name="hub_journeys",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SG_SLA_ENRICHED,
                view_name="sg_sla_enriched",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_PICKUP_SUCCESS,
                view_name="order_events_pickup_success",
                system_id=system_id,
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).SG_COST_CARD_POSTCODE,
                view_name="sg_postcode_map",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).SG_COST_CARD_FM_DRIVER_HOUR,
                view_name="fm_driver_hour",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).SG_COST_CARD_EXCLUDED_SHIPPER,
                view_name="excluded_shipper",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).SG_COST_CARD_TIME_PER_PARCEL,
                view_name="time_per_parcel",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(env).SG_COST_CARD_COST_SEGMENT,
                view_name="cost_segment",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).TRANSACTIONS,
                view_name="transactions",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="filtered_om",
                jinja_template="""

                select
                    order_milestones.order_id
                    , order_milestones.system_id
                    , least(coalesce(order_milestones.weight,0), 200) as weight
                    , order_milestones.parcel_size as size
                    , order_milestones.from_postcode
                    , order_milestones.pickup_datetime
                    , order_milestones.dp_dropoff_datetime
                    , order_milestones.inbound_datetime
                    , order_milestones.rts_trigger_datetime
                    , order_milestones.delivery_success_datetime
                    , order_milestones.pickup_hub_id
                    , order_milestones.inbound_hub_id
                    , order_milestones.delivery_success_hub_id
                    , order_milestones.dest_hub_id
                    , order_milestones.rts_flag
                    , order_milestones.is_pickup_required
                    , order_milestones.third_party_tracking_id
                    , order_milestones.shipper_id
                    , shippers_enriched.parent_id_coalesce as shipper_parent_id_coalesce
                    , shippers_enriched.parent_name_coalesce as shipper_parent_name_coalesce
                    , shippers_enriched.sales_channel
                    , shippers_enriched.shipper_name
                    , shippers_enriched.sf_nv_product_line
                    , order_milestones.creation_datetime
                    , case 
                        when (shippers_enriched.sf_nv_product_line = 'Restock' or shippers_enriched.shipper_name like 'B2B%')
                        then 'b2b'
                        when (shippers_enriched.sf_nv_product_line = 'Cold Chain' or shippers_enriched.shipper_name like '%Cold Chain%')
                        then 'cold chain'
                        else 'ecommerce'
                        end as allocation_type 
                    , date_format(order_milestones.creation_datetime, 'yyyy-MM') as created_month
                from order_milestones
                left join shippers_enriched
                    on order_milestones.shipper_id = shippers_enriched.id
                where shippers_enriched.sales_channel != 'Test'

                """,
            ),
            base.TransformView(
                view_name="fm_attempt_base",
                jinja_template="""

                with postcode_enriched as (

                    select 
                        lpad(postcode, 6, '0') as postcode
                        , trim(fm_team) as fm_team
                        , trim(fm_group) as fm_group
                    from sg_postcode_map

                ),
                -- MAPPING TABLE TO JOIN JOB TYPE AND DATA SOURCE
                mapping as (

                    select 
                        'PICKUP_APPOINTMENT' as job_type
                        , 'Pickup Appointment Job' as data_source
                    union all
                    select 
                        'RESERVATION'
                        , 'Reservation'
                    union all
                    select 
                        'PUDO_PICKUP_APPOINTMENT'
                        , 'Pudo Pickup Appointment Job'

                ),
                base_reservations as (

                    select
                        oeps.order_id
                        , r.waypoint_id
                        , r.route_id
                        , r.status
                        , r.dpms_id
                        , coalesce(r.service_end_datetime, r.update_datetime) as service_end_datetime
                        , r.created_month
                        , pc.fm_group
                        , we.postcode
                        , case 
                            when r.dpms_id is null 
                            then coalesce(r.service_end_datetime, r.update_datetime)
                            else null
                            end as doorstep_success_datetime
                        , case 
                            when r.dpms_id is null and extract(hour from coalesce(r.service_end_datetime, r.update_datetime)) < 7 
                            then date(coalesce(r.service_end_datetime, r.update_datetime) - interval '1' day) 
                            else date(coalesce(r.service_end_datetime, r.update_datetime)) 
                            end as doorstep_pp_date
                        -- DEDUP WHERE MORE THAN 1 PICKUP SUCCESS ARE MADE IN THE SAME DAY
                        , row_number() over(partition by oeps.order_id, date(oeps.created_at) order by oeps.created_at) as pickup_success_row_num

                    from reservations_enriched r
                    left join waypoints_enriched as we
                        on we.legacy_id = r.waypoint_id
                    inner join postcode_enriched as pc
                        on pc.postcode = lpad(we.postcode, 6, '0')
                    left join shippers_enriched as s
                        on r.shipper_id = s.id
                    -- MAP RESERVATION_ID BACK TO ORDER_ID
                    left join mapping as m
                        on r.data_source = m.data_source
                    inner join order_events_pickup_success oeps
                        on m.job_type = oeps.job_type
                        and r.reservation_id = oeps.reservation_id
                    where lower(r.status) in ('success', 'completed')
                        -- REMOVE B2B, Cold Chain, LM ORDERS
                        and (s.sf_nv_product_line not in ('Restock', 'Cold Chain') or s.sf_nv_product_line is null)
                        and s.shipper_name not like 'B2B%'
                        and s.shipper_name not like '%Cold Chain%'
                        and lower(r.route_driver_type) not like '%maincon - lm%'
                        and lower(r.route_driver_type) not like '%subcon - lm%'
                        and lower(r.route_driver_type) not like '%last mile - staff%'
                        and pc.fm_group in ('NORTH', 'CENTRAL', 'WEST', 'EAST')
                ),
                doorstep_reservations as (

                    select
                        *
                        -- WAYPOINTS WITH MORE THAN 1 RESERVATION AND DIFFERENT ATTEMPT DATETIMES
                        , row_number() over (partition by route_id, waypoint_id order by doorstep_success_datetime desc) as waypoint_seq
                    from base_reservations
                    where fm_group is not null
                        and dpms_id is null
                        -- DEDUP WHEN MORE THAN 1 PICKUP SUCCESS ARE MADE WITHIN SAME DAY
                        and pickup_success_row_num = 1

                ),
                pudo_sent as (

                    select distinct
                        order_id
                        , waypoint_id
                        , route_id
                        , dpms_id
                        , fm_group
                        , service_end_datetime as pudo_send_datetime
                        , case
                            when dpms_id is not null and extract(hour from service_end_datetime) < 7
                            then date(service_end_datetime - interval '1' day)
                            else date(service_end_datetime)
                            end as pudo_send_date
                    from base_reservations
                    where dpms_id is not null
                        -- DEDUP WHEN MORE THAN 1 PICKUP SUCCESS ARE MADE WITHIN SAME DAY
                        and pickup_success_row_num = 1

                ),
                pudo_collect as (

                    select
                        t.order_id
                        , sle.tracking_id
                        , t.waypoint_id
                        , t.route_id
                        , t.type
                        , t.distribution_point_id
                        , t.service_end_time + interval 8 hour as service_end_datetime
                    from transactions t
                    left join sg_sla_enriched sle
                        on t.order_id = sle.order_id
                    left join shippers_enriched s
                        on sle.shipper_id = s.id
                    where t.type = 'DD'
                        and lower(t.status) = 'success'
                        and t.distribution_point_id is not null
                        -- REMOVE UNSERVED TRANSACTIONS
                        and t.route_id is not null
                        and t.service_end_time is not null

                        -- REMOVE B2B COLD CHAIN ORDERS
                        and (s.sf_nv_product_line not in ('Restock', 'Cold Chain') or s.sf_nv_product_line is null)
                        and s.shipper_name not like 'B2B%'
                        and s.shipper_name not like '%Cold Chain%'

                ),
                pudo_collect_waypoints_prep as (

                    select distinct
                        pd.order_id
                        , pd.tracking_id
                        , pd.route_id
                        , pd.waypoint_id
                        , pd.distribution_point_id
                        , pc.fm_group
                        , dpr.dropped_off_at as pudo_dropoff_datetime
                    from pudo_collect pd
                    left join dp_reservations_enriched as dpr
                        on dpr.barcode = pd.tracking_id
                        -- PREVENT CROSS DAY MAPPING TO WRONG ROUTES
                        and date(pd.service_end_datetime) = date(dpr.dropped_off_at)
                        and dpr.source in ('FULLY_INTEGRATED_NINJA_COLLECT', 'SEMI_INTEGRATED_NINJA_COLLECT', 'RESCHEDULE', 'OPERATOR')
                    left join waypoints_enriched as we
                        on we.legacy_id = pd.waypoint_id
                    inner join postcode_enriched as pc
                        on pc.postcode = lpad(we.postcode, 6, '0')
                        and pc.fm_group in ('NORTH', 'CENTRAL', 'WEST', 'EAST')
                    -- PUDO COLLECT WAYPOINT_TYPE = 0
                    where we.waypoint_type = 0
                        and dpr.dropped_off_at is not null

                ),
                pudo_collect_waypoints as (

                    select
                        *
                        , case
                            when extract(hour from pudo_dropoff_datetime) < 7
                            then date(pudo_dropoff_datetime - interval '1' day)
                            else date(pudo_dropoff_datetime)
                        end as pudo_dropoff_date
                    from pudo_collect_waypoints_prep

                ),
                combined_pudo_data as (

                    select
                        order_id
                        , route_id
                        , waypoint_id
                        , dpms_id 
                        , fm_group
                        , pudo_send_datetime as pudo_success_datetime
                        , pudo_send_date as pudo_date
                    from pudo_sent

                    union all

                    select
                        order_id
                        , route_id
                        , waypoint_id
                        , distribution_point_id as dpms_id
                        , fm_group
                        , pudo_dropoff_datetime as pudo_success_datetime
                        , pudo_dropoff_date as pudo_date
                    from pudo_collect_waypoints

                ),
                pudo_dedup as (

                    select
                        *
                        , (unix_timestamp(pudo_success_datetime) - unix_timestamp(lag(pudo_success_datetime) over (
                             partition by route_id, dpms_id
                             order by pudo_success_datetime)))
                        as time_diff_in_sec
                    from combined_pudo_data
                
                ),
                pudo as (

                    select
                        *
                        -- TO FLAG VALID WAYPOINT ATTEMPT COUNT, IF VISIT SAME DP WITHIN 15 MIN WILL CONSIDER AS SAME ATTEMPT
                        , case
                            when time_diff_in_sec > 15*60 or time_diff_in_sec is null
                            then 1
                            else 0
                        end as valid_pudo_attempt_flag
                    from pudo_dedup
                    
                ),
                pudo_attempts_prep as (

                    select
                        fm_group
                        , pudo_date as event_date
                        , pudo_success_datetime as event_datetime
                        , waypoint_id
                        , order_id
                        , dpms_id
                        , route_id
                        , valid_pudo_attempt_flag
                        , sum(valid_pudo_attempt_flag) over(partition by route_id, dpms_id order by pudo_success_datetime) as row_num
                    from pudo

                ),
                pudo_attempts as (

                    select
                        fm_group
                        , event_date
                        , event_datetime
                        , waypoint_id
                        , order_id
                        , dpms_id
                        , route_id
                        , valid_pudo_attempt_flag
                        -- CREATE PUDO ATTEMPT ID TO MAP BACK WAYPOINT CPP TO ORDER LEVEL
                        , concat(route_id, lpad(dpms_id, 7, '0'), lpad(row_num, 3, '0')) as pudo_attempt_id
                    from pudo_attempts_prep

                ), 
                doorstep_attempts as (

                    select
                        fm_group
                        , doorstep_pp_date as event_date
                        , doorstep_success_datetime as event_datetime
                        , waypoint_id
                        , order_id
                        , route_id
                        , dpms_id
                        , waypoint_seq
                    from doorstep_reservations

                ),
                final as (

                    select 
                        event_date
                        , event_datetime
                        , fm_group
                        , route_id
                        , waypoint_id
                        , order_id
                        , dpms_id
                        , waypoint_seq
                        , null as pudo_attempt_id
                        , 'doorstep' as attempt_type
                    from doorstep_attempts
                    
                    union all
                    
                    select 
                        event_date
                        , event_datetime
                        , fm_group
                        , route_id
                        , waypoint_id
                        , order_id
                        , dpms_id
                        , null as waypoint_seq
                        , pudo_attempt_id
                        , 'pudo' as attempt_type
                    from pudo_attempts

                )
                select 
                    * 
                    , coalesce(pudo_attempt_id, waypoint_id) as valid_waypoint_id
                from final 

                """,
            ),
            base.TransformView(
                view_name="fm_waypoint_level",
                jinja_template="""
                with doorstep_agg as (

                    select
                        date_trunc('month', event_date) as event_month
                        , fm_group
                        , count(distinct route_id, waypoint_id) filter (where waypoint_seq = 1) as total_doorstep_success
                    from fm_attempt_base
                    where attempt_type = 'doorstep'
                    group by {{ range(1, 3) | join(',') }}

                ),

                pudo_agg as (

                    select
                        date_trunc('month', event_date) as event_month
                        , fm_group
                        , count(distinct pudo_attempt_id) as total_pudo_success
                        --, sum(valid_pudo_attempt_flag) as total_pudo_success
                    from fm_attempt_base
                    where attempt_type = 'pudo'
                    group by {{ range(1, 3) | join(',') }}

                )

                select
                    pp.event_month
                    , pp.fm_group as region
                    , pp.total_doorstep_success + pd.total_pudo_success as total_waypoints
                from doorstep_agg pp
                left join pudo_agg pd
                    on pp.event_month = pd.event_month
                    and pp.fm_group = pd.fm_group

                        """,
            ),
            base.TransformView(
                view_name="monthly_cost_per_waypoint",
                jinja_template="""

                with fm_order_level as (

                    select 
                        date_format(event_date, 'yyyy-MM') as event_month
                        , fm_group as region
                        , count(distinct order_id, coalesce(pudo_attempt_id, 0)) as total_pickup_orders
                    from fm_attempt_base
                    group by {{ range(1, 3) | join(',') }}

                ),

                pickup_direct_time as (

                    select
                        o.event_month
                        , o.region
                        , o.total_pickup_orders
                        , time_per_parcel.total_time_per_parcel
                        , o.total_pickup_orders * time_per_parcel.total_time_per_parcel as pickup_time_per_region
                    from fm_order_level o
                    left join time_per_parcel
                        on o.event_month = time_per_parcel.month

                ),
                -- TOTAL DRIVER WORKING HOURS - TOTAL DIRECT CPP TIME TAKEN
                waypoint_allocation_time as (

                    select
                        d.event_month
                        , d.region
                        , d.driver_hours * 60 - pickup_direct_time.pickup_time_per_region as indirect_time_per_waypoint
                        , pickup_direct_time.total_pickup_orders
                        , pickup_direct_time.total_time_per_parcel
                    from fm_driver_hour d
                    left join pickup_direct_time
                        on d.event_month = pickup_direct_time.event_month
                        and lower(d.region) = lower(pickup_direct_time.region)
                    where d.event_month is not null

                ),
                waypoint_indirect_cost as (

                    select
                        wp.event_month
                        , wp.region
                        , wp.total_time_per_parcel
                        , wp.total_pickup_orders
                        , round(wp.indirect_time_per_waypoint, 2) as indirect_time_per_waypoint
                        , round(avg(cost)/(sum(driver_hours) * 60), 2) as cost_per_driver_min
                        , round(sum(driver_hours), 0) as total_driver_hour
                        , round(avg(indirect_time_per_waypoint)*(avg(cost)/(sum(driver_hours) * 60))/avg(waypoint.total_waypoints), 2) as cost_per_waypoint
                    from waypoint_allocation_time wp
                    left join fm_driver_hour d
                        on wp.event_month = d.event_month
                    left join cost_segment cost
                        on cost.created_month = wp.event_month
                        and cost.cost_segment = 'FM'
                        and cost.type = 'ecommerce'
                        -- NEW DRIVER HOUR COST ALLOCATION
                        and cost.remarks = 'driver hour logic'
                    left join fm_waypoint_level as waypoint
                        on waypoint.event_month = wp.event_month
                        and lower(waypoint.region) = lower(wp.region)
                    group by {{ range(1, 6) | join(',') }}

                )
                select 
                    * 
                from waypoint_indirect_cost

                """,
            ),
            base.TransformView(
                view_name="fm_costs",
                jinja_template="""

                -- ECOMMERCE ORDERS
                with ecom_waypoint_region_prep as (

                    select
                        event_date
                        , valid_waypoint_id
                        , fm_group as region
                        , count(distinct order_id) as parcels
                        , min(event_datetime) as earliest_timestamp
                    from fm_attempt_base
                    group by {{ range(1, 4) | join(',') }}

                ),
                ecom_waypoint_region_rank as (

                    select
                        event_date
                        , valid_waypoint_id
                        , region
                        , earliest_timestamp
                        , parcels
                        , row_number() over (partition by event_date, valid_waypoint_id order by parcels desc, earliest_timestamp) as ranked
                    from ecom_waypoint_region_prep

                ),
                ecom_waypoint_region as (

                    select
                        event_date
                        , valid_waypoint_id
                        , min_by(region, ranked) as region
                        , sum(parcels) as parcel_count_per_waypoint
                    from ecom_waypoint_region_rank
                    group by {{ range(1, 3) | join(',') }}

                ),
                fm_ecom_base as (

                    select
                        base.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , date_format(base.event_datetime, 'yyyy-MM') as month
                        , 'fm' as cost_segment
                        , filtered_om.rts_flag
                        , filtered_om.rts_trigger_datetime
                        , base.event_date
                        , base.event_datetime
                        , wp.region as origin_hub_region
                        , coalesce(filtered_om.pickup_hub_id, filtered_om.inbound_hub_id) as origin_hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , case 
                            when filtered_om.pickup_datetime is not null then 'pickup'
                            when filtered_om.inbound_datetime is not null then 'inbound'
                        end as type
                        , filtered_om.allocation_type
                        , round(monthly_cost_per_waypoint.cost_per_waypoint/wp.parcel_count_per_waypoint, 2) as indirect_cpp
                        , monthly_cost_per_waypoint.cost_per_driver_min * monthly_cost_per_waypoint.total_time_per_parcel as direct_cpp
                        , 'ecommerce' as remarks
                        , base.valid_waypoint_id as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                        , wp.parcel_count_per_waypoint
                        , base.valid_waypoint_id
                    from fm_attempt_base base
                    inner join ecom_waypoint_region wp
                        on base.valid_waypoint_id = wp.valid_waypoint_id
                        and base.event_date = wp.event_date
                    left join filtered_om
                        on base.order_id = filtered_om.order_id
                    left join monthly_cost_per_waypoint
                        on lower(base.fm_group) = lower(monthly_cost_per_waypoint.region)
                        and date_trunc('month', base.event_date) = date_trunc('month', monthly_cost_per_waypoint.event_month)
                    where wp.parcel_count_per_waypoint is not null

                ), 
                -- NON ECOMMERCE ORDERS
                non_ecom_base as (

                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , 'fm' as cost_segment
                        , filtered_om.rts_flag
                        , filtered_om.rts_trigger_datetime
                        , coalesce(filtered_om.pickup_datetime, filtered_om.inbound_datetime, filtered_om.delivery_success_datetime) as event_datetime
                        , date(coalesce(filtered_om.pickup_datetime, filtered_om.inbound_datetime, filtered_om.delivery_success_datetime)) as day
                        , lower(sg_postcode_map.fm_group) as region
                        , coalesce(filtered_om.pickup_hub_id, filtered_om.inbound_hub_id) as hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , case 
                            when filtered_om.pickup_datetime is not null then 'pickup'
                            when filtered_om.inbound_datetime is not null then 'inbound'
                            end as type
                        , first_mile_volume_orders.waypoint_id
                        , filtered_om.allocation_type
                        , case
                            when first_mile_volume_orders.waypoint_id is not null
                                and first_mile_volume_orders.order_type != 'Return'
                                and first_mile_volume_orders.pickup_channel not in ('Cross Border'
                                                                                , 'Cross Border (Direct Injection)'
                                                                                , 'Direct Injection'
                                                                                , 'Non-FM'
                                                                                , 'NV internal'
                                                                                , 'Order Set as Pickup Not Required'
                                                                                , 'No pickup_scan'
                                                                                , '')
                            then 1
                            else 0
                            end as valid_waypoint_allocation_flag
                    from filtered_om
                    left join sg_postcode_map
                        on lpad(filtered_om.from_postcode, 6, '0') = lpad(sg_postcode_map.postcode, 6, '0')
                    left join first_mile_volume_orders
                        on filtered_om.order_id = first_mile_volume_orders.order_id
                    where allocation_type in ('b2b', 'cold chain')

                ),
                non_ecom_order_allocation_base as (

                    select 
                        date_format(event_datetime, 'yyyy-MM') as event_month
                        , allocation_type
                        , count(*) as orders
                    from non_ecom_base
                    group by {{ range(1, 3) | join(',') }}

                ),
                non_ecom_order_allocation_month as (

                    select
                        base.event_month
                        , base.allocation_type
                        , cost_segment.cost / cast(base.orders as double) as cpp
                    from non_ecom_order_allocation_base base
                    left join cost_segment
                        on base.event_month = cost_segment.created_month
                        and base.allocation_type = lower(cost_segment.type)
                        and lower(cost_segment.cost_segment) = 'fm'

                ),
                fm_non_ecom_costs as (

                    select
                        base.order_id
                        , base.created_month
                        , base.system_id
                        , base.cost_segment
                        , base.event_datetime
                        , date_format(base.day, 'yyyy-MM') as month
                        , base.region as origin_hub_region
                        , base.hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , base.weight
                        , base.size
                        , base.type
                        , order_allocation_month.cpp as cost
                        , base.allocation_type as remarks
                        , base.waypoint_id as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from non_ecom_base as base
                    left join non_ecom_order_allocation_month as order_allocation_month
                        on date_format(base.day, 'yyyy-MM') = order_allocation_month.event_month
                        and base.allocation_type = order_allocation_month.allocation_type

                ),
                final as (

                    select
                        order_id
                        , created_month
                        , system_id
                        , cost_segment
                        , event_datetime
                        , month
                        , origin_hub_region
                        , origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , weight
                        , size
                        , type
                        , coalesce(indirect_cpp, 0) + coalesce(direct_cpp, 0) as cost
                        , 'not_applicable' as remarks
                        , ref_id
                        , utilization_weight
                        , utilization_numerator
                        , utilization_denominator
                        , utilization_rate
                    from fm_ecom_base

                    union all

                    select 
                        order_id
                        , created_month
                        , system_id
                        , cost_segment
                        , event_datetime
                        , month
                        , origin_hub_region
                        , origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , weight
                        , size
                        , type
                        , cost
                        , 'not_applicable' as remarks
                        , ref_id
                        , utilization_weight
                        , utilization_numerator
                        , utilization_denominator
                        , utilization_rate
                    from fm_non_ecom_costs

                )
                select
                    *
                from final

                        """,
            ),
            base.TransformView(
                view_name="sort_costs",
                jinja_template="""
                with sort_events as (
                
                    select
                        hub_journeys.order_id
                        , filtered_om.created_month
                        , hub_journeys.system_id
                        , 'sort' as cost_segment
                        , hub_journeys.event_datetime
                        , date_format(hub_journeys.event_datetime, 'yyyy-MM') as month
                        , hub_journeys.region as origin_hub_region
                        , hub_journeys.coalesce_hub_id as origin_hub_id
                        , 'not_applicable' as dest_hub_region
                        , 'not_applicable' as dest_hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , 'not applicable' as type
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null
                        and hub_journeys.facility_type = 'CROSSDOCK'

                ),
                final as (
                
                    select
                        sort_events.order_id
                        , sort_events.created_month
                        , sort_events.system_id
                        , sort_events.cost_segment
                        , sort_events.event_datetime
                        , sort_events.month
                        , sort_events.origin_hub_region
                        , sort_events.origin_hub_id
                        , sort_events.dest_hub_region
                        , sort_events.dest_hub_id
                        , sort_events.weight
                        , sort_events.size
                        , sort_events.type
                        , cost_segment.cost
                        , cost_segment.remarks
                        , 'not_applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from sort_events
                    left join cost_segment
                        on sort_events.cost_segment = lower(cost_segment.cost_segment)
                        and sort_events.origin_hub_id = cost_segment.hub_id
                        and sort_events.month = cost_segment.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="lm_costs",
                jinja_template="""
                with lm_event_base as (
                
                    select
                        transactions.order_id
                        , transactions.waypoint_id
                        , waypoints_enriched.routing_zone_id as zone_id
                        , transactions.route_id
                        , from_utc_timestamp(transactions.service_end_time, "{{ local_timezone }}") as service_end_time
                    from transactions
                    left join waypoints_enriched
                        on transactions.waypoint_id = waypoints_enriched.legacy_id
                    left join route_logs_enriched
                        on transactions.route_id = route_logs_enriched.legacy_id
                    where transactions.status = 'Success'
                        and transactions.type = 'DD'

                ),
                lm_base_pre as (
                
                    select
                        lm_event_base.*
                        , fleet_performance_base_data.courier_type as driver_type
                    from lm_event_base
                    left join fleet_performance_base_data
                        on lm_event_base.route_id = fleet_performance_base_data.route_id
                        and date(lm_event_base.service_end_time) = fleet_performance_base_data.route_date
                
                ),
                lm_base as (
                
                    select
                        lm_base_pre.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , lm_base_pre.service_end_time as event_datetime
                        , coalesce(delivery_success_hub.region, dest_hub.region) as region
                        , coalesce(filtered_om.delivery_success_hub_id, filtered_om.dest_hub_id) as hub_id
                        , lm_base_pre.driver_type as type
                        , filtered_om.weight
                        , filtered_om.size
                        , lm_base_pre.zone_id as ref_id
                    from filtered_om
                    left join lm_base_pre
                        on filtered_om.order_id = lm_base_pre.order_id
                    left join hubs_enriched as delivery_success_hub
                        on filtered_om.delivery_success_hub_id = delivery_success_hub.id
                    left join hubs_enriched as dest_hub
                        on filtered_om.dest_hub_id = dest_hub.id
                    where
                        filtered_om.delivery_success_datetime is not null
                        and filtered_om.third_party_tracking_id is null
                        -- REMOVE COLD CHAIN & B2B ORDERS
                        and (
                            filtered_om.sf_nv_product_line not in ('Restock', 'Cold Chain')
                            or filtered_om.sf_nv_product_line is null
                        )
                ),
                lm_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'lm' as cost_segment
                        , 'not applicable' as origin_hub_region
                        , 'not applicable' as origin_hub_id
                        , region as dest_hub_region
                        , hub_id as dest_hub_id
                        , weight
                        , size
                        , type
                        , ref_id
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from lm_base

                ),
                final_v1 as (
                
                    select
                        lm_events.order_id
                        , lm_events.created_month
                        , lm_events.system_id
                        , lm_events.cost_segment
                        , lm_events.event_datetime
                        , lm_events.month
                        , lm_events.origin_hub_region
                        , lm_events.origin_hub_id
                        , lm_events.dest_hub_region
                        , lm_events.dest_hub_id
                        , lm_events.weight
                        , lm_events.size
                        , lm_events.type
                        , cost_segment.cost
                        , cost_segment.remarks
                        , lm_events.ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from lm_events
                    left join cost_segment
                        on lm_events.cost_segment = lower(cost_segment.cost_segment)
                        and lower(lm_events.type) = lower(cost_segment.type)
                        and lm_events.ref_id = cost_segment.zone_id
                        and lm_events.size = lower(cost_segment.size)
                        and lm_events.month = cost_segment.created_month
                    where lm_events.month <= '2024-06'

                ),
                final_v2 as (
                
                    select
                        lm_events.order_id
                        , lm_events.created_month
                        , lm_events.system_id
                        , lm_events.cost_segment
                        , lm_events.event_datetime
                        , lm_events.month
                        , lm_events.origin_hub_region
                        , lm_events.origin_hub_id
                        , lm_events.dest_hub_region
                        , lm_events.dest_hub_id
                        , lm_events.weight
                        , lm_events.size
                        , lm_events.type
                        , cost_segment.cost
                        , cost_segment.remarks
                        , lm_events.ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from lm_events
                    left join cost_segment
                        on lm_events.cost_segment = lower(cost_segment.cost_segment)
                        and lm_events.ref_id = cost_segment.zone_id
                        and lm_events.size = lower(cost_segment.size)
                        and lm_events.month = cost_segment.created_month
                    where lm_events.month >= '2024-07'

                ),
                final as (
                
                    select * from final_v1
                    UNION ALL
                    select * from final_v2
                
                )
                    
                select * from final

                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="mm_costs",
                jinja_template="""
                with mm_base as (
                
                    select
                        system_id
                        , order_id
                        , event_datetime
                        , region
                        , coalesce_hub_id
                        , coalesce_hub_name
                        , facility_type
                        , lead(region,1) over (partition by order_id order by event_datetime) as next_region
                        , lead(coalesce_hub_id,1) over (partition by order_id order by event_datetime) as next_coalesce_hub_id
                        , lead(facility_type,1) over (partition by order_id order by event_datetime) as next_facility_type
                    from hub_journeys

                ),
                west_pickup as (
                
                    select distinct
                        order_id
                    from fm_costs
                    where origin_hub_region = 'west'

                ),
                pri_filter_base as (

                    select
                        system_id
                        , order_id
                        , min_by(coalesce_hub_id, event_datetime) as first_hub
                        , min(event_datetime) as event_datetime
                    from mm_base
                    group by 1,2
                
                ),
                pri_filter as (

                    select
                        pri_filter_base.system_id
                        , pri_filter_base.order_id
                        , pri_filter_base.event_datetime
                    from pri_filter_base
                    left join west_pickup
                        on pri_filter_base.order_id = west_pickup.order_id
                    where pri_filter_base.first_hub = '103410'
                        and west_pickup.order_id is not null

                ),
                mm_pri_base as (

                    select
                        system_id
                        , order_id
                        , 'primary' as type
                        , 'PDN' as region
                        , 109 as coalesce_hub_id
                        , 'YCK' as next_region
                        , 103410 as next_coalesce_hub_id
                        , case
                            when hour(event_datetime) >= 22 then date_trunc('DD', event_datetime) + interval 22 hours
                            else date_trunc('DD', event_datetime - interval 1 day) + interval 22 hours
                        end as event_datetime
                    from pri_filter

                ),
                sec_logic as (
                
                    select
                        *
                        , case
                            when lower(next_facility_type) = 'station' and lower(facility_type) = 'crossdock' and coalesce_hub_id = 103410 then 1
                            else 0
                            end as mm_cost_flag
                    from mm_base

                ),
                mm_sec_base as (
                
                    select
                        system_id
                        , order_id
                        , 'secondary' as type
                        , region
                        , coalesce_hub_id
                        , next_region
                        , next_coalesce_hub_id
                        , event_datetime
                    from sec_logic
                    where mm_cost_flag = 1
                
                ),
                mm_combined as (

                    select * from mm_pri_base
                    UNION ALL
                    select * from mm_sec_base

                ),
                mm_events as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , 'mm' as cost_segment
                        , mm_combined.region as origin_hub_region
                        , mm_combined.coalesce_hub_id as origin_hub_id
                        , mm_combined.next_region as dest_hub_region
                        , mm_combined.next_coalesce_hub_id as dest_hub_id
                        , filtered_om.weight
                        , filtered_om.size
                        , mm_combined.type
                        , mm_combined.event_datetime
                        , date_format(mm_combined.event_datetime, 'yyyy-MM') as month
                    from filtered_om
                    left join mm_combined
                        on filtered_om.order_id = mm_combined.order_id

                ),
                mm_cpp_base as (
                
                    select
                        month
                        , sum(mm_events.weight) as total_weight
                        , count(*) as parcels
                    from mm_events
                    group by 1
                    
                ),
                mm_cpp as (
                
                    select
                        *
                        , cast(cost_segment.cost as double) / total_weight as cpkg
                    from mm_cpp_base
                    left join cost_segment
                        on lower(cost_segment.cost_segment) = 'mm'
                        and mm_cpp_base.month = cost_segment.created_month

                ),
                final as (
                
                    select
                        mm_events.order_id
                        , mm_events.created_month
                        , mm_events.system_id
                        , mm_events.cost_segment
                        , mm_events.event_datetime
                        , mm_events.month
                        , mm_events.origin_hub_region
                        , mm_events.origin_hub_id
                        , mm_events.dest_hub_region
                        , mm_events.dest_hub_id
                        , mm_events.weight
                        , mm_events.size
                        , mm_events.type
                        , mm_cpp.cpkg * mm_events.weight as cost
                        , 'not_applicable' as remarks
                        , 'not_applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from mm_events
                    left join mm_cpp
                        on mm_events.month = mm_cpp.created_month

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="hub_costs",
                jinja_template="""
                with hub_base as (
                
                    select
                        filtered_om.order_id
                        , filtered_om.created_month
                        , filtered_om.system_id
                        , filtered_om.weight
                        , filtered_om.size
                        , hub_journeys.event_datetime
                        , hub_journeys.coalesce_hub_id
                        , hub_journeys.region
                    from filtered_om
                    left join hub_journeys
                        on filtered_om.order_id = hub_journeys.order_id
                    where
                        hub_journeys.order_id is not null
                        -- REMOVE COLD CHAIN ORDERS WITH INBOUND_HUB_ID = 1
                        and (
                            filtered_om.sf_nv_product_line != 'Cold Chain'
                            or filtered_om.sf_nv_product_line is null
                            or filtered_om.inbound_hub_id != 1
                        )
                ),
                hub_events as (
                
                    select
                        order_id
                        , created_month
                        , system_id
                        , 'hub' as cost_segment
                        , region as origin_hub_region
                        , coalesce_hub_id as origin_hub_id
                        , 'not applicable' as dest_hub_region
                        , 'not applicable' as dest_hub_id
                        , weight
                        , size
                        , event_datetime
                        , date_format(event_datetime, 'yyyy-MM') as month
                    from hub_base

                ),
                final as (
                
                    select
                        hub_events.order_id
                        , hub_events.created_month
                        , hub_events.system_id
                        , hub_events.cost_segment
                        , hub_events.event_datetime
                        , hub_events.month
                        , hub_events.origin_hub_region
                        , hub_events.origin_hub_id
                        , hub_events.dest_hub_region
                        , hub_events.dest_hub_id
                        , hub_events.weight
                        , hub_events.size
                        , cost_segment.type
                        , cost_segment.cost
                        , cost_segment.remarks
                        , 'not applicable' as ref_id
                        , 'not_applicable' as utilization_weight
                        , 'not_applicable' as utilization_numerator
                        , 'not_applicable' as utilization_denominator
                        , 'not_applicable' as utilization_rate
                    from hub_events
                    left join cost_segment
                        on hub_events.cost_segment = lower(cost_segment.cost_segment)
                        and hub_events.origin_hub_id = cost_segment.hub_id
                        and hub_events.month = cost_segment.created_month

                )
                    
                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with combined_costs as (
                
                    select * from fm_costs
                    UNION ALL
                    select * from sort_costs
                    UNION ALL
                    select * from mm_costs
                    UNION ALL
                    select * from lm_costs
                    UNION ALL
                    select * from hub_costs

                ),
                sequence as (
                    select 
                        *
                        , row_number() over (partition by order_id order by event_datetime, cost_segment, type) as event_sequence
                    from combined_costs

                ),
                final as (
                
                    select 
                        system_id
                        , cast(order_id as bigint) as order_id
                        , event_sequence
                        , event_datetime
                        , lower(origin_hub_region) as origin_hub_region
                        , origin_hub_id
                        , lower(dest_hub_region) as dest_hub_region
                        , dest_hub_id
                        , weight
                        , lower(size) as size
                        , lower(type) as type
                        , round(cast(cost as double), 4) as cost
                        , lower(remarks) as remarks
                        , ref_id
                        , utilization_weight
                        , utilization_numerator
                        , utilization_denominator
                        , utilization_rate
                        , lower(cost_segment) as cost_segment
                        , month as event_month
                        , month as created_month
                        , created_month as order_created_month
                    from sequence
                    where month >= '2023-01' 
                    order by {{ range(1, 4) | join(',') }}

                )

                select * from final
                
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).COST_CARD_EVENTS_SG,
        measurement_datetime=measurement_datetime,
        partition_by=("cost_segment", "event_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()