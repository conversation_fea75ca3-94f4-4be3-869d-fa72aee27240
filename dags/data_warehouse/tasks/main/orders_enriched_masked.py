import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.OrdersDAG.Task.ORDERS_ENRICHED_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    post_execution_check=True,
)

DEPLOYMENT_TIME = "2022-06-27 05:36:00"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDER_DETAILS,
                view_name="order_details",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).CODS,
                view_name="cods",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).DELIVERY_TYPES,
                view_name="delivery_types",
            ),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).MARKETPLACE_SELLERS,
                view_name="marketplace_sellers",
            ),
            base.InputTable(
                path=delta_tables.ThreePlOmsProdGL(input_env, is_masked).THIRD_PARTY_VENDORS,
                view_name="third_party_vendors",
            ),
            base.InputTable(
                path=delta_tables.ThreePlOmsProdGL(input_env, is_masked).THIRD_PARTY_ORDERS,
                view_name="third_party_orders",
            ),
            base.InputTable(
                path=delta_tables.PricingProdGL(input_env, is_masked).PRICING_ORDERS,
                view_name="pricing_orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.ShipperProdGL(input_env, is_masked).SHIPPERS,
                view_name="shippers",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT orders.id AS order_id
                       , orders.tracking_id
                       , third_party_orders.third_party_tracking_id
                       , orders.shipper_order_ref_no AS shipper_reference_number
                       , orders.status
                       , orders.granular_status

                       , CASE
                {%- for enriched_status, granular_statuses in status_enriched.items() %}
                             WHEN orders.granular_status in ('{{ granular_statuses | join("', '") }}')
                {%- if enriched_status == 'completed' %}
                                  AND orders.status = 'Completed'
                {%- endif %}
                             THEN '{{ enriched_status }}'
                {%- endfor %}
                             ELSE 'exception'
                         END AS status_enriched

                       , cast(orders.global_shipper_id as bigint) AS shipper_id
                       , get_json_object(orders.shipper_ref_metadata, '$.shipper.sellerId') as seller_id
                       , get_json_object(orders.shipper_ref_metadata, '$.shipper.sellerName') as seller_name
                       , CASE
                            WHEN get_json_object(orders.shipper_ref_metadata, '$.platformInfo.platformTags')
                                like '%lazmall%' then 1
                            ELSE 0
                        END AS lazmall_flag
                       , case 
                            when (case 
                                    when shippers.short_name = 'Lazada' 
                                    then 'lazada' 
                                    else 'non_lazada' 
                                  end) = 'lazada'
                            then (case 
                                    when replace(get_json_object(orders.shipper_ref_metadata, '$.firstMileTplName'), '"', '') = 'LEX ID'
                                    then 1 
                                    else 0 
                                  end) 
                            else null
                        end as lex_flag
                       , cast(orders.source_id as int) as source_id
                       , get_json_object(order_details.metadata,"$.plugin_name") as plugin_name
                       , orders.cod_id

                {%- for name, definition in currency_columns.items() %}
                       , cast({{ definition }} as double) AS {{ name }}
                       , cast(round({{ definition }}/{{ sgd_exchange_rate[system_id] }}, 2) as double) AS {{ name }}_sgd
                {%- endfor %}

                {%- if ninjapack_tid_prefix[system_id] %}
                       , CASE
                             WHEN orders.tracking_id rlike '({{ ninjapack_tid_prefix[system_id] | join('|') }})' THEN 1
                             ELSE 0
                         END
                {%- else %}
                       , 0
                {%- endif %}
                         AS ninjapack_flag
                       , cast(orders.rts as bigint) AS rts_flag

                       , CASE
                {%- for size, id in parcel_size_to_id.items() %}
                             WHEN orders.parcel_size_id = {{ id }} THEN '{{ size }}'
                {%- endfor %}
                             ELSE NULL
                         END AS parcel_size

                       , cast(get_json_object(orders.dimensions, '$.width') as double) AS nv_width
                       , cast(get_json_object(orders.dimensions, '$.height') as double) AS nv_height
                       , cast(get_json_object(orders.dimensions, '$.length') as double) AS nv_length
                       , cast(get_json_object(orders.dimensions, '$.weight') as double) AS nv_weight
                       , orders.weight
                       , cast(get_json_object(orders.data, '$.originalWeight') as double) AS original_weight
                       , if(
                            orders.created_at >= to_timestamp('{{ deployment_time }}')
                            , cast(package_content as string)
                            , get_json_object(orders.shipper_ref_metadata, '$.items')
                        ) AS items
                       , orders.instruction AS delivery_instructions
                       , coalesce(
                            get_json_object(orders.pricing_info, '$.pricing_request.params.from_billing_zone')
                            , get_json_object(orders.pricing_info, '$.fromBillingZone.billingZone'))
                         AS from_billing_zone
                       , coalesce(
                            get_json_object(orders.pricing_info, '$.pricing_request.params.to_billing_zone')
                            , get_json_object(orders.pricing_info, '$.toBillingZone.billingZone'))
                         AS to_billing_zone

                {%- for level in ('l1', 'l2', 'l3') %}
                       , get_json_object(orders.pricing_info, '$.fromBillingZone.{{ level }}_id')
                         AS from_{{ level }}_id
                       , get_json_object(orders.pricing_info, '$.fromBillingZone.{{ level }}_name')
                         AS from_{{ level }}_name
                       , get_json_object(orders.pricing_info, '$.toBillingZone.{{ level }}_id')
                         AS to_{{ level }}_id
                       , get_json_object(orders.pricing_info, '$.toBillingZone.{{ level }}_name')
                         AS to_{{ level }}_name
                {%- endfor %}

                       , orders.service_type
                       , delivery_types.name AS delivery_type
                       , orders.type AS order_type
                       , get_json_object(orders.shipper_ref_metadata, '$.delivery_verification_mode')
                         AS delivery_verification_mode
                       , orders.from_postcode
                       , orders.to_postcode
                       , orders.from_name
                       , orders.from_address1
                       , orders.from_address2
                       , orders.from_city
                       , orders.from_country
                       , orders.to_name
                       , orders.to_address1
                       , orders.to_address2
                       , orders.to_city
                       , orders.to_country
                       , orders.stamp_id
                       , get_json_object(orders.data, '$.previousDeliveryDetails[0].address1') AS original_to_address1
                       , get_json_object(orders.data, '$.previousDeliveryDetails[0].address2') AS original_to_address2
                       , get_json_object(orders.data, '$.previousDeliveryDetails[0].city') AS original_to_city
                       , get_json_object(orders.data, '$.previousDeliveryDetails[0].country') AS original_to_country
                       , get_json_object(orders.data, '$.previousDeliveryDetails[0].latitude') AS original_to_latitude
                       , get_json_object(orders.data, '$.previousDeliveryDetails[0].longitude') AS original_to_longitude
                       , from_utc_timestamp(
                            from_unixtime(get_json_object(shipper_ref_metadata,
                            '$.platformInfo.platformOrderCreationTime')/1000
                        )
                        , '{{ local_timezone }}' ) AS platform_creation_datetime
                       , orders.comments
                       , if  ((ms.marketplace_id = 324763 or orders.shipper_id = 132156) and '{{ system_id }}' = 'my',
                            case
                                when lower(shipper_ref_metadata) like '%packageorigintype":"crossborder%'
                                or lower(shipper_ref_metadata) like '%firstmiletplslug":"cainiao%'
                                or orders.tracking_id like 'LXST%'
                                or orders.tracking_id like 'NLMYXST%' then 'XB'
                                when lower(shipper_ref_metadata) like '%firstmiletplslug":"lex%' then 'LEX'
                                when lower(shipper_ref_metadata) like '%shippingtype":"warehouse%' then 'WH'
                                when lower(shipper_ref_metadata) like '%shippingtype":"marketplace%' then 'MP'
                            end,
                            if  (ms.marketplace_id = 332853 or orders.shipper_id = 132887,
                                case
                                    when (orders.tracking_id like 'NMYD%' and get_json_object(shipper_ref_metadata, '$.destination.name') = 'Lazada Return WH - KLP Logistics Hub') then 'DRTW'
                                    when (orders.tracking_id like 'NMYD%' and get_json_object(shipper_ref_metadata, '$.destination.name') != 'Lazada Return WH - KLP Logistics Hub') then 'DRTM'
                                    when get_json_object(shipper_ref_metadata, '$.destination.name') = 'Lazada Return WH - KLP Logistics Hub' then 'RTW'
                                    else 'RTM'
                                end,
                                NULL))
                            as shipper_group_my
                       , cast(po.billing_weight as double) as billing_weight
                       , get_json_object(shipper_ref_metadata, '$.package_id') as package_id
                       , third_party_vendors.name as third_party_shipper_name
                       , from_utc_timestamp(orders.created_at, '{{ local_timezone }}') AS creation_datetime
                       , date_format(orders.created_at, 'yyyy-MM') AS created_month
                FROM orders
                LEFT JOIN delivery_types ON delivery_types.id = orders.delivery_type_id
                LEFT JOIN cods ON cods.id = orders.cod_id
                LEFT JOIN order_details ON order_details.order_id = orders.id
                LEFT JOIN third_party_orders ON orders.id = third_party_orders.order_id 
                    AND nv_system_id = '{{system_id}}'
                LEFT JOIN third_party_vendors ON third_party_vendors.legacy_id = third_party_orders.third_party_vendor_legacy_id 
                    AND third_party_orders.nv_system_id  =third_party_vendors.nv_system_id
                LEFT JOIN marketplace_sellers ms on ms.seller_id = orders.shipper_id
                LEFT JOIN pricing_orders po on orders.id = po.order_id and '{{ system_id }}' = lower(po.system_id)
                    and po.billing_weight is not null
                LEFT JOIN shippers ON orders.global_shipper_id = shippers.id
                WHERE orders.deleted_at is NULL

                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "deployment_time": DEPLOYMENT_TIME,
                    "ninjapack_tid_prefix": {
                        "id": ("NPCDK", "NPCAK", "NINID"),
                        "my": ("MYPAC", "NINMY"),
                        "ph": ("NINPH",),
                        "sg": ("NVPST", "NINSG"),
                        "vn": ("NINVN",),
                    },
                    "parcel_size_to_id": {"s": 0, "m": 1, "l": 2, "xl": 3, "xxl": 4, "xs": 5},
                    "status_enriched": {
                        # completed needs to go first because of additional filter on status
                        "completed": ("Completed", "Transferred to 3PL"),
                        "pending": (
                            "Pending Pickup",
                            "Pending Pickup at Distribution Point",
                            "Staging",
                            "Pickup fail",
                            "Cross Border Transit",
                            "En-route to Sorting Hub",
                            "Van en-route to pickup",
                        ),
                        "returned_to_sender": ("Returned to Sender",),
                        "cancelled": ("Cancelled",),
                        "on_hold": ("On Hold",),
                        "transit": (
                            "Arrived at Sorting Hub",
                            "On Vehicle for Delivery",
                            "Pending Reschedule",
                            "Arrived at Origin Hub",
                            "Arrived at Distribution Point",
                            "Transferred to 3PL",
                        ),
                    },
                    "currency_columns": {
                        "cost": "orders.cost",
                        "insurance_value": "orders.insurance",
                        "gst_fee": "get_json_object(orders.pricing_info, '$.gst')",
                        "delivery_fee": "get_json_object(orders.pricing_info, '$.delivery_fee')",
                        "cod_value": "cods.goods_amount",
                    },
                    # TODO: create a dimension table with historical exchange rate as time series?
                    "sgd_exchange_rate": {
                        "id": 10272.36,
                        "my": 3.02,
                        "ph": 37.44,
                        "sg": 1,
                        "th": 22.4,
                        "vn": 17171.05,
                        "mm": 1001.89,
                    },
                },
            ),
            base.TransformView(
                view_name="group_to_remove_dups",
                jinja_template="""
                select 
                    order_id 
                    ,tracking_id     
                    ,third_party_tracking_id
                    ,shipper_reference_number
                    ,status   
                    ,granular_status
                    ,status_enriched
                    ,shipper_id
                    ,seller_id   
                    ,seller_name
                    ,lazmall_flag
                    ,lex_flag
                    ,source_id
                    ,plugin_name
                    ,cod_id   
                    ,cost  
                    ,cost_sgd
                    ,insurance_value
                    ,insurance_value_sgd
                    ,gst_fee
                    ,gst_fee_sgd
                    ,delivery_fee
                    ,delivery_fee_sgd
                    ,cod_value
                    ,cod_value_sgd
                    ,ninjapack_flag
                    ,rts_flag
                    ,parcel_size
                    ,nv_width
                    ,nv_height
                    ,nv_length
                    ,nv_weight
                    ,weight
                    ,original_weight
                    ,items
                    ,delivery_instructions
                    ,from_billing_zone
                    ,to_billing_zone
                    ,from_l1_id
                    ,from_l1_name
                    ,to_l1_id
                    ,to_l1_name
                    ,from_l2_id
                    ,from_l2_name
                    ,to_l2_id
                    ,to_l2_name
                    ,from_l3_id
                    ,from_l3_name
                    ,to_l3_id
                    ,to_l3_name
                    ,service_type
                    ,delivery_type
                    ,order_type
                    ,delivery_verification_mode
                    ,from_postcode
                    ,to_postcode
                    ,to_name
                    ,from_name
                    ,from_address1
                    ,from_address2
                    ,from_city
                    ,from_country
                    ,to_address1
                    ,to_address2
                    ,to_city
                    ,to_country
                    ,stamp_id
                    ,original_to_address1
                    ,original_to_address2
                    ,original_to_city
                    ,original_to_country
                    ,original_to_latitude
                    ,original_to_longitude
                    ,platform_creation_datetime
                    ,comments
                    ,shipper_group_my
                    ,billing_weight
                    ,package_id
                    ,third_party_shipper_name
                    ,creation_datetime
                    ,created_month
                    , count(*)
                from final_view
                group by {{ range(1, 82) | join(',') }}
                """,
            ),
            base.TransformView(
                view_name="after_removing_dups",
                jinja_template="""
                select 
                    order_id 
                    ,tracking_id     
                    ,third_party_tracking_id
                    ,shipper_reference_number
                    ,status   
                    ,granular_status
                    ,status_enriched
                    ,shipper_id
                    ,seller_id   
                    ,seller_name
                    ,lazmall_flag
                    ,lex_flag
                    ,source_id
                    ,plugin_name
                    ,cod_id   
                    ,cost  
                    ,cost_sgd
                    ,insurance_value
                    ,insurance_value_sgd
                    ,gst_fee
                    ,gst_fee_sgd
                    ,delivery_fee
                    ,delivery_fee_sgd
                    ,cod_value
                    ,cod_value_sgd
                    ,ninjapack_flag
                    ,rts_flag
                    ,parcel_size
                    ,nv_width
                    ,nv_height
                    ,nv_length
                    ,nv_weight
                    ,weight
                    ,original_weight
                    ,items
                    ,delivery_instructions
                    ,from_billing_zone
                    ,to_billing_zone
                    ,from_l1_id
                    ,from_l1_name
                    ,to_l1_id
                    ,to_l1_name
                    ,from_l2_id
                    ,from_l2_name
                    ,to_l2_id
                    ,to_l2_name
                    ,from_l3_id
                    ,from_l3_name
                    ,to_l3_id
                    ,to_l3_name
                    ,service_type
                    ,delivery_type
                    ,order_type
                    ,delivery_verification_mode
                    ,from_postcode
                    ,to_postcode
                    ,from_name
                    ,from_address1
                    ,from_address2
                    ,from_city
                    ,from_country
                    ,to_name
                    ,to_address1
                    ,to_address2
                    ,to_city
                    ,to_country
                    ,stamp_id
                    ,original_to_address1
                    ,original_to_address2
                    ,original_to_city
                    ,original_to_country
                    ,original_to_latitude
                    ,original_to_longitude
                    ,platform_creation_datetime
                    ,comments
                    ,shipper_group_my
                    ,billing_weight
                    ,package_id
                    ,third_party_shipper_name
                    ,creation_datetime
                    ,created_month
                from group_to_remove_dups
                """,
            ),
        
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDERS_ENRICHED,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")

    run(spark, task_config)
    spark.stop()
