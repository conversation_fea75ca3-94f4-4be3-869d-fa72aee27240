import logging
from datetime import datetime, timedelta
from pathlib import Path
import json
from airflow.decorators import dag
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.models import Variable
from common.stringcase import kebab_case
from metadata import constants

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

env = Variable.get("env")
tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2023, 10, 1),
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "execution_timeout": constants.Timeout.THREE_HOURS,
    "on_failure_callback": None,
}

def get_table_configs():
    """Returns the list of table configurations"""
    base_path = "gs://nv-data-prod-bi-sensitive-reports"
    standard_partitions = ['system_id', 'created_month']

    return [
        {
            "name": "lazada_orders_base",
            "source_path": f"{base_path}/lazada_orders_base/measurement_datetime=latest",
            "partition_columns": standard_partitions,
            "primary_key": "order_id",
            "strategy": "drop_duplicates"
        },
        {
            "name": "consignee_email_order_features",
            "source_path": f"{base_path}/consignee_email_order_features/measurement_datetime=latest",
            "partition_columns": standard_partitions,
            "primary_key": "order_id",
            "strategy": "drop_duplicates"
        },
        {
            "name": "consignee_phone_number_order_features",
            "source_path": f"{base_path}/consignee_phone_number_order_features/measurement_datetime=latest",
            "partition_columns": standard_partitions,
            "primary_key": "order_id",
            "strategy": "drop_duplicates"
        },
        {
            "name": "parcel_scan_features",
            "source_path": f"{base_path}/parcel_scan_features/measurement_datetime=latest",
            "partition_columns": standard_partitions,
            "primary_key": "order_id",
            "strategy": "drop_duplicates"
        },
        {
            "name": "lazada_hv_order_features",
            "source_path": f"{base_path}/lazada_hv_order_features/measurement_datetime=latest",
            "partition_columns": standard_partitions,
            "primary_key": "order_id",
            "strategy": "drop_duplicates"
        },
        {
            "name": "lazada_hv_order_fraud_prediction",
            "source_path": f"{base_path}/lazada_hv_order_fraud_prediction/measurement_datetime=latest",
            "partition_columns": ['system_id', 'inbound_date'],
            "primary_key": "order_id",
            "strategy": "drop_duplicates"
        },
    ]

@dag(
    dag_id="deduplication_dag",
    default_args=default_args,
    description="DAG to deduplicate tables using Spark",
    schedule_interval="0 0 * * *",
    catchup=False,
    max_active_runs=1,
    tags=["dwh", "data_warehouse", "deduplication"],
)
def deduplication_dag():
    """DAG for deduplicating tables using Spark jobs."""
    logger.info("Starting deduplication DAG")

    table_configs = get_table_configs()

    for config in table_configs:
        task_name = f"deduplicate_{config['name']}"
        config_json = json.dumps(config)

        dedup_task = SparkSubmitOperator(
            task_id=task_name,
            name=kebab_case(task_name),
            application=f"{tasks_path}/table_dedup.py",
            application_args=[config_json],
            conn_id="spark_default",
            conf={
                **spark_conf.get(tables_to_size.get(task_name, "medium"), {}),
                "spark.sql.adaptive.enabled": True,
                "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
            },
        )

dedup_dag = deduplication_dag()