import json
import logging
import os
import sys
from tempfile import NamedTemporaryFile

import pandas as pd
import pendulum
from google.cloud import storage
from simple_salesforce import Salesforce

from common.stringcase import snake_case

PARTITION_COLUMN = "nv_updated_date"

_PRIMITIVE_TYPE_TO_PANDAS_TYPE = {
    "xsd:boolean": "bool",
    "xsd:dateTime": "datetime64",
    "xsd:date": "datetime64",
    "xsd:double": "double",
    "xsd:int": "double",  # Note: int32 doesn't support None values, so we use double
    "xsd:base64Binary": "unicode",
    "xsd:string": "unicode",
    "xsd:time": "unicode",  # Note: pandas don't have a datatype to cater for xsd:time of format `00:00:00`
    "xsd:anyType": "unicode",
    "tns:ID": "unicode",  # Note: a wrapper around xsd:string
}


def list_blobs(bucket_name, obj_root, partition_column):
    client = storage.Client()
    bucket = client.get_bucket(bucket_name)
    blobs = list(bucket.list_blobs(prefix=obj_root))
    filtered_blobs = [blob for blob in blobs if partition_column in blob.name[len(obj_root) :]]
    return filtered_blobs


def upload_to_gcs(bucket_name, gs_obj_name, local_file_path):
    client = storage.Client()
    bucket = client.get_bucket(bucket_name)
    blob = bucket.blob(gs_obj_name)
    blob.upload_from_filename(local_file_path)


def process_datetime_columns(df, schema):
    datetime_columns = [column for column, dtype in schema.items() if dtype == "datetime64"]

    for column in datetime_columns:
        df.loc[(df[column] <= "1677-09-21") | (df[column] >= "2262-04-11"), column] = pd.NaT


def make_query_pandas_df(from_dt, to_dt, select, from_obj, where=None, include_deleted=True):
    schema = get_pandas_schema(from_obj)

    if from_obj == "ContentDocumentLink":
        soql = f"""SELECT (Select Id,LinkedEntityId, ContentDocumentId, SystemModstamp, IsDeleted, ShareType, Visibility FROM ContentDocumentLinks) FROM ContentDocument WHERE LastModifiedDate >= {from_dt} AND LastModifiedDate < {to_dt}"""
    else:
        soql = construct_soql(schema, select, from_obj, where)

    logging.info(f"Making query to Salesforce:\n{soql}")
    resp = sf_client.query_all(soql, include_deleted=include_deleted)

    logging.info(f"Received results: Total size: {resp['totalSize']}; Done: {resp['done']}")

    if not resp["records"]:
        return pd.DataFrame()

    if from_obj == "ContentDocumentLink":
        data = []
        for record in resp["records"]:
            for link_record in record["ContentDocumentLinks"]["records"]:
                link_data = [
                    link_record["Id"],
                    link_record["LinkedEntityId"],
                    link_record["ContentDocumentId"],
                    link_record["IsDeleted"],
                    link_record["SystemModstamp"],
                    link_record["ShareType"],
                    link_record["Visibility"],
                ]
                data.append(link_data)
        df = pd.DataFrame(
            data,
            columns=[
                "Id",
                "LinkedEntityId",
                "ContentDocumentId",
                "IsDeleted",
                "SystemModstamp",
                "ShareType",
                "Visibility",
            ],
        )
    else:
        df = pd.DataFrame.from_records(resp["records"], exclude=["attributes"])

    process_datetime_columns(df, schema)
    for column, dtype in schema.items():
        if dtype == "datetime64":
            df[column] = pd.to_datetime(df[column], utc=True).dt.tz_localize(None)
        else:
            df[column] = df[column].astype(dtype)

    return df


def get_pandas_schema(obj):
    """Returns the pandas schema of a SF object"""
    schema = {}
    desc = sf_client.__getattr__(obj).describe()

    for f in desc["fields"]:
        name, sf_type = f["name"], f["soapType"]
        if sf_type in _PRIMITIVE_TYPE_TO_PANDAS_TYPE:
            schema[name] = _PRIMITIVE_TYPE_TO_PANDAS_TYPE[sf_type]
    return schema


def construct_soql(schema, select, from_obj, where):
    fields = schema.keys() if select == "*" else select
    soql = f"SELECT {','.join(fields)}\nFROM {from_obj}\nWHERE {where}"
    return soql


def describe_object(obj):
    """Returns the description of a SF object's schema and its metadata"""
    desc = sf_client.__getattr__(obj).describe()
    return _ordered_dict_to_dict(desc)


def get_available_fields(obj):
    """Returns the names of the fields."""
    desc = describe_object(obj)
    return [f["name"] for f in desc["fields"] if f["soapType"] in _PRIMITIVE_TYPE_TO_PANDAS_TYPE]


def _ordered_dict_to_dict(od):
    return json.loads(json.dumps(od))


if __name__ == "__main__":
    # Connect to Salesforce
    if os.environ.get("domain") == "":
        domain = None
    else:
        domain = os.environ.get("domain")
    sf_client = Salesforce(
        username=os.environ["SF_USERNAME"],
        password=os.environ["SF_PASSWORD"],
        security_token=os.environ["SF_SECURITY_TOKEN"],
        domain=domain,
        instance_url=os.environ["instance_url"],
        version=os.environ["version"],
    )

    storage_client = storage.Client()

    obj_name = os.environ["name"]

    logging.info("Extracting variables from config name: {}".format(obj_name))

    updated_at_field = os.environ["updated_at_field"]

    logging.info("Extracting variables from updated_at: {}".format(updated_at_field))

    obj_name_sc = snake_case(obj_name)
    obj_root = f"{os.environ['GCS_OBJECT_BASE_PATH']}/{obj_name_sc}/"
    blobs = list_blobs(bucket_name=os.environ["GCS_BUCKET"], obj_root=obj_root, partition_column=PARTITION_COLUMN)
    snapshot_from_date_env = os.environ.get("SNAPSHOT_FROM_DATE")
    should_snapshot = len(blobs) == 0 or os.environ["SNAPSHOT"] == "true"
    snapshot_from_date = None

    if snapshot_from_date_env:
        snapshot_from_date = pendulum.parse(snapshot_from_date_env)

    from_dt_str = os.environ.get("AIRFLOW_EXECUTION_DATE")
    from_dt = pendulum.parse(from_dt_str)

    logging.info(" from_dt: {}".format(from_dt))

    to_dt_str = os.environ.get("AIRFLOW_NEXT_EXECUTION_DATE")
    to_dt = pendulum.parse(to_dt_str)

    logging.info("to_dt {}".format(to_dt))

    # Format from_dt and to_dt before passing them to make_query_pandas_df
    from_dt_formatted = from_dt.format('YYYY-MM-DDTHH:mm:ssZ')  # Format for SOQL
    to_dt_formatted = to_dt.format('YYYY-MM-DDTHH:mm:ssZ')  # Format for SOQL

    if should_snapshot:
        where_clause = f"{updated_at_field} < {to_dt_formatted}"
        if os.environ.get("SNAPSHOT_FROM_DATE"):
            snapshot_from_dt = pendulum.parse(os.environ.get("SNAPSHOT_FROM_DATE"))
            where_clause += f" AND {updated_at_field} >= '{snapshot_from_dt.format('YYYY-MM-DDTHH:mm:ssZ')}'"
    else:
        where_clause = f"{updated_at_field} >= {from_dt_formatted} AND {updated_at_field} < {to_dt_formatted}"

    df = make_query_pandas_df(from_dt_formatted, to_dt_formatted, select="*", from_obj=obj_name, where=where_clause)

    if df.empty:
        logging.info("DataFrame is empty. Exiting the job.")
        sys.exit(0)

    df.columns = [snake_case(c) for c in df.columns]
    partition_path = f"{obj_root}{PARTITION_COLUMN}={from_dt.date()}/"
    fname = f"{obj_name_sc}_{os.environ['TS_NODASH']}.snappy.parquet"
    gs_obj_name = partition_path + fname

    if storage_client.get_bucket(os.environ["GCS_BUCKET"]).blob(gs_obj_name).exists():
        logging.info(f"Skipping the upload to GCS Bucket because {gs_obj_name} already exists")
    else:
        with NamedTemporaryFile("w") as tmp:
            df.to_parquet(tmp.name, compression="snappy", engine="fastparquet")
            tmp.flush()

            logging.info(f"Uploading to GCS Bucket: '{os.environ['GCS_BUCKET']}'...")

            upload_to_gcs(os.environ["GCS_BUCKET"], gs_obj_name, tmp.name)

            logging.info("File Uploaded!")