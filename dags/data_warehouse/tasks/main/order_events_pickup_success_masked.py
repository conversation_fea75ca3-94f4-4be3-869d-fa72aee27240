# Do not use this table for general pickup scan metrics, use pickup_scan_events instead. This table is an intermediate
# table for reservations_events, intended to parse reservations related data that's only available in the data JSON
# blob from order_events 'PICKUP SUCCESS' events.
import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_PICKUP_SUCCESS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_PICKUP_SUCCESS_MASKED,
    system_ids=(constants.SystemID.GL,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.EventsProdGL(input_env, is_masked).ORDER_EVENTS,
                view_name="order_events",
                input_range=lookback_ranges.input,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                -- order_events type 39=PICKUP_SUCCESS

                select
                    id as order_event_id
                    , order_id
                    , system_id
                    , get_json_object(data, '$.driver_id') as driver_id
                    , get_json_object(data, '$.route_id') as route_id
                    , get_json_object(data, '$.waypoint_id') as waypoint_id
                    , get_json_object(data, '$.reservation_id') as reservation_id
                    , get_json_object(data, '$.job_type') as job_type
                    , from_utc_timestamp(
                        -- from_unixtime only accepts UNIX timestamps with no milliseconds, i.e. first 10 characters
                        from_unixtime(substr(get_json_object(data, '$.pickup_arrival_timestamp'), 0, 10))
                        , {{ get_local_timezone }}
                    ) as pickup_arrival_datetime
                    , get_json_object(data, '$.latitude') as pickup_latitude
                    , get_json_object(data, '$.longitude') as pickup_longitude 
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) AS created_at
                    , date_format(from_utc_timestamp(created_at, {{ get_local_timezone }}), 'yyyy-MM') AS created_month
                from order_events
                where
                    type = 39
                    and get_json_object(data, '$.reservation_id') is not null
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("system_id")},
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_EVENTS_PICKUP_SUCCESS,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
