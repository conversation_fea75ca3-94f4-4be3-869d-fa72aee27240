import sys

from pyspark.sql import SparkSession

from common import hive_metastore
from common.utils.helper import map_schema
from common.utils.gcs import get_nv_data_bucket_uri

PARTITION_COLUMNS = ("created_month",)
HIVE_DB_FORMAT = "datalake_{schema}"


def update_hive_metastore(spark, gs_path, schema, table):
    if schema.startswith("amast"):
        system_id = schema.split("_")[-1]
        print(system_id)
        delta_path = f"gs://nv-data-prod-data-lake/amast/{table}/"
        PARTITION_COLUMNS = (system_id, "nv_created_month")
    else:
        delta_path = f"{gs_path}/{map_schema(schema)}/{table}"
    hive_schema = HIVE_DB_FORMAT.format(schema=schema)

    print(f"Updating Hive metastore for {delta_path}")
    
    if schema == "data_sanity":
        PARTITION_COLUMNS = ("schema", "table", "execution_date")
    else:
        PARTITION_COLUMNS = ("created_month",)
    hive_metastore.update_metastore(spark, hive_schema, PARTITION_COLUMNS, table, delta_path)


if __name__ == "__main__":
    env, schema, table = sys.argv[1:]
    gs_path = get_nv_data_bucket_uri(env, bucket_type="db", schema = map_schema(schema), strip=False)

    spark = SparkSession.builder.enableHiveSupport().getOrCreate()
    update_hive_metastore(spark, gs_path, schema, table)
    spark.stop()
