import json
from datetime import timed<PERSON><PERSON>
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.metabase import TABLES
from metadata.constants import Timeout
from metadata.spark_conf import SPARK_CONF
from metadata.table_sizes import TABLE_SIZES

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")

env = Variable.get("env")
medium_tables = TABLE_SIZES["cdc_hive"]["medium"]
large_tables = TABLE_SIZES["cdc_hive"]["large"]
spark_conf = SPARK_CONF[env]["cdc_hive"]
gcs_bucket = f"nv-data-{env}-datalake"
env_tables = TABLES[env]

default_args = {
    "owner": "airflow",
    "start_date": "2020-05-21",
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.ONE_HOUR,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

with DAG(
        dag_id="datalake_db_cdc_hive_metastore_integration",
        default_args=default_args,
        schedule_interval="@daily",
        catchup=False,
        concurrency=50,
        sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
        tags=["data_lake", "db_cdc"],
) as dag:
    for schema, tables in env_tables.items():
        for table in tables:
            conf = next(
                (
                    spark_conf[size]
                    for size in ["large", "medium"]
                    if f"{schema}.{table}" in TABLE_SIZES["cdc_hive"][size]
                ),
                spark_conf["small"],
            )
            update_hms = SparkSubmitOperator(
                task_id=f"update_hive_metastore_{schema}_{table}",
                name=kebab_case(f"update-hms-{schema}-{table}"),
                execution_timeout=Timeout.ONE_HOUR,
                application=f"{tasks_path}/update_hive_metastore.py",
                application_args=[env, schema, table],
                conn_id="spark_default",
                conf=conf,
            )
