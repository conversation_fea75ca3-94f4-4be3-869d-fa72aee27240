---
  - name: Gather current branch name
    shell: "git branch | grep \\* | cut -d ' ' -f2"
    register: current_git_branch

  - name: Store current branch name
    set_fact:
      branch: "{{ current_git_branch.stdout }}"

  - name: Clean dist docker directory
    file:
      path: "dist/docker/{{ svc }}"
      state: absent

  - name: Create dist docker directory
    file:
      path: "dist/docker/{{ svc }}"
      state: directory

  - name: Gather git details
    shell: git describe --tags --long
    register: git_info

  - name: Register git tag
    set_fact:
      tag: "-{{ git_info.stdout }}"

  - name: Include {{ svc }} specific tasks
    include_tasks: "{{ svc }}.yml"

  - name: Generate dockerfile
    template:
      src: "{{ svc }}.Dockerfile.j2"
      dest: "dist/docker/{{ svc }}/Dockerfile"

  - name: set docker build tags
    set_fact:
      full_tag: "{{ docker_registry }}/nv-data-{{ branch | lower }}-{{ svc }}-dags:{{ versions[svc] }}{{ tag }}"
      latest_tag: "{{ docker_registry }}/nv-data-{{ branch | lower }}-{{ svc }}-dags:{{ versions[svc] }}-latest"

  - name: Build docker image versions[service]
    command: "podman build -t {{ full_tag }} -t {{ latest_tag }} dist/docker/{{ svc }}"

  - name: Push docker image
    command: "podman push {{ full_tag }}"

  - name: Push docker image
    command: "podman push {{ latest_tag }}"

  - name: Create artifacts
    copy:
      dest: "dist/docker/{{ svc }}/{{ svc }}.env"
      content: |
        image_registry: "{{ docker_registry }}"
        image_name: "nv-data-{{ branch | lower }}-{{ svc }}-dags"
        image_tag: "{{ versions[svc] }}{{ tag }}"
        git_branch: "{{ branch }}"
        spark_image_tag: "{{ versions.spark }}{{ tag }}"
        spark_image_name: "nv-data-{{ branch | lower }}-spark-dags"
