from datetime import datetime, timedelta

from airflow import DAG
from airflow.models import Variable

from gsheets.operators.gsheets_to_gcs_operator import GoogleSheetsToGcsOperator

from common.airflow import notifications as notif
from metadata.constants import Timeout

ALERT_CHANNEL = "chat_dwh_alert"
env = Variable.get("env")
spreadsheets = Variable.get("spreadsheets", deserialize_json=True, default_var=[])

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(2020, 2, 18),
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.ONE_HOUR if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


with DAG(
    dag_id="datalake_gsheets_to_gcs",
    default_args=default_args,
    schedule_interval="0 17 * * *",
    sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
    catchup=False,
    on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
    tags=["data_lake"],
) as dag:
    for spreadsheet in spreadsheets:
        for sheet_name in spreadsheet["sheet_names"]:
            GoogleSheetsToGcsOperator(
                task_id=f"{spreadsheet['task_id']}_{sheet_name}",
                spreadsheet_id=spreadsheet["spreadsheet_id"],
                sheet_name=sheet_name,
                gcs_bucket=f"nv-data-{env}-bi-sensitive-reports"
                if spreadsheet["sensitive_flag"]
                else f"nv-data-{env}-data-lake",
                gcs_folder_path="gsheets",
                gsheets_conn_id="google_cloud_default",
                gcs_conn_id="google_cloud_default",
                params={"alert_channel": spreadsheet.get("alert_channel", ALERT_CHANNEL)},
            )
