import sys

from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import data_warehouse, versioned_parquet_tables_masked, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FakePhysicalParcelDAG.Task.ID_CLAIMS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.FakePhysicalParcelDAG.Task.ID_CLAIMS_ENRICHED_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_MILESTONES_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.ShippersDAG.DAG_ID,
            task_id=data_warehouse.ShippersDAG.Task.SHIPPER_ATTRIBUTES_MASKED,
        ),
    ),
    system_ids=(SystemID.ID,),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=("system_id", "created_month")),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    input_env = "prod"

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).FMO_DATA_CLAIM_TO_BI_CLAIM_2024,
                view_name="claim_2024",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).FMO_DATA_CLAIM_TO_BI_CLAIM_2025,
                view_name="claim_2025",
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_MILESTONES,
                view_name="order_milestones",
                system_id=system_id,
                version_datetime="latest",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPPER_ATTRIBUTES,
                view_name="shipper_attributes",
                system_id=system_id,
                version_datetime="latest",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="claims",
                jinja_template="""
                -- Union 2024 and 2025 claims from two tables

                select
                    datetime
                    , tracking_id
                    , claim_type
                    , claim_amount
                from claim_2024

                union all

                select
                    datetime
                    , tracking_id
                    , claim_type
                    , claim_amount
                from claim_2025   

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                select
                    to_date(claims.datetime, 'd-MMM-yy') as claim_datetime
                    , claims.tracking_id
                    , claims.claim_type as claim_type
                    , cast(regexp_replace(claims.claim_amount, ',', '') as int) as claim_amount
                    , case
                        when cast(regexp_replace(claims.claim_amount, ',', '') as int) >= 500000 then '>=500k'
                        else '<500k'
                    end as claim_amount_category
                    , date_format(to_date(claims.datetime, 'd-MMM-yy'), 'yyyy-MM') as claim_month
                    , oms.order_id 
                    , oms.granular_status
                    , oms.cod_value 
                    , oms.insurance_value 
                    , oms.system_id
                    , oms.creation_datetime 
                    , oms.created_month
                    , sa.id as shipper_id
                    , sa.shipper_name 
                    , sa.sales_channel 
                    , sa.parent_id_coalesce 
                    , sa.parent_name_coalesce
                    , sa.sf_nv_product_line
                from claims
                left join order_milestones as oms
                    on claims.tracking_id = oms.tracking_id
                left join shipper_attributes as sa
                    on oms.shipper_id = sa.id   

                """,
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ID_CLAIMS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    run(spark, task_config)
    spark.stop()