import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession

from common.utils.gcs import get_nv_data_bucket_uri


def vacuum_delta_table(spark, env, schema, table, retention_hours):
    """
    Vacuums older files in a Delta table up to the specified retention threshold (30 days)

    :param spark:   Spark session
    :param env:     Data lake environment
    :param schema:  DB schema
    :param table:   DB table
    """
    gs_path = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=False)
    path = f"{gs_path}/{schema}/{table}"
    print(f"Running vacuum on {path} with rentention hours: {retention_hours}")
    delta_table = DeltaTable.forPath(spark, path)
    delta_table.vacuum(float(retention_hours))


if __name__ == "__main__":
    env, schema, table, retention_hours = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    vacuum_delta_table(spark, env, schema, table, retention_hours)
    spark.stop()