import json
from datetime import datetime
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from data_warehouse.tasks.main.config import dag_configs
from data_warehouse.utils import airflow
from metadata.constants import Timeout
from metadata.data_warehouse import CalendarMaskedDAG, ShipperLifetimeValueDAG

DEFAULT_SCHEDULE = "0 18 * * *"  # Daily 2am SGT
DAG_ID = "data_warehouse_full_run"

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
spark_conf = Variable.get("spark_conf", deserialize_json=True)["dwh"]
size_to_tables = Variable.get("table_sizes", deserialize_json=True)["dwh"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}

default_args = {
    "owner": "airflow",
    "start_date": datetime(2022, 9, 1),
    "retries": 0,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

full_run_variable = json.loads(Variable.get("data_warehouse_full_run", "{}"))

task_operators = {}
with DAG(
        catchup=False,
        dag_id=DAG_ID,
        default_args=default_args,
        max_active_runs=1,
        schedule_interval=DEFAULT_SCHEDULE,
        tags=["data_warehouse"],
) as dag:
    for dag_config in dag_configs.values():
        for config in dag_config["task_configs"]:
            task_name = config.task_name
            start = DummyOperator(task_id=f"{task_name}_start")
            end = DummyOperator(task_id=f"{task_name}_end")

            for system_id in config.system_ids:
                last_measurement_datetime_str = "{{ execution_date }}"
                measurement_datetime_str = "{{ next_execution_date }}"
                enable_full_run_str = "True"

                task_id = f"{task_name}_{system_id}"

                if full_run_variable:
                    task_variable = full_run_variable.get(task_id, {})
                    start_date = task_variable.get("start_date")
                    end_date = task_variable.get("end_date")

                    if start_date and end_date:
                        last_measurement_datetime_str = start_date
                        measurement_datetime_str = end_date
                        enable_full_run_str = "False"

                input_args = {
                    "env": env,
                    "last_measurement_datetime_str": last_measurement_datetime_str,
                    "measurement_datetime_str": measurement_datetime_str,
                    "enable_full_run_str": enable_full_run_str,
                    "system_id": system_id,
                }

                task = SparkSubmitOperator(
                    task_id=task_id,
                    name=kebab_case(task_id),
                    application=f"{tasks_path}/main/{config.py_file}",
                    application_args=[json.dumps(input_args, separators=(",", ":"))],
                    conn_id="spark_default",
                    conf={
                        **spark_conf[tables_to_size.get(task_id, "small")],
                        "spark.sql.adaptive.enabled": True,
                        "spark.kubernetes.driver.label.ninja-spark-dwh-etl": task_id,
                        "spark.kubernetes.executor.label.ninja-spark-dwh-etl": task_id,
                        "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                        "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
                    },
                    execution_timeout=Timeout.EIGHT_HOURS,
                    params={"enable_full_run": True},
                    trigger_rule="one_failed",  # trigger task only when {task_id}_start failed, skip by default
                )
                start >> task >> end
            task_operators[task_name] = {"start": start, "end": end}

            # Create tasks to refresh Hive Metastore
            hms_config = config.hive_metastore_config
            if hms_config:
                update_hms_tasks = {}
                update_sing_hms_tasks = {}
                for conf in hms_config:
                    update_hms_tasks[conf.hive_schema] = airflow.get_update_hms_task(env, tasks_path, task_name, conf)
                    update_sing_hms_tasks[conf.hive_schema] = airflow.get_update_sing_hms_task(env, tasks_path,
                                                                                               task_name, conf)
                    end >> update_hms_tasks[conf.hive_schema] >> update_sing_hms_tasks[conf.hive_schema]
    for dag_config in dag_configs.values():
        for config in dag_config["task_configs"]:
            # Define internal dependencies after creating all tasks as tasks are not created in topological order
            for dep in config.depends_on:
                task_operators[dep]["end"] >> task_operators[config.task_name]["start"]
            # Create ExternalTaskSensor for external dependencies
            for ext_dep in config.depends_on_external:
                dep = ext_dep.task_id
                calendar_masked_list = [
                    value for name, value in vars(CalendarMaskedDAG.Task).items() if not name.startswith("_")
                ]
                shipper_list = [
                    value for name, value in vars(ShipperLifetimeValueDAG.Task).items() if not name.startswith("_")
                ]
                if dep not in calendar_masked_list and dep not in shipper_list and not dep.startswith("pii_delta"):
                    task_operators[dep]["end"] >> task_operators[config.task_name]["start"]
