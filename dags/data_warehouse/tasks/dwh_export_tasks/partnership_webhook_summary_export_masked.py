from common.stringcase import kebab_case
from data_warehouse.tasks.dwh_export_tasks import base
from datetime import timedelta
from metadata import data_warehouse, versioned_parquet_tables_masked

SQL = """
select 
    shipper
    , system_id
    , kpi_type
    , total_parcels
    , success_parcels
    , parcels_with_no_breaches
    , delivery_success_date
from 
    input_table
order by delivery_success_date desc
"""

TASK_NAME = data_warehouse.DataWarehouseExportDAG.Task.PARTNERSHIP_WEBHOOK_SUMMARY_EXPORT_MASKED

input_env = 'prod'

def get_task_config(env):
    airflow_config = base.AirflowConfig(
        task_id=TASK_NAME,
        task_name=kebab_case(TASK_NAME),
        conf={
            "spark.executor.instances": "60",
			"spark.executor.cores": "6",
            "spark.executor.memory": "10300m",
            "spark.kubernetes.executor.request.cores": "3600m",
            "spark.driver.memory": "8500m",
            "spark.kubernetes.driver.request.cores": "2200m",
        },
        input_path=versioned_parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_SUMMARY_STATS
        + "/measurement_datetime=latest",
        sql=SQL,
        gcs_check_prefix=versioned_parquet_tables_masked.DataWarehouse(input_env).PARTNERSHIP_WEBHOOK_SUMMARY_STATS.split("/")[-1]
        + "/measurement_datetime=latest",
        alert_channel="google_chat",
        output_path=f"gs://nv-data-{input_env}-webhook-report/partnership_webhook_summary_stats.csv",
        depends_on_external=(
            base.DependsOnExternal(
                dag_id=data_warehouse.WebhookSnapshotDAG.DAG_ID,
                task_id=data_warehouse.WebhookSnapshotDAG.Task.PARTNERSHIP_WEBHOOK_SUMMARY_STATS_MASKED,
            ),
        ),
    )
    return airflow_config