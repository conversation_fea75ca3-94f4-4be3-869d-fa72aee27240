import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, delta_tables, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.ORIGINAL_CONSIGNEE_INFORMATION_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.ORIGINAL_CONSIGNEE_INFORMATION_MASKED,
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.ORDER_EVENTS_UPDATE_CONTACT_INFORMATION_MASKED,
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrderEventsDAG.DAG_ID,
            task_id=data_warehouse.OrderEventsDAG.Task.UPDATE_ADDRESS_EVENTS_MASKED,
        ),
    ),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    input_env = "prod"
    is_masked = True

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).ORDERS,
                view_name="orders",
                input_range=lookback_ranges.input,
            ),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).ORDER_EVENTS_UPDATE_CONTACT_INFORMATION,
                view_name="order_events_update_contact_information",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).UPDATE_ADDRESS_EVENTS,
                view_name="update_address_events",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="original_address",
                jinja_template="""

                with 
                    original_address_base as (
                        select 
                            order_id
                            , sha2(old_to_address1, 256) as original_to_address1
                            , sha2(old_to_address2, 256) as original_to_address2
                            , sha2(old_to_postcode, 256) as original_to_postcode
                            , old_to_city as original_to_city
                            , old_to_district as original_to_district
                            , old_to_state as original_to_state
                            , row_number() over (partition by order_id order by event_creation_datetime) as change_sequence
                        from update_address_events
                    )

                select *
                from original_address_base 
                where change_sequence = 1

                """,
            ),
            base.TransformView(
                view_name="original_contact",
                jinja_template="""
                with
                    original_contact_base as (

                        select 
                            order_id
                            , sha2(original_consignee_name, 256) as original_to_name
                            , concat(sha2(lower(split(original_consignee_email, "@")[0]), 256), "@", 
                                split(original_consignee_email, "@")[1]) as original_to_email
                            , sha2(original_consignee_contact, 256) as original_to_contact
                            , row_number() over (partition by order_id order by event_timestamp) as change_sequence
                        from order_events_update_contact_information   

                    )

                select 
                    order_id
                    , original_to_name
                    , original_to_email
                    , original_to_contact
                from original_contact_base 
                where change_sequence = 1

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""

                    select
                        orders.id as order_id
                        , from_utc_timestamp(orders.created_at, '{{ local_timezone }}') as creation_datetime
                        , coalesce(original_address.original_to_address1, orders.to_address1) as original_to_address1
                        , coalesce(original_address.original_to_address2, orders.to_address2) as original_to_address2
                        , coalesce(original_address.original_to_postcode, orders.to_postcode) as original_to_postcode
                        , coalesce(original_address.original_to_city, orders.to_city) as original_to_city       
                        , coalesce(original_address.original_to_district, orders.to_district) as original_to_district
                        , coalesce(original_address.original_to_state, orders.to_state) as original_to_state
                        , coalesce(original_contact.original_to_name, orders.to_name) as original_to_name
                        , coalesce(original_contact.original_to_email, orders.to_email) as original_to_email
                        , coalesce(original_contact.original_to_contact, orders.to_contact) as original_to_contact
                        , '{{ system_id }}' as system_id
                        , date_format(orders.created_at, 'yyyy-MM') as created_month
                    from orders
                    left join original_address on
                        orders.id = original_address.order_id
                    left join original_contact on
                        orders.id = original_contact.order_id

                """,
                jinja_arguments={
                    "system_id": system_id,
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                },
            ),
        )
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).ORIGINAL_CONSIGNEE_INFORMATION,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
