from data_warehouse.tasks.shipper_lifetime_tasks import base
from metadata import versioned_parquet_tables_masked

PICKLE_BUCKET_FORMAT = "nv-data-{env}-data-warehouse"
PICKLE_DIRECTORY = "pickles/parent_shipper_lifetime_values/effective_date"
MODEL_TYPES = ("default",)


def get_task_config(gcp_conn_id, env, system_id, next_execution_date):
    input_file_path = versioned_parquet_tables_masked.DataWarehouse(env).PARENT_SHIPPER_LIFETIME_VALUES_BASE
    pickle_bucket = PICKLE_BUCKET_FORMAT.format(env=env)
    output_file_path = versioned_parquet_tables_masked.DataWarehouse(env).PARENT_SHIPPER_LIFETIME_VALUES

    return base.TaskConfig(
        system_id=system_id,
        gcp_conn_id=gcp_conn_id,
        input_file_path=input_file_path,
        pickle_bucket=pickle_bucket,
        pickle_directory=PICKLE_DIRECTORY,
        execution_date=next_execution_date,
        model_types=MODEL_TYPES,
        output_file_path=output_file_path,
    )


def run(**kwargs):
    config = get_task_config(kwargs["gcp_conn_id"], kwargs["env"], kwargs["system_id"], kwargs["next_execution_date"])
    base.run(config)
