from airflow.models import Variable

class DatabaseConfig:
    # MYSQL_TABLES = {
    #     "aaa_prod_gl": ["user_info", "users", "users_groups"],
    #     "core_prod_id": [
    #         "blobs",
    #         "blocked_dates",
    #         "bulk_blob",
    #         "bulk_uploads",
    #         "cod_collections",
    #         "cod_inbounds",
    #         "cods",
    #         "delivery_types",
    #         "dnrs",
    #         "hub_groups",
    #         "hubs",
    #         "inbound_scans",
    #         "industries",
    #         "job_photos",
    #         "oauth_scopes",
    #         "oauth_session_access_tokens",
    #         "oauth_session_token_scopes",
    #         "oauth_sessions",
    #         "order_actions",
    #         "order_batch_items",
    #         "order_batches",
    #         "order_jaro_scores_v2",
    #         "order_pickups",
    #         "order_sla",
    #         "order_tag_names",
    #         "order_tags",
    #         "orders",
    #         "outbound_scans",
    #         "parameters",
    #         "print_templates",
    #         "print_zpl_templates",
    #         "printers",
    #         "reservation_blob",
    #         "reservation_failure_reason",
    #         "reservation_tracking_ids",
    #         "reservations",
    #         "route_logs",
    #         "route_waypoint",
    #         "salespersons",
    #         "shipping_pricing_template_details",
    #         "shipping_pricing_templates",
    #         "stamp_prefix_seq_num",
    #         "third_party_orders",
    #         "third_party_shippers",
    #         "timewindows",
    #         "transaction_blob",
    #         "transaction_failure_reason",
    #         "transactions",
    #         "warehouse_sweeps",
    #         "waypoint_photos",
    #         "waypoints",
    #         "order_delivery_verifications",
    #         "order_details",
    #         "order_tags_search",
    #         "shipper_pickups_search",
    #         "order_delivery_details"
    #     ],
    #     "billing_prod_gl": [
    #         "priced_orders",
    #         "order_pricing_dlq_errors",
    #         "invoiced_orders",
    #         "invoicing_jobs",
    #         "templates",
    #         "report_fields",
    #         "report_schedules",
    #         "journals",
    #         "ledger_orders",
    #         "ledgers",
    #         "configurations",
    #         "shipper_accounts",
    #         "cod_orders",
    #         "transactions",
    #         "invoice_dispute_suites",
    #         "invoice_dispute_types",
    #         "invoice_dispute_rules",
    #         "invoice_dispute_tids",
    #         "invoice_disputes",
    #         "invoice_dispute_tid_issues",
    #         "picked_up_orders",
    #         "volume_discount_orders"
    #     ],
    #     "addressing_prod_gl": [
    #         "address_events", "global_waypoints", "jaro_scores",
    #         "pricing_zones", "pricing_zones_v2", "zones", "feature_switch"
    #     ]
    # }
    MYSQL_TABLES = Variable.get("mysql_tables", deserialize_json=True, default_var={})
    MYSQL_CONNECTIONS = Variable.get("mysql_connections", deserialize_json=True)