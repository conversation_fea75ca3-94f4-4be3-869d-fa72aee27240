---
  - name: Create dist docker scripts directory
    file:
      path: "dist/docker/{{ svc }}/scripts"
      state: directory

  - name: Create dist dags directory
    file:
      path: "dist/docker/{{ svc }}/dags"
      state: directory

  - name: Copy dags
    synchronize:
      src: "../../../../dags"
      dest: "dist/docker/{{ svc }}"

  - name: Create dist plugins directory
    file:
      path: "dist/docker/{{ svc }}/plugins"
      state: directory

  - name: Copy plugins
    synchronize:
      src: "../../../../plugins"
      dest: "dist/docker/{{ svc }}"

  - name: Create dist great_expectations directory
    file:
      path: "dist/docker/{{ svc }}/great_expectations"
      state: directory

  - name: Copy great_expectations
    synchronize:
      src: "../../../../great_expectations"
      dest: "dist/docker/{{ svc }}"

  - name: <PERSON><PERSON> {{ svc }} requirements
    copy:
      src: "../../../../requirements-{{ svc }}.txt"
      dest: "dist/docker/{{ svc }}"

  - name: Copy log4j conf
    synchronize:
      src: "../../../../log4j.properties"
      dest: "dist/docker/{{ svc }}"

  - name: Create keys directory
    file:
      path: "dist/docker/{{ svc }}/keys"
      state: directory

  - name: Get key from secret
    shell: >
      kubectl get secret
      storage-service-account-key
      --context {{ kubernetes_context }}
      -n {{ kubernetes_namespace }}
      -o json | jq '.data["key.json"]
      | @base64d
      | fromjson'
      > dist/docker/{{ svc }}/keys/key.json

  - name: Generate spark-defaults.conf
    template:
      src: "spark-defaults.conf.j2"
      dest: "dist/docker/{{ svc }}/spark-defaults.conf"

  - name: Register spark base image
    set_fact:
      spark_base_img: "{{ docker_registry }}/nv-{{ branch | lower }}-spark:latest"

  - name: Pull spark base image
    command: "podman pull {{ spark_base_img }}"
