import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked, parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.INVOICE_DISPUTE_CASE_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.INVOICE_DISPUTE_CASE_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(
        data_warehouse.SalesforceDAG.Task.SALESFORCE_CS_CASE_ENRICHED_MASKED,
        data_warehouse.SalesforceDAG.Task.SALESFORCE_SS_CASE_ENRICHED_MASKED,
    ),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.CalendarMaskedDAG.DAG_ID, task_id=data_warehouse.CalendarMaskedDAG.Task.CALENDAR_MASKED
        ),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="pricing", partition_columns=()),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.DataWarehouse(input_env).CALENDAR, view_name="calendar"),
        ),
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CASE_ENRICHED, 
                view_name="salesforce_case_enriched"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_CS_CASE_ENRICHED,
                view_name="salesforce_cs_case_enriched"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_SS_CASE_ENRICHED,
                view_name="salesforce_ss_case_enriched"
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.BillingProdGL(input_env, is_masked).INVOICE_DISPUTE_TIDS,
            view_name="invoice_dispute_tids"),
            base.InputTable(path=delta_tables.BillingProdGL(input_env, is_masked).INVOICE_DISPUTES,
            view_name="invoice_disputes"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="case_protected_revenue",
                jinja_template="""
                select
                    invoice_dispute_id
                    , sum(get_json_object(invoice_dispute_tids.pricing_breakdown, '$.total_with_tax')) 
                        as total_protected_revenue
                from invoice_dispute_tids
                -- rejected tids
                where invoice_dispute_tids.resolution = 3
                group by invoice_dispute_id
                """,
            ),
            base.TransformView(
                view_name="tid_status",
                jinja_template="""
                with 
                    status as (
                        select 
                            invoice_dispute_id
                            , sum(if(invoice_dispute_tids.status = 'PENDING', 1, 0)) as count_unresolved
                        from invoice_dispute_tids
                        group by invoice_dispute_id
                    ) 
                    
                    select
                        status.*
                        , if(count_unresolved = 0, 1, 0) as is_resolved
                    from status
                """,
            ),
            base.TransformView(
                view_name="revenue_leakage_weight",
                jinja_template="""
                select
                    invoice_dispute_id
                    , sum(get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.delivery_fee.amount')) 
                        as revised_delivery_fee_weight
                    , sum(get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.cod_fee.amount')) 
                        as revised_cod_fee_weight
                    , sum(get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.rts_fee.amount')) 
                        as revised_rts_fee_weight 
                    , sum(get_json_object(invoice_dispute_tids.pricing_breakdown, '$.delivery_fee.amount')) 
                        as original_delivery_fee_weight
                    , sum(get_json_object(invoice_dispute_tids.pricing_breakdown, '$.cod_fee.amount')) 
                        as original_cod_fee_weight
                    , sum(get_json_object(invoice_dispute_tids.pricing_breakdown, '$.rts_fee.amount')) 
                        as original_rts_fee_weight
                from invoice_dispute_tids
                where invoice_dispute_tids.resolution = 2
                    and invoice_dispute_tids.invoice_dispute_type_name = 'Billing Weight'
                group by invoice_dispute_id
                """,
            ),
            base.TransformView(
                view_name="revenue_leakage_non_weight",
                jinja_template="""
                select
                    invoice_dispute_id
                    , sum(get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.delivery_fee.amount'))
                        as revised_delivery_fee_non_weight
                    , sum(get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.cod_fee.amount'))
                        as revised_cod_fee_non_weight
                    , sum(get_json_object(invoice_dispute_tids.revised_pricing_breakdown, '$.rts_fee.amount'))
                        as revised_rts_fee_non_weight
                    , sum(get_json_object(invoice_dispute_tids.pricing_breakdown, '$.delivery_fee.amount'))
                        as original_delivery_fee_non_weight
                    , sum(get_json_object(invoice_dispute_tids.pricing_breakdown, '$.cod_fee.amount'))
                        as original_cod_fee_non_weight
                    , sum(get_json_object(invoice_dispute_tids.pricing_breakdown, '$.rts_fee.amount'))
                        as original_rts_fee_non_weight
                from invoice_dispute_tids
                -- accepted tids
                where invoice_dispute_tids.resolution = 2
                    and invoice_dispute_tids.invoice_dispute_type_name != 'Billing Weight'
                group by invoice_dispute_id
                """,
            ),
            base.TransformView(
                view_name="local_tz",
                jinja_template="""
                select
                    id
                    , external_ref
                    , lower(system_id) as system_id
                    , shipper_id
                    , shipper_name
                    , from_utc_timestamp(dispute_filed_date, {{ get_local_timezone }}) as dispute_filed_date
                    , status
                    , number_of_tids
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) as created_datetime
                    , from_utc_timestamp(closed_at, {{ get_local_timezone }}) as closed_datetime
                from invoice_disputes
                """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("lower(system_id)")},
            ),
            base.TransformView(
                view_name="invoice_disputes_base",
                jinja_template="""
                select
                    local_tz.id
                    , local_tz.external_ref
                    , local_tz.shipper_id
                    , local_tz.shipper_name
                    , local_tz.dispute_filed_date
                    , local_tz.status
                    , local_tz.number_of_tids
                    , local_tz.created_datetime
                    , local_tz.closed_datetime
                    , local_tz.system_id
                    , date(date_trunc('month', local_tz.dispute_filed_date)) as dispute_filed_month
                    , date(date_trunc('week', local_tz.dispute_filed_date)) as dispute_filed_week
                    , if(closed_datetime is null, 0, 1) as is_closed
                    , case_protected_revenue.total_protected_revenue
                    , tid_status.is_resolved
                    , tid_status.count_unresolved
                    , revenue_leakage_weight.revised_delivery_fee_weight
                    , revenue_leakage_weight.revised_cod_fee_weight
                    , revenue_leakage_weight.revised_rts_fee_weight
                    , revenue_leakage_weight.original_delivery_fee_weight
                    , revenue_leakage_weight.original_cod_fee_weight
                    , revenue_leakage_weight.original_rts_fee_weight
                    , revenue_leakage_non_weight.revised_delivery_fee_non_weight
                    , revenue_leakage_non_weight.revised_cod_fee_non_weight
                    , revenue_leakage_non_weight.revised_rts_fee_non_weight
                    , revenue_leakage_non_weight.original_delivery_fee_non_weight
                    , revenue_leakage_non_weight.original_cod_fee_non_weight
                    , revenue_leakage_non_weight.original_rts_fee_non_weight
                from local_tz
                left join case_protected_revenue
                    on local_tz.id = case_protected_revenue.invoice_dispute_id
                left join tid_status
                    on local_tz.id = tid_status.invoice_dispute_id
                left join revenue_leakage_weight
                    on local_tz.id = revenue_leakage_weight.invoice_dispute_id
                left join revenue_leakage_non_weight
                    on local_tz.id = revenue_leakage_non_weight.invoice_dispute_id
                """,
            ),
            base.TransformView(
                view_name="calendar_working_days",
                jinja_template="""
                with
                    country_calendar as (
                        select
                            country
                            , date
                            , day
                            , comments
                            , if(dayofweek(date) = 1 or dayofweek(date) = 7 or comments is not null, 0, 1)
                                as is_working_day
                            , sum(if(if(dayofweek(date) = 1 or dayofweek(date) = 7 or comments is not null, 0, 1) = 1,
                                1, 0)) over (partition by country order by date asc) as grp
                        from calendar
                        where
                            region = 'national'
                            and country != 'mm'
                    ),

                    regular_calendar as (
                        select
                            country
                            , date
                            , day
                            , comments
                            , is_working_day
                            , date_add(date, days_to_next_working_day) as next_working_day
                        from
                            (
                            select
                                *
                                , row_number() over (
                                    partition by country, grp order by date desc
                                ) as days_to_next_working_day
                            from country_calendar
                            )
                        order by country, date
                    ),

                    working_days as (
                        select
                            country
                            , date
                            , day
                        from regular_calendar
                        where is_working_day = 1
                    ),

                    final as (
                        select
                            rc.country
                            , rc.date
                            , rc.day
                            , rc.comments
                            , rc.is_working_day
                            , rc.next_working_day
                            , wd.date as next_working_dayx
                            , row_number() over(partition by rc.country, rc.date order by wd.date asc)
                                as next_working_day_order
                        from regular_calendar rc
                        left join working_days wd
                            on wd.country = rc.country
                            and wd.date > rc.date
                            and wd.date <= rc.date + interval '21' day
                    )

                    select
                        country
                        , date
                        , day
                        , comments
                        , is_working_day
                        , max(if(next_working_day_order = 1, next_working_dayx, null)) as next_working_day
                        , max(if(next_working_day_order = 2, next_working_dayx, null)) as next_working_day2
                        , max(if(next_working_day_order = 3, next_working_dayx, null)) as next_working_day3
                        , max(if(next_working_day_order = 4, next_working_dayx, null)) as next_working_day4
                        , max(if(next_working_day_order = 5, next_working_dayx, null)) as next_working_day5
                        , max(if(next_working_day_order = 6, next_working_dayx, null)) as next_working_day6
                        , max(if(next_working_day_order = 7, next_working_dayx, null)) as next_working_day7
                    from final
                    where next_working_day_order < 8

                    group by 1, 2, 3, 4, 5
                    order by country, date
                """,
            ),
            base.TransformView(
                view_name="calendar_working_days_filtered",
                jinja_template="""
                select
                    *
                    , row_number() over(partition by country order by date asc) as working_day_rank
                from calendar_working_days
                where is_working_day = 1
                """,
            ),
            base.TransformView(
                view_name="invoice_disputes_final",
                jinja_template="""
                with
                    base as (
                        select
                            invoice_disputes_base.*
                            , case
                                when
                                    calendar_working_days.is_working_day = 0
                                    then to_timestamp(cast(calendar_working_days.next_working_day as STRING) || ' 9',
                                        'yyyy-MM-dd k')
                                when
                                    hour(created_datetime) < 9
                                    then to_timestamp(cast(cast(created_datetime as date) as STRING) || ' 9',
                                        'yyyy-MM-dd k')
                                when
                                    hour(created_datetime) >= 18
                                    then to_timestamp(cast(calendar_working_days.next_working_day as STRING) || ' 9',
                                        'yyyy-MM-dd k')
                                else created_datetime
                            end as creation_datetime_bizhrs
                            , case
                                when
                                    cal2.is_working_day = 0
                                    then to_timestamp(cast(cal2.next_working_day as STRING) || ' 9',
                                        'yyyy-MM-dd k')
                                when
                                    hour(closed_datetime) < 9
                                    then to_timestamp(cast(cast(closed_datetime as date) as STRING) || ' 9',
                                        'yyyy-MM-dd k')
                                when
                                    hour(closed_datetime) >= 18
                                    then to_timestamp(cast(cal2.next_working_day as STRING) || ' 9',
                                        'yyyy-MM-dd k')
                                else closed_datetime
                            end as closed_datetime_bizhrs
                        from invoice_disputes_base
                        left join calendar_working_days
                            on date(invoice_disputes_base.created_datetime) = calendar_working_days.date
                            and invoice_disputes_base.system_id = calendar_working_days.country
                        left join calendar_working_days cal2
                            on date(invoice_disputes_base.closed_datetime) = cal2.date
                            and invoice_disputes_base.system_id = cal2.country
                    ),
                    
                    working_day_rank as (
                        select 
                            base.*
                            , wcal1.working_day_rank as creation_working_day_rank
                            , wcal2.working_day_rank as closed_working_day_rank
                        from base
                        left join calendar_working_days_filtered wcal1
                            on date(base.creation_datetime_bizhrs) = wcal1.date
                            and base.system_id = wcal1.country
                        left join calendar_working_days_filtered wcal2
                            on date(base.closed_datetime_bizhrs) = wcal2.date
                            and base.system_id = wcal2.country
                    ),
                    
                    working_minutes as (
                        select
                        
                            working_day_rank.*
                            , ((closed_working_day_rank - creation_working_day_rank) + 1) * 8 * 60 as no_working_minutes
                            , case
                                when hour(creation_datetime_bizhrs) >= 13
                                then ((unix_timestamp(creation_datetime_bizhrs) - unix_timestamp(to_timestamp(
                                    cast(date(creation_datetime_bizhrs) as STRING) || ' 9', 'yyyy-MM-dd k'))) / 60) - 60
                                when hour(creation_datetime_bizhrs) = 12
                                then 180
                                else ((unix_timestamp(creation_datetime_bizhrs) - unix_timestamp(to_timestamp(cast(date
                                    (creation_datetime_bizhrs) as STRING) || ' 9', 'yyyy-MM-dd k'))) / 60)
                            end as creation_mins_passed
                            , case 
                                when hour(closed_datetime_bizhrs) < 12
                                then (((unix_timestamp(to_timestamp(cast(date(closed_datetime_bizhrs) as string) ||
                                    ' 18', 'yyyy-MM-dd k')) - unix_timestamp(closed_datetime_bizhrs))) / 60) - 60
                                when hour(closed_datetime_bizhrs) = 12
                                then 300
                                else (((unix_timestamp(to_timestamp(cast(date(closed_datetime_bizhrs) as string) ||
                                ' 18', 'yyyy-MM-dd k')) - unix_timestamp(closed_datetime_bizhrs))) / 60)
                            end as closed_mins_before
                        from working_day_rank
                    )
                    
                    select
                        working_minutes.id
                        , working_minutes.external_ref
                        , working_minutes.shipper_id
                        , working_minutes.shipper_name
                        , working_minutes.dispute_filed_date
                        , working_minutes.dispute_filed_week
                        , working_minutes.dispute_filed_month
                        , working_minutes.status
                        , working_minutes.number_of_tids
                        , working_minutes.created_datetime
                        , working_minutes.closed_datetime
                        , working_minutes.is_closed
                        , working_minutes.total_protected_revenue
                        , working_minutes.is_resolved
                        , working_minutes.count_unresolved
                        , working_minutes.revised_delivery_fee_weight
                        , working_minutes.revised_cod_fee_weight
                        , working_minutes.revised_rts_fee_weight
                        , working_minutes.original_delivery_fee_weight
                        , working_minutes.original_cod_fee_weight
                        , working_minutes.original_rts_fee_weight
                        , working_minutes.revised_delivery_fee_non_weight
                        , working_minutes.revised_cod_fee_non_weight
                        , working_minutes.revised_rts_fee_non_weight
                        , working_minutes.original_delivery_fee_non_weight
                        , working_minutes.original_cod_fee_non_weight
                        , working_minutes.original_rts_fee_non_weight
                        , (no_working_minutes - creation_mins_passed - closed_mins_before) / 60 
                            as lead_time_by_working_hours
                    from working_minutes
                    where shipper_id not in (78482, 7559750, 72128)
                """,
            ),
            base.TransformView(
                view_name="salesforce_base",
                jinja_template="""
                    with
                        salesforce_case_union as (
                            select
                                id
                                , case_number
                                , shipper_id
                                , cs_issue_type
                                , fsr_issue_type
                                , creation_datetime
                                , ops_response_datetime
                                , null as response_time_by_working_hours
                                , null as lead_time_by_working_hours
                                , closed_datetime
                                , is_closed
                                , date(date_trunc('week', creation_datetime)) as created_week
                                , system_id
                            from salesforce_ss_case_enriched
                            union all
                            select 
                                id
                                , case_number
                                , shipper_id
                                , cs_issue_type
                                , fsr_issue_type
                                , creation_datetime
                                , ops_response_datetime
                                , null as response_time_by_working_hours
                                , null as lead_time_by_working_hours
                                , closed_datetime
                                , is_closed
                                , date(date_trunc('week', creation_datetime)) as created_week
                                , system_id
                            from salesforce_cs_case_enriched
                            union all
                            select
                                id
                                , case_number
                                , shipper_id
                                , cs_issue_type
                                , fsr_issue_type
                                , creation_datetime
                                , ops_response_datetime
                                , response_time_by_working_hours
                                , lead_time_by_working_hours
                                , closed_datetime
                                , is_closed
                                , date(date_trunc('week', creation_datetime)) as created_week
                                , system_id
                            from salesforce_case_enriched
                        ),

                        sla_calculation as (
                            select
                                id
                                , case_number
                                , shipper_id
                                , cs_issue_type
                                , fsr_issue_type
                                , creation_datetime
                                , ops_response_datetime
                                , closed_datetime
                                , response_time_by_working_hours
                                , lead_time_by_working_hours
                                , is_closed
                                , date(date_trunc('week', creation_datetime)) as created_week
                                , 7 as temp_desired_sla_days
                                , case
                                    when fsr_issue_type = 'Breadth - Escalatable'
                                    and ops_response_datetime is not null
                                    and response_time_by_working_hours <= (7 * 8 * 0.7)
                                    then 1
                                else 0 end as temp_response_sla_met
                                , case
                                    when fsr_issue_type = 'Breadth - Escalatable'
                                    and lead_time_by_working_hours <= 7 * 8
                                    and is_closed = 1
                                    then 1
                                else 0 end as temp_sla_met
                                , system_id
                            from salesforce_case_union
                            where cs_issue_type = 'Finance: Invoice Disputes'
                        ),

                        base as (
                            select
                                *
                                , case
                                    when fsr_issue_type = 'Breadth - Escalatable'
                                        and (response_time_by_working_hours / 8) > (temp_desired_sla_days * 0.7)
                                        then 1
                                    when fsr_issue_type = 'Breadth - Escalatable'
                                        and temp_response_sla_met = 1
                                        then 1
                                    else 0 end as temp_response_sla_measured
                                , case
                                    when
                                        fsr_issue_type = 'Breadth - Escalatable'
                                        and closed_datetime is not null
                                        and from_utc_timestamp('{{ measurement_datetime }}', {{ get_local_timezone }})
                                            > date(closed_datetime)
                                        then 1
                                    when
                                        fsr_issue_type = 'Breadth - Escalatable'
                                        and closed_datetime is null
                                        and (lead_time_by_working_hours / 8) > temp_desired_sla_days
                                        then 1
                                    when
                                        temp_sla_met = 1
                                        then 1
                                    else 0 end as temp_sla_measured
                            from sla_calculation
                        )

                        select
                            *
                        from base
                        where shipper_id not in (78482, 7559750, 72128)
                    """,
                jinja_arguments={"get_local_timezone": util.get_local_timezone("lower(system_id)")},
            ),
            base.TransformView(
                view_name="combined",
                jinja_template="""
                select 
                    salesforce_base.id as sf_case_id
                    , salesforce_base.case_number as sf_case_number
                    , salesforce_base.shipper_id
                    , salesforce_base.cs_issue_type as sf_cs_issue_type
                    , salesforce_base.fsr_issue_type as sf_fsr_issue_type
                    , salesforce_base.creation_datetime as sf_creation_datetime
                    , salesforce_base.ops_response_datetime as sf_ops_response_datetime
                    , salesforce_base.closed_datetime as sf_closed_datetime
                    , salesforce_base.response_time_by_working_hours as sf_response_time_by_working_hours
                    , salesforce_base.lead_time_by_working_hours as sf_lead_time_by_working_hours
                    , salesforce_base.is_closed as sf_is_case_closed
                    , salesforce_base.created_week as sf_created_week
                    , salesforce_base.temp_desired_sla_days as sf_temp_desired_sla_days
                    , salesforce_base.temp_response_sla_met as sf_temp_response_sla_met
                    , salesforce_base.temp_sla_met as sf_temp_sla_met
                    , salesforce_base.temp_response_sla_measured as sf_temp_response_sla_measured
                    , salesforce_base.temp_sla_measured as sf_temp_sla_measured
                    , invoice_disputes_final.id as fin_id
                    , invoice_disputes_final.external_ref as fin_external_ref
                    , invoice_disputes_final.dispute_filed_date as fin_dispute_filed_date
                    , invoice_disputes_final.dispute_filed_week as fin_dispute_filed_week
                    , invoice_disputes_final.dispute_filed_month as fin_dispute_filed_month
                    , invoice_disputes_final.status as fin_status
                    , invoice_disputes_final.number_of_tids as fin_number_of_tids
                    , invoice_disputes_final.created_datetime as fin_created_datetime
                    , invoice_disputes_final.closed_datetime as fin_closed_datetime
                    , invoice_disputes_final.is_closed as fin_is_closed
                    , invoice_disputes_final.total_protected_revenue as fin_total_protected_revenue
                    , invoice_disputes_final.is_resolved as fin_is_resolved
                    , invoice_disputes_final.count_unresolved as fin_count_unresolved
                    , invoice_disputes_final.revised_delivery_fee_weight as fin_revised_delivery_fee_weight
                    , invoice_disputes_final.revised_cod_fee_weight as fin_revised_cod_fee_weight
                    , invoice_disputes_final.revised_rts_fee_weight as fin_revised_rts_fee_weight
                    , invoice_disputes_final.original_delivery_fee_weight as fin_original_delivery_fee_weight
                    , invoice_disputes_final.original_cod_fee_weight as fin_original_cod_fee_weight
                    , invoice_disputes_final.original_rts_fee_weight as fin_original_rts_fee_weight
                    , invoice_disputes_final.revised_delivery_fee_non_weight as fin_revised_delivery_fee_non_weight
                    , invoice_disputes_final.revised_cod_fee_non_weight as fin_revised_cod_fee_non_weight
                    , invoice_disputes_final.revised_rts_fee_non_weight as fin_revised_rts_fee_non_weight
                    , invoice_disputes_final.original_delivery_fee_non_weight as fin_original_delivery_fee_non_weight
                    , invoice_disputes_final.original_cod_fee_non_weight as fin_original_cod_fee_non_weight
                    , invoice_disputes_final.original_rts_fee_non_weight as fin_original_rts_fee_non_weight
                    , invoice_disputes_final.lead_time_by_working_hours as fin_lead_time_by_working_hours
                    , salesforce_base.system_id
                from salesforce_base
                left join invoice_disputes_final
                    on salesforce_base.id = invoice_disputes_final.external_ref
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).INVOICE_DISPUTE_CASE_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=(),
        enable_csv=True,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df

if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("mapreduce.fileoutputcommitter.marksuccessfuljobs", "false")
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()