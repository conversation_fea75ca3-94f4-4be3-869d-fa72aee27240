from dataclasses import dataclass
from datetime import timedelta
from typing import Optional, <PERSON><PERSON>


@dataclass(frozen=True)
class DependsOnExternal:
    dag_id: str
    task_id: str
    execution_delta: Optional[timedelta] = None


@dataclass(frozen=True)
class AirflowConfig:
    task_id: str
    task_name: str
    input_path: str
    sql: str
    gcs_check_prefix: str
    alert_channel: str
    output_path: str
    depends_on_external: Tuple[DependsOnExternal, ...] = ()
    conf: Optional[dict] = None
