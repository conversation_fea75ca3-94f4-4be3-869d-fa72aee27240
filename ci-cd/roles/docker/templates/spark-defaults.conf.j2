spark.submit.deployMode                                         cluster
spark.kubernetes.container.image                                {{ docker_registry }}/nv-data-{{ branch | lower }}-spark-dags:{{ versions.spark }}{{ tag }}
spark.kubernetes.container.image.pullPolicy                     IfNotPresent
spark.kubernetes.container.image.pullSecrets                    docker
spark.kubernetes.namespace                                      {{ env }}
spark.kubernetes.authenticate.driver.serviceAccountName         {{ env }}-sparkoperator-spark
spark.kubernetes.driver.secrets.storage-service-account-key     /keys
spark.kubernetes.executor.secrets.storage-service-account-key   /keys
spark.kubernetes.driver.podTemplateFile                         /opt/airflow/files/templates/data-lake-driver.yml
spark.kubernetes.executor.podTemplateFile                       /opt/airflow/files/templates/data-lake-executor.yml
spark.hadoop.google.cloud.auth.service.account.enable           true
spark.hadoop.google.cloud.auth.service.account.json.keyfile     /keys/key.json
spark.hadoop.hive.metastore.uris                                thrift://data-{{ env }}-global-hive-metastore-service:9083
spark.sql.files.maxPartitionBytes                               128MB
spark.sql.adaptive.coalescePartitions.enabled                   true
spark.sql.adaptive.advisoryPartitionSizeInBytes                 64MB
spark.sql.adaptive.skewJoin.enabled                             true