import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.MiddleMileDAG.Task.MOVEMENT_TRIPS_ENRICHED_MASKED,
    system_ids=(constants.SystemID.GL,),
    depends_on=(data_warehouse.MiddleMileDAG.Task.MIDDLE_MILE_TRIP_RELATIONSHIPS_MASKED,),
    depends_on_external=(
        base.DependsOnExternal(
            dag_id=data_warehouse.FleetDAG.DAG_ID, 
            task_id=data_warehouse.FleetDAG.Task.DRIVERS_ENRICHED_MASKED
        ),
        base.DependsOnExternal(
            dag_id=data_warehouse.HubsDAG.DAG_ID,
            task_id=data_warehouse.HubsDAG.Task.HUBS_ENRICHED_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.MonitoringDAG.DAG_ID,
            task_id=data_warehouse.MonitoringDAG.Task.HUBS_COORDINATES_CHANGE_EVENTS_MASKED),
        base.DependsOnExternal(
            dag_id=data_warehouse.OrdersDAG.DAG_ID,
            task_id=data_warehouse.OrdersDAG.Task.ORDER_DIMENSIONS_MASKED),
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="data_warehouse"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 6, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    latest_partition = f"/measurement_datetime=latest"
    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_ENRICHED, view_name="hubs_enriched"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).DRIVERS_ENRICHED,
                view_name="drivers_enriched"
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SHIPMENTS_ENRICHED,
                view_name="shipments_enriched",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).MIDDLE_MILE_TRIP_RELATIONSHIPS,
                view_name="middle_mile_trip_relationships",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).HUBS_COORDINATES_CHANGE_EVENTS,
                view_name="hubs_coordinates_change_events",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(env).ORDER_DIMENSIONS,
                view_name="order_dimensions",
                input_range=lookback_ranges.input,
            ),
        ),
        parquet_tables=(
            base.InputTable(
                path=parquet_tables_masked.DataWarehouse(input_env).HUB_DRIVING_DISTANCES + latest_partition,
                view_name="hub_driving_distances",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).MM_VEHICLE_TYPES,
                view_name="mm_vehicle_types",
            ),
            base.InputTable(
                path=parquet_tables_masked.GSheets(input_env).ID_AIRPORT_RA_LATLONG,
                view_name="id_airport_ra_latlong",
            ),
        ),
        delta_tables=(
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).HUB_RELATIONS,
                view_name="hub_relations",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).HUB_RELATION_SCHEDULES,
                view_name="hub_relation_schedules",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_TRIP_DRIVERS,
                view_name="movement_trip_drivers",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_TRIPS,
                view_name="movement_trips",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).MOVEMENT_TRIP_EVENTS,
                view_name="movement_trip_events",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).SHIPMENT_ORDERS,
                view_name="shipment_orders",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_CANCELLATION_REASONS,
                view_name="trip_cancellation_reasons",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_SAFETY_CHECKS,
                view_name="trip_safety_checks",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_SHIPMENT_SCANS,
                view_name="trip_shipment_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRIP_UNSCANNED_SHIPMENTS,
                view_name="trip_unscanned_shipments",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRUCK_TYPES,
                view_name="hub_prod_truck_types",
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).TRUCK_UTILIZATIONS,
                view_name="truck_utilizations",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.HubProdGL(input_env, is_masked).FLIGHT_INFO,
                view_name="flight_info",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.MovementTripProdGL(input_env, is_masked).TRIPS_VEHICLE,
                view_name="trips_vehicle",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.MovementTripProdGL(input_env, is_masked).VEHICLES,
                view_name="vehicles",
            ),
            base.InputTable(
                path=delta_tables.MovementTripProdGL(input_env, is_masked).TRUCK_TYPES,
                view_name="movement_trip_prod_truck_types",
            ),
        ),
        version_datetime=measurement_datetime,
    )

    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="movement_trips_base",
                jinja_template="""
                -- Calculate this first as movement classification (eg Hub - Airport) is required for ID Airport RA latlong logic
                with trips_w_time_diff as (
                    select
                        *
                        , cast(
                            (
                                unix_timestamp(movement_trips.actual_start_time)
                                - unix_timestamp(movement_trips.expected_start_time)
                            ) / 60 as int
                        ) as start_time_difference_min
                        , cast(
                            (
                                unix_timestamp(movement_trips.actual_end_time)
                                - unix_timestamp(movement_trips.expected_end_time)
                            ) / 60 as int
                        ) as end_time_difference_min
                        , case
                            when expected_duration_min >= 0 and expected_duration_min <= 240 then 30
                            when expected_duration_min > 240 and expected_duration_min <= 480 then 60
                            when expected_duration_min > 480 and expected_duration_min <= 960 then 120
                            when expected_duration_min > 960 and expected_duration_min <= 1440 then 180
                            when expected_duration_min > 1440 then 240
                            end as tdoa_threshold
                    from movement_trips
                )

                    select
                        base.id as trip_id
                        , base.schedule_id
                        , lower(hub_relations.origin_hub_system_id) as origin_country
                        , hub_relations.origin_hub_id
                        , origin_hub.name as origin_hub_name
                        , origin_hub.region as origin_hub_region
                        , lower(hub_relations.destination_hub_system_id) as dest_country
                        , hub_relations.destination_hub_id as dest_hub_id
                        , dest_hub.name as dest_hub_name
                        , dest_hub.region as dest_hub_region
                        , if(origin_hub.region = dest_hub.region, 'Intra', 'Inter') as region_movement_type
                        , case
                            when origin_hub.facility_type = 'CROSSDOCK'
                                and dest_hub.facility_type = 'CROSSDOCK' then 'primary'
                            when origin_hub.facility_type = 'AIRPORT' then 'primary'
                            when dest_hub.facility_type = 'AIRPORT' then 'primary'
                            else 'secondary'
                            end as linehaul_purpose_type
                        , concat_ws(
                            ' - ', origin_hub.facility_type, dest_hub.facility_type
                        ) as movement_classification
                        , base.movement_type
                        , base.status
                        , base.trip_cancellation_reason_id
                        , from_utc_timestamp(base.created_at, {{ get_local_timezone }}) as created_datetime
                        , from_utc_timestamp(
                            base.expected_start_time, {{ get_local_timezone }}
                        ) as expected_start_datetime
                        , from_utc_timestamp(
                            base.actual_start_time, {{ get_local_timezone }}
                        ) as actual_start_datetime
                        , base.start_time_difference_min
                        , cast(abs(base.start_time_difference_min) <= 15 as int) as on_time_start_flag
                        , from_utc_timestamp(
                            base.expected_end_time, {{ get_local_timezone }}
                        ) as expected_arrival_datetime
                        , from_utc_timestamp(
                            base.actual_end_time, {{ get_local_timezone }}
                        ) as actual_arrival_datetime
                        , case 
                            when abs(base.end_time_difference_min) <= base.tdoa_threshold then 1
                            when abs(base.end_time_difference_min) > base.tdoa_threshold then 0
                            else null
                            end as on_time_end_flag
                        , base.end_time_difference_min
                        , base.expected_duration_min
                        , hub_relations.id as hub_relations_id
                        , if(base.deleted_at is not null, 1, 0) as deleted_flag
                        , lower(hub_relations.origin_hub_system_id) as system_id
                        , date_format(
                            from_utc_timestamp(base.created_at, {{ get_local_timezone }}), 'yyyy-MM'
                        ) as created_month
                    from trips_w_time_diff as base
                    left join hub_relations
                        on base.hub_relation_id = hub_relations.id
                    left join hubs_enriched as origin_hub
                        on hub_relations.origin_hub_id = origin_hub.id
                        and lower(hub_relations.origin_hub_system_id) = origin_hub.system_id
                    left join hubs_enriched as dest_hub
                        on hub_relations.destination_hub_id = dest_hub.id
                        and lower(hub_relations.destination_hub_system_id) = dest_hub.system_id
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("hub_relations.origin_hub_system_id"),
                },
            ),

            base.TransformView(
                view_name="trip_drivers_clean",
                jinja_template="""
                select
                    trip_id
                    , max_by(driver_id, id) filter (where is_primary = 1) as primary_driver_id
                    , max_by(driver_id, id) filter (where is_primary = 0) as secondary_driver_id
                from movement_trip_drivers
                where
                    deleted_at is null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="latest_safety_check",
                jinja_template="""
                select
                    trip_id
                    , max_by(vehicle_number, id) as vehicle_number
                    , max_by(truck_type_id, id) as truck_type_id
                from trip_safety_checks
                where
                    deleted_at is null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="trips_entering",
                jinja_template="""
                with
                    base as (

                        select distinct
                            trip_shipment_scans.trip_id
                            , trip_shipment_scans.shipment_id
                            , shipment_orders.order_id
                        from trip_shipment_scans
                        left join shipment_orders
                            on trip_shipment_scans.shipment_id = shipment_orders.shipment_id
                        where
                            trip_shipment_scans.deleted_at is null
                            and trip_shipment_scans.scan_type = 'SHIPMENT_VAN_INBOUND'

                    )
                    , final as (

                        select
                            trip_id
                            , count(distinct shipment_id) as shipments_entering
                            , count(distinct order_id) as orders_entering
                        from base
                        group by 1

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="trips_exiting",
                jinja_template="""
                with
                    base as (

                        select distinct
                            trip_shipment_scans.trip_id
                            , trip_shipment_scans.shipment_id
                            , shipment_orders.order_id
                        from trip_shipment_scans
                        left join shipment_orders
                            on trip_shipment_scans.shipment_id = shipment_orders.shipment_id
                        where
                            trip_shipment_scans.deleted_at is null
                            and trip_shipment_scans.scan_type = 'SHIPMENT_HUB_INBOUND'

                    )
                    , final as (

                        select
                            trip_id
                            , count(distinct shipment_id) as shipments_exiting
                            , count(distinct order_id) as orders_exiting
                        from base
                        group by 1

                    )

                select
                    *
                from final
                """,
            ),
            base.TransformView(
                view_name="trips_aggregate",
                jinja_template="""
                with trip_orders as (

                    select distinct
                        system_id
                        , trip_id
                        , order_id
                    from middle_mile_trip_relationships
                    where order_id is not null

                ),
                trip_sum as (
                    select
                        trip_orders.trip_id
                        , sum(order_dimensions.estimated_weight) as total_order_weight
                        , sum(order_dimensions.estimated_volume) as total_order_volume
                    from trip_orders
                    left join order_dimensions
                        on trip_orders.system_id = order_dimensions.system_id
                        and trip_orders.order_id = order_dimensions.order_id
                    group by 1

                ),
                trip_shipments_orders as (

                    select
                        trip_id
                        , count(distinct order_id) as total_orders
                        , count(distinct shipment_id) as total_shipments
                    from middle_mile_trip_relationships
                    group by 1

                ),
                final as (

                    select
                        trip_shipments_orders.trip_id
                        , trip_shipments_orders.total_orders
                        , trip_shipments_orders.total_shipments
                        , trip_sum.total_order_weight
                        , trip_sum.total_order_volume
                    from trip_shipments_orders
                    left join trip_sum
                        on trip_shipments_orders.trip_id = trip_sum.trip_id

                )

                select * from final

                """,
            ),

            base.TransformView(
                view_name="movement_trip_events_base",
                jinja_template="""
                select
                    id
                    , trip_id
                    , get_json_object(movement_trip_events.ext_data, '$.latitude') as driver_latitude
                    , get_json_object(movement_trip_events.ext_data, '$.longitude') as driver_longitude
                    , from_utc_timestamp(created_at, {{ get_local_timezone }}) as event_datetime
                    , event
                    , hub_id
                    , status
                    , lower(hub_system_id) as system_id
                from movement_trip_events
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("hub_system_id"),
                },
            ),

            ## CTE to calculate the distance between driver triggering ARRIVED/DEPARTED event and hub latlongs
            base.TransformView(
                view_name="driver_event_distance_cte",
                jinja_template="""
                with events_cte as (
                    select
                        movement_trip_events_base.trip_id
                        , movement_trip_events_base.event
                        , max_by(movement_trip_events_base.driver_latitude, movement_trip_events_base.id) as driver_latitude
                        , max_by(movement_trip_events_base.driver_longitude, movement_trip_events_base.id) as driver_longitude
                        , max_by(hubs_coordinates_change_events.latitude, movement_trip_events_base.id) as hub_latitude
                        , max_by(hubs_coordinates_change_events.longitude, movement_trip_events_base.id) as hub_longitude
                    from movement_trip_events_base
                    left join hubs_coordinates_change_events
                        on movement_trip_events_base.hub_id = hubs_coordinates_change_events.hub_id
                        and movement_trip_events_base.system_id = hubs_coordinates_change_events.system_id
                        and date(movement_trip_events_base.event_datetime) >= hubs_coordinates_change_events.start_date
                        and date(movement_trip_events_base.event_datetime) <= hubs_coordinates_change_events.end_date
                    group by 1,2
                ),

                -- For HUB - AIRPORT movements in ID only, use latlong store in id_airport_ra_latlong gsheet.
                id_airport_ra_latlong_cte as (
                    select
                        events_cte.trip_id
                        , events_cte.driver_latitude
                        , events_cte.driver_longitude
                        , events_cte.event
                        , coalesce(id_airport_ra_latlong.latitude, events_cte.hub_latitude) as hub_latitude
                        , coalesce(id_airport_ra_latlong.longitude, events_cte.hub_longitude) as hub_longitude
                    from movement_trips_base
                    left join events_cte
                        on movement_trips_base.trip_id = events_cte.trip_id
                    left join id_airport_ra_latlong
                        on movement_trips_base.dest_hub_id = id_airport_ra_latlong.hub_id
                        and date(coalesce(movement_trips_base.actual_arrival_datetime, movement_trips_base.expected_arrival_datetime)) >= id_airport_ra_latlong.start_date
                        and date(coalesce(movement_trips_base.actual_arrival_datetime, movement_trips_base.expected_arrival_datetime)) <= id_airport_ra_latlong.end_date
                        and movement_trips_base.system_id = 'id'
                        and movement_trips_base.movement_classification like '%- AIRPORT'
                        and events_cte.event = 'ARRIVED'
                ),

                distance_cte as (

                    select
                        trip_id
                        , event
                        , ( 6371*ACOS(COS(radians(driver_latitude))*COS(radians(hub_latitude))*COS(radians(hub_longitude)
                            -radians(driver_longitude))+SIN(radians(driver_latitude))*SIN(radians(hub_latitude)))
                        ) as haversine_distance
                    from id_airport_ra_latlong_cte
                )

                select *
                from distance_cte
                """,
            ),

            ## CTE to calculate the distance between origin and dest hub latlongs
            base.TransformView(
                view_name="hub_distances_cte",
                jinja_template="""
                with hubs_latlong_cte as (
                    select
                        movement_trips_base.trip_id
                        , origin_hub_latlong.latitude as origin_hub_latitude
                        , origin_hub_latlong.longitude as origin_hub_longitude
                        , coalesce(id_airport_ra_latlong.latitude, dest_hub_latlong.latitude) as dest_hub_latitude
                        , coalesce(id_airport_ra_latlong.longitude, dest_hub_latlong.longitude) as dest_hub_longitude
                    from movement_trips_base
                    left join hubs_coordinates_change_events as origin_hub_latlong
                        on movement_trips_base.origin_hub_id = origin_hub_latlong.hub_id
                        and date(coalesce(movement_trips_base.actual_start_datetime, movement_trips_base.expected_start_datetime)) >= origin_hub_latlong.start_date
                        and date(coalesce(movement_trips_base.actual_start_datetime, movement_trips_base.expected_start_datetime)) <= origin_hub_latlong.end_date
                        and movement_trips_base.system_id = origin_hub_latlong.system_id
                    left join hubs_coordinates_change_events as dest_hub_latlong
                        on movement_trips_base.dest_hub_id = dest_hub_latlong.hub_id
                        and date(coalesce(movement_trips_base.actual_arrival_datetime, movement_trips_base.expected_arrival_datetime)) >= dest_hub_latlong.start_date
                        and date(coalesce(movement_trips_base.actual_arrival_datetime, movement_trips_base.expected_arrival_datetime)) <= dest_hub_latlong.end_date
                        and movement_trips_base.system_id = dest_hub_latlong.system_id
                -- For HUB - AIRPORT movements in ID only, use latlong store in id_airport_ra_latlong gsheet.
                    left join id_airport_ra_latlong
                        on movement_trips_base.dest_hub_id = id_airport_ra_latlong.hub_id
                        and date(coalesce(movement_trips_base.actual_arrival_datetime, movement_trips_base.expected_arrival_datetime)) >= id_airport_ra_latlong.start_date
                        and date(coalesce(movement_trips_base.actual_arrival_datetime, movement_trips_base.expected_arrival_datetime)) <= id_airport_ra_latlong.end_date
                        and movement_trips_base.system_id = 'id'
                        and movement_trips_base.movement_classification like '%- AIRPORT'
                ),

                hub_distance as (

                    select
                        trip_id
                        , 6371000 * acos(cos(radians(origin_hub_latitude)) * cos(radians(dest_hub_latitude)) * cos(radians(dest_hub_longitude) 
                            - radians(origin_hub_longitude)) 
                            + sin(radians(origin_hub_latitude)) * sin(radians(dest_hub_latitude)))
                            as haversine_distance
                    from hubs_latlong_cte
                )

                select *
                from hub_distance
                """,
            ),

            base.TransformView(
                view_name="trip_completion",
                jinja_template="""
                    select
                        system_id
                        , trip_id
                        , min(event_datetime) as completion_datetime
                    from movement_trip_events_base
                    where status = 'COMPLETED'
                    group by 1,2
                """,
            ),
            base.TransformView(
                view_name="dedup_truck_utilization",
                jinja_template="""
                with final as (

                    select
                        trip_id
                        , max_by(utilization, created_at) as truck_utilization
                        , max_by(truck_type_id, created_at) as truck_type_id
                    from truck_utilizations
                    group by 1

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="comments",
                jinja_template="""
                with final as (

                    select
                        hub_relation_id
                        , min_by(comment, created_at) as comments
                    from hub_relation_schedules
                    group by 1

                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="vehicle_assignment_feature_cte",
                jinja_template="""
                with latest_vehicle_trip_mapping as (
                    select
                        trip_id
                        , min_by(has_confirmed, created_at) as has_confirmed
                        , min_by(vehicle_id, created_at) as vehicle_id
                    from trips_vehicle
                    group by 1
                ),

                final as (
                    select
                        latest_vehicle_trip_mapping.*
                        , vehicles.one_time_use
                        , vehicles.trimmed_vehicle_number
                        , vehicles.truck_type_id
                    from latest_vehicle_trip_mapping
                    left join vehicles
                        on latest_vehicle_trip_mapping.vehicle_id =  vehicles.id
                )

                select * from final
                """,
            ),
            base.TransformView(
                view_name="dedup_truck_types",
                jinja_template="""
                with final as (
                    select
                        id
                        , system_id
                        , max_by(name, coalesce(updated_at,created_at)) as name
                    from movement_trip_prod_truck_types
                    group by 1,2
                )

                select * from final

                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with pre_final as (

                    select
                        movement_trips_base.trip_id
                        , movement_trips_base.schedule_id
                        , movement_trips_base.origin_country
                        , movement_trips_base.origin_hub_id
                        , trip_cancellation_reasons.cancellation_reason
                        , departed_distance.haversine_distance as departure_distance_from_hub
                        , arrived_distance.haversine_distance as arrival_distance_from_hub
                        , case
                            when departed_distance.haversine_distance <= 1 then 1
                            when departed_distance.haversine_distance > 1 then 0
                            else null
                            end as valid_departure_flag
                        , case
                            when arrived_distance.haversine_distance <= 1 then 1
                            when arrived_distance.haversine_distance > 1 then 0
                            else null
                            end as valid_arrival_flag
                        , movement_trips_base.origin_hub_name
                        , movement_trips_base.origin_hub_region
                        , movement_trips_base.dest_country
                        , movement_trips_base.dest_hub_id
                        , movement_trips_base.dest_hub_name
                        , movement_trips_base.dest_hub_region
                        , movement_trips_base.region_movement_type
                        , movement_trips_base.linehaul_purpose_type
                        , movement_trips_base.movement_classification
                        , movement_trips_base.movement_type
                        , flight_info.flight_no
                        , movement_trips_base.status
                        , movement_trips_base.created_datetime
                        , movement_trips_base.expected_start_datetime
                        , movement_trips_base.actual_start_datetime
                        , movement_trips_base.start_time_difference_min
                        , movement_trips_base.on_time_start_flag
                        , movement_trips_base.expected_arrival_datetime
                        , movement_trips_base.actual_arrival_datetime
                        , movement_trips_base.on_time_end_flag
                        , movement_trips_base.end_time_difference_min
                        , trip_completion.completion_datetime
                        , movement_trips_base.expected_duration_min
                        , trip_drivers_clean.primary_driver_id
                        , primary_driver.first_name as primary_driver_name
                        , trip_drivers_clean.secondary_driver_id
                        , secondary_driver.first_name as secondary_driver_name
                        , coalesce(vehicle_assignment_feature_cte.trimmed_vehicle_number, latest_safety_check.vehicle_number) as vehicle_number
                        , coalesce(truck_types_v3.name, truck_types_v2.name, truck_types_v1.name) as truck_type
                        , mm_vehicle_types.actual_capacity_cbm
                        , mm_vehicle_types.expected_baseline_cbm
                        , dedup_truck_utilization.truck_utilization
                        , trips_aggregate.total_shipments
                        , trips_aggregate.total_orders
                        , trips_aggregate.total_order_weight
                        , trips_aggregate.total_order_volume
                        , trips_entering.shipments_entering
                        , trips_entering.orders_entering
                        , trips_exiting.shipments_exiting
                        , trips_exiting.orders_exiting
                        , comments.comments
                        , vehicle_assignment_feature_cte.has_confirmed
                        , vehicle_assignment_feature_cte.one_time_use
                        , movement_trips_base.deleted_flag
                        , movement_trips_base.system_id
                        , movement_trips_base.created_month
                        , hub_distances_cte.haversine_distance
                    from movement_trips_base
                    left join trip_completion
                        on movement_trips_base.trip_id = trip_completion.trip_id
                    left join trip_cancellation_reasons
                        on movement_trips_base.trip_cancellation_reason_id = trip_cancellation_reasons.id
                    left join latest_safety_check
                        on movement_trips_base.trip_id = latest_safety_check.trip_id
                    left join trip_drivers_clean
                        on movement_trips_base.trip_id = trip_drivers_clean.trip_id
                    left join drivers_enriched as primary_driver
                        on trip_drivers_clean.primary_driver_id = primary_driver.id
                    left join drivers_enriched as secondary_driver
                        on trip_drivers_clean.secondary_driver_id = secondary_driver.id
                    left join trips_aggregate
                        on movement_trips_base.trip_id = trips_aggregate.trip_id
                    left join trips_entering
                        on movement_trips_base.trip_id = trips_entering.trip_id
                    left join trips_exiting
                        on movement_trips_base.trip_id = trips_exiting.trip_id
                    left join dedup_truck_utilization
                        on movement_trips_base.trip_id = dedup_truck_utilization.trip_id
                    left join vehicle_assignment_feature_cte
                        on movement_trips_base.trip_id = vehicle_assignment_feature_cte.trip_id
                    left join hub_prod_truck_types as truck_types_v1
                        on lower(movement_trips_base.system_id) = truck_types_v1.system_id
                        and dedup_truck_utilization.truck_type_id = truck_types_v1.id
                    left join hub_prod_truck_types as truck_types_v2
                        on lower(movement_trips_base.system_id) = truck_types_v2.system_id
                        and latest_safety_check.truck_type_id = truck_types_v2.id
                    left join dedup_truck_types as truck_types_v3
                        on lower(movement_trips_base.system_id) = truck_types_v3.system_id
                        and vehicle_assignment_feature_cte.truck_type_id = truck_types_v3.id
                    left join driver_event_distance_cte as departed_distance
                        on movement_trips_base.trip_id = departed_distance.trip_id
                        and departed_distance.event = 'DEPARTED'
                    left join driver_event_distance_cte as arrived_distance
                        on movement_trips_base.trip_id = arrived_distance.trip_id
                        and arrived_distance.event = 'ARRIVED'
                    left join flight_info 
                        on movement_trips_base.trip_id = flight_info.trip_id
                    left join comments
                        on movement_trips_base.hub_relations_id = comments.hub_relation_id
                    left join mm_vehicle_types
                        on lower(movement_trips_base.system_id) = mm_vehicle_types.system_id
                        and coalesce(truck_types_v3.id, truck_types_v2.id, truck_types_v1.id) = mm_vehicle_types.id
                    left join hub_distances_cte
                        on movement_trips_base.trip_id = hub_distances_cte.trip_id
                )

                , final as (

                    select
                        pre_final.*
                        , total_order_volume / cast(expected_baseline_cbm as double) as calculated_utilization
                        , case 
                            when pre_final.system_id = 'id' and pre_final.linehaul_purpose_type = 'primary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(6000000 as double))
                            when pre_final.system_id = 'id' and pre_final.linehaul_purpose_type = 'secondary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(3000000 as double))
                            when pre_final.system_id = 'my' and pre_final.linehaul_purpose_type = 'primary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(1000000 as double))
                            when pre_final.system_id = 'my' and pre_final.linehaul_purpose_type = 'secondary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(500000 as double))
                            when pre_final.system_id = 'ph' and pre_final.linehaul_purpose_type = 'primary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(2500000 as double))
                            when pre_final.system_id = 'ph' and pre_final.linehaul_purpose_type = 'secondary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(1250000 as double))
                            when pre_final.system_id = 'sg' and pre_final.linehaul_purpose_type = 'primary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(100000 as double))
                            when pre_final.system_id = 'sg' and pre_final.linehaul_purpose_type = 'secondary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(50000 as double))
                            when pre_final.system_id = 'th' and pre_final.linehaul_purpose_type = 'primary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(2000000 as double))
                            when pre_final.system_id = 'th' and pre_final.linehaul_purpose_type = 'secondary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(1000000 as double))
                            when pre_final.system_id = 'vn' and pre_final.linehaul_purpose_type = 'primary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(2500000 as double))
                            when pre_final.system_id = 'vn' and pre_final.linehaul_purpose_type = 'secondary'
                                then least(coalesce(cast(hub_driving_distances.distance as double), pre_final.haversine_distance), cast(1250000 as double))
                            end as estimated_distance
                    from pre_final
                    left join hub_driving_distances
                        on pre_final.origin_hub_id = hub_driving_distances.origin_id
                        and pre_final.dest_hub_id = hub_driving_distances.dest_id
                        and pre_final.system_id = hub_driving_distances.system_id

                )

                select * from final

                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).MOVEMENT_TRIPS_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    run(spark, task_config)
    spark.stop()
