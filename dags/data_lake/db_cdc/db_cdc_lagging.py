from datetime import datetime, timedelta
from airflow.decorators import dag, task
from common.airflow import notifications as notif 
from common.utils import alerts
from config.config import Config 
from airflow.providers.postgres.hooks.postgres import PostgresHook
from typing import List, Dict
import logging

LAGGING_JOBS_QUERY = """
WITH latest_runs AS (
    SELECT 
        dag_id,
        MAX(execution_date) AS latest_execution_date
    FROM dag_run
    WHERE dag_id LIKE 'datalake_cdc_%'
        AND dag_id NOT LIKE 'datalake_cdc_k8s%'
        AND dag_id NOT LIKE 'datalake_cdc_raw_lagging_hours%'
        AND state = 'success'
    GROUP BY dag_id
)
SELECT 
    dag_id,
    latest_execution_date,
    NOW() AS current_time,
    EXTRACT(EPOCH FROM (NOW() - latest_execution_date)) / 3600 AS lag_hours
FROM latest_runs
WHERE EXTRACT(EPOCH FROM (NOW() - latest_execution_date)) / 3600 > 6;
"""

default_args = {
    'owner': 'airflow',
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    "on_failure_callback": (
        lambda context: (notif.chat.send_ti_failure_alert(context), notif.athena.send_notification_to_athena(context))
    )
    if Config.ENV in ("prod", "dev")
    else None,
}

@dag(
    dag_id='monitor_lagging_jobs',
    default_args=default_args,
    description='Monitor CDC jobs that are lagging by more than 6 hours',
    schedule_interval='@hourly',
    start_date=datetime(2024, 10, 27),
    catchup=False,
    tags=['monitoring', 'db_cdc']
)
def monitor_lagging_jobs():
    @task
    def check_lagging_jobs() -> List[Dict]:
        """
        Query Airflow metadata database to find lagging jobs
        Returns a list of dictionaries containing lagging job details
        """
        postgres_conn_id = 'airflow_metastore_db' if Config.ENV == 'prod' else 'airflow_metastore_db_dev'
        postgres_hook = PostgresHook(postgres_conn_id=postgres_conn_id)
        lagging_jobs = postgres_hook.get_records(LAGGING_JOBS_QUERY)

        if not lagging_jobs:
            logging.info("No lagging jobs found. No alert sent.")
            return []

        return [
            {
                'dag_id': job[0],
                'latest_execution': job[1].strftime('%Y-%m-%d %H:%M:%S'),
                'current_time': job[2].strftime('%Y-%m-%d %H:%M:%S'),
                'lag_hours': round(job[3], 2)
            }
            for job in lagging_jobs
        ]

    @task
    def send_alert_to_gchat(lagging_jobs: List[Dict]):
        """
        Send an alert to Google Chat if there are lagging jobs
        """
        if lagging_jobs:
            message = "\n".join(
                f"Job {job['dag_id']} is lagging by {job['lag_hours']} hours. Last execution was at {job['latest_execution']}."
                for job in lagging_jobs
            )
            alerts.raise_gchat_alert(message, notif.chat.get_connection_details('chat_testing_alert'))
        else:
            logging.info("No lagging jobs to alert.")

    lagging_jobs = check_lagging_jobs()
    send_alert_to_gchat(lagging_jobs)

monitor_lagging_jobs_dag = monitor_lagging_jobs()
