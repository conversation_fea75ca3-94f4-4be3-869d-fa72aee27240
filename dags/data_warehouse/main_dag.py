import json
import os
from datetime import datetime, timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.operators.python import ShortCircuitOperator
from airflow.providers.google.cloud.sensors.gcs import GCSObjectsWithPrefixExistenceSensor

from common.airflow import notifications as notif
from common.spark import spark_app_utils
from common.stringcase import kebab_case
from common.utils import helper
from common.utils.gcs import strip_uri
from custom_operators.spark_k8s import SparkK8s
from data_warehouse.tasks.main.config import dag_configs
from data_warehouse.utils import airflow
from metadata.constants import MASKED_DATA_WAREHOUSE_BASE_URI, SystemID, Timeout
from metadata.spark_conf import SPARK_CONF
from metadata.table_sizes import TABLE_SIZES
from airflow.models import Variable

DEFAULT_SCHEDULE = "0 18 * * *"  # Daily 2am SGT

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
spark_conf = SPARK_CONF[env]["dwh"]
size_to_tables = TABLE_SIZES["dwh"]
registry = os.environ["IMAGE_REGISTRY"]
image_name = os.environ["SPARK_IMAGE_NAME"]
tag = os.environ["SPARK_IMAGE_TAG"]
tables_to_size = {table: size for size, tables in size_to_tables.items() for table in tables}
table_sizes_airflow = {} # table_sizes_airflow = Variable.get("table_sizes", deserialize_json=True)["cdc"]


default_args = {
    "owner": "airflow",
    "start_date": datetime(2020, 2, 28),
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "sla": Timeout.EIGHT_HOURS if env == "prod" else None,
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

MASKED_IDENTIFIER = "_masked"


# Added it only for testing k8s operator
# def check_variable(var_name):
#     try:
#         # Fetch the variable value (return False if not set)
#         var_value = Variable.get(var_name, default_var=False)
#         if var_value:
#             return True
#         else:
#             return False
#     except KeyError:
#         return False


def _create_dag(dag_id, tasks_config, schedule_interval, max_active_runs, alert_channel):
    task_operators = {}
    ext_sensors = {}
    with DAG(
            catchup=False,
            dag_id=dag_id,
            default_args=default_args,
            max_active_runs=max_active_runs,
            schedule_interval=schedule_interval,
            sla_miss_callback=notif.chat.send_dag_run_sla_miss_alert,
            tags=["data_warehouse"],
    ) as dag:
        for config in tasks_config:
            task_name = config.task_name
            start = DummyOperator(task_id=f"{task_name}_start")
            end = DummyOperator(task_id=f"{task_name}_end")
            for system_id in config.system_ids:
                task_id = f"{task_name}_{system_id}"
                # input_args = {
                #     "env": env,
                #     "last_measurement_datetime_str": "{{ execution_date }}",
                #     "measurement_datetime_str": "{{ next_execution_date }}",
                #     "enable_full_run_str": "{{ params.enable_full_run }}",
                #     "system_id": system_id,
                # }
                input_args_k8s = {
                    "env": env,
                    "last_measurement_datetime_str": "{{ execution_date }}",
                    "measurement_datetime_str": "{{ next_execution_date }}",
                    "enable_full_run_str": False,
                    "system_id": system_id,
                }
                # task = SparkSubmitOperator(
                #     task_id=task_id,
                #     name=kebab_case(task_id),
                #     application=f"{tasks_path}/main/{config.py_file}",
                #     application_args=[json.dumps(input_args, separators=(",", ":"))],
                #     conn_id="spark_default",
                #     conf={
                #         **spark_conf[tables_to_size.get(task_id, "small")],
                #         "spark.sql.adaptive.enabled": 'true',
                #         "spark.kubernetes.driver.label.ninja-spark-dwh-etl": task_id,
                #         "spark.kubernetes.executor.label.ninja-spark-dwh-etl": task_id,
                #         "spark.kubernetes.driver.podTemplateFile": "/opt/airflow/files/templates/dwh-driver.yml",
                #         "spark.kubernetes.executor.podTemplateFile": "/opt/airflow/files/templates/dwh-executor.yml",
                #     },
                #     execution_timeout=config.execution_timeout,
                #     params={"enable_full_run": False, "alert_channel": alert_channel},
                # )
                app_config = helper.build_config(task_name, system_id, env, f"{registry}/{image_name}:{tag}", "dwh",
                                                 group="dwh")
                app_config["main_application_file"] = f"{tasks_path}/main/{config.py_file}"
                app_config["table_sizes_airflow"] = table_sizes_airflow
                app_config["name"] = f"{task_name}_{system_id}_job"
                app_config["spark_config"] = {**spark_conf[tables_to_size.get(task_id, "small")],
                                              "spark.sql.adaptive.enabled": 'true',
                                              "spark.kubernetes.driver.label.ninja-spark-dwh-etl": task_id,
                                              "spark.kubernetes.executor.label.ninja-spark-dwh-etl": task_id }
                application_file_delta = spark_app_utils.get_application_file_str(app_config, app_type='dwh')
                delta_task = SparkK8s(
                    task_id=f"{task_id}",
                    namespace=os.environ["AIRFLOW__KUBERNETES__NAMESPACE"],
                    application_file=application_file_delta,
                    kubernetes_conn_id="kubernetes_default",
                    in_cluster=True,
                    cluster_context="data-context",
                    reattach_on_restart=True,
                    get_logs=True,
                    log_events_on_failure=True,
                    delete_on_termination=True,
                    do_xcom_push=False,
                    arguments=[json.dumps(input_args_k8s, separators=(",", ":"))],
                    env_vars={"EXECUTION_DATE": "{{ execution_date }}"},
                    pool="k8s_spark_pool",
                    execution_timeout=config.execution_timeout,
                )
                # short_circuit_task = ShortCircuitOperator(
                #     task_id=f'{task_id}_short_circuit_task',
                #     python_callable=check_variable,
                #     op_kwargs={'var_name': task_id},
                #     provide_context=True
                # )
                # start >> task >> end
                start >> delta_task >> end
                # start >> short_circuit_task >> delta_task
            task_operators[task_name] = {"start": start, "end": end}

            # Create ExternalTaskSensor for external dependencies
            for ext_dep in config.depends_on_external:
                ext_dag_task_id = f"{ext_dep.dag_id}_{ext_dep.task_id}"
                if ext_dag_task_id not in ext_sensors:
                    ext_sensors[ext_dag_task_id] = airflow.get_dwh_external_task_sensor(
                        ext_dep.dag_id, ext_dep.task_id, dag_id, ext_dep.execution_delta
                    )
                ext_sensors[ext_dag_task_id] >> start

            # Create tasks to refresh Hive Metastore
            hms_config = config.hive_metastore_config
            if hms_config:
                update_hms_tasks = {}
                update_sing_hms_tasks = {}
                for conf in hms_config:
                    update_hms_tasks[conf.hive_schema] = airflow.get_update_hms_task(env, tasks_path, task_name, conf)
                    update_sing_hms_tasks[conf.hive_schema] = airflow.get_update_sing_hms_task(env, tasks_path,
                                                                                               task_name, conf)
                    end >> update_hms_tasks[conf.hive_schema] >> update_sing_hms_tasks[conf.hive_schema]

            # Check if task is successfully written in GCS
            if config.post_execution_check:
                gcs_sensors = {}
                for system_id in config.system_ids:
                    if task_name.endswith(MASKED_IDENTIFIER):
                        task = "_".join(task_name.split("_")[:-1])
                        prefix_path = (
                                task + "/measurement_datetime={{ next_execution_date.strftime('%Y-%m-%d %H-%M-%S') }}"
                        )
                        if system_id != SystemID.GL:
                            prefix_path += f"/system_id={system_id}"
                        gcs_sensors[system_id] = GCSObjectsWithPrefixExistenceSensor(
                            task_id=f"{task_name}_{system_id}_check_gcs_existence",
                            bucket=strip_uri(MASKED_DATA_WAREHOUSE_BASE_URI.format(env)),
                            prefix=prefix_path,
                            google_cloud_conn_id="google_cloud_default",
                            execution_timeout=Timeout.ONE_MINUTE,
                            retries=0,
                        )
                        if hms_config:
                            end >> gcs_sensors[system_id] >> update_hms_tasks[conf.hive_schema]
                        else:
                            end >> gcs_sensors[system_id]

        # Define internal dependencies after creating all tasks as tasks are not created in topological order
        for config in tasks_config:
            for dep in config.depends_on:
                task_operators[dep]["end"] >> task_operators[config.task_name]["start"]
    globals()[dag_id] = dag
    return dag


for dag_id, dag_config in dag_configs.items():
    _create_dag(
        dag_id,
        dag_config["task_configs"],
        dag_config.get("schedule", DEFAULT_SCHEDULE),
        dag_config.get("max_active_runs", 1),
        dag_config.get("alert_channel", "google_chat"),
    )