import codecs
import json
import sys

from pyspark.sql import SparkSession
from pyspark.sql.functions import col, date_format

from common import db
from common.spark.util import cast_columns


def create_db_snapshot(spark, env, schema, table, primary_keys, conn_dict, start_time=None, end_time=None):
    """
    Creates a DB table snapshot in the GCS data lake with Spark.

    :param spark:           Spark session
    :param env:             Data lake environment
    :param schema:          DB schema
    :param table:           DB table
    :param primary_keys:    Primary key(s) in {'column_name': 'data_type'} format
    :param conn_dict:       DB connection details
    :param start_time:      Start time for the snapshot (optional)
    :param end_time:        End time for the snapshot (optional)
    """
    url, conn_props = db.get_spark_jdbc_config(**conn_dict, schema=schema)

    snapshot_column = "created_at"
    has_int_key = False
    parallelism = 1000

    if len(primary_keys) == 1:
        column_name, data_type = [*primary_keys.items()][0]
        if data_type in ("int", "bigint"):
            snapshot_column = column_name
            has_int_key = True

    partition_column = "created_month"

    if start_time is not None and end_time is not None:
        snapshot_column = "created_at"
        query = f'select * from {schema}.{table} where (({snapshot_column}) >= "{start_time}" and ({snapshot_column}) <= "{end_time}") or ((updated_at) >= "{start_time}" and (updated_at) <= "{end_time}")'
        print(query)
        df = (
            spark.read.format("jdbc")
            .option("url", url)
            .option("user", conn_dict["user"])
            .option("password", conn_dict["password"])
            .option("query", query)
            .load()
        )
        if df.count() == 0:
            print("DataFrame is empty. Exiting Spark job.")
            spark.stop()
            return
        df = df.withColumn(partition_column, date_format(col("created_at"), "yyyy-MM"))
        print(df.show())
        print(df.count())
    else:
        min_value, max_value = _get_min_max(spark, url, conn_props, table, snapshot_column)

        snapshot_props = {
            "partitionColumn": snapshot_column,
            "lowerBound": str(min_value),
            "upperBound": str(max_value),
            "numPartitions": str(parallelism),
        }

        df = spark.read.jdbc(
            url=url,
            table=f"{schema}.{table}",
            properties={**conn_props, **snapshot_props},
        ).withColumn(partition_column, date_format(col("created_at"), "yyyy-MM"))

        if start_time is not None and end_time is not None:
            df = df.filter((col(partition_column) >= start_time) & (col(partition_column) <= end_time))

    df = _cast_boolean_columns_to_bigint(df)
    gcs_path = f"gs://nv-data-{env}-data-lake/snapshots/{schema}/{table}"
    df.repartition(10, partition_column).write.mode("overwrite").partitionBy(partition_column).parquet(gcs_path)


def _cast_boolean_columns_to_bigint(df):
    boolean_columns = {column: "bigint" for column, dtype in df.dtypes if dtype == "boolean"}
    return cast_columns(df, boolean_columns)


def _get_min_max(spark, url, conn_props, table, snapshot_column):
    query = f"(select min({snapshot_column}) as min_value, max({snapshot_column}) as max_value from {table}) t"
    min_max_lst = spark.read.jdbc(url=url, table=query, properties=conn_props).take(1)
    if not min_max_lst:
        raise RuntimeError(f"Empty table {table}")
    return min_max_lst[0]["min_value"], min_max_lst[0]["max_value"]


if __name__ == "__main__":
    env, schema, table, primary_keys_str, conn_dict_str, start_time, end_time = sys.argv[1:]
    primary_keys = json.loads(primary_keys_str)
    conn_dict = json.loads(conn_dict_str)
    conn_dict["password"] = codecs.decode(conn_dict["password"], "rot-13")

    spark = SparkSession.builder.getOrCreate()
    create_db_snapshot(spark, env, schema, table, primary_keys, conn_dict, start_time, end_time)
    spark.stop()
