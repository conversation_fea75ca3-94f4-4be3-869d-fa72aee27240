import pandas as pd
from google.cloud import storage

from common.utils.gcs import strip_uri
from data_warehouse.utils import logger

logger = logger.get_logger(__file__)
client = storage.Client()


class InvalidDirectoryException(Exception):
    """Raised when GCS directory is not found."""

    pass


def _append_slash(string):
    if string and not string.endswith("/"):
        string = string + "/"
    return string


def get_directory_blobs(bucket_name, directory, delimiter=None):
    directory = _append_slash(directory)
    blobs = list(client.list_blobs(bucket_or_name=bucket_name, prefix=directory, delimiter=delimiter))
    if not blobs:
        raise InvalidDirectoryException("Directory not found.")
    return blobs


def get_blob_size(bucket_name, blob_name):
    """Gets blob size in MB."""
    bucket = client.bucket(bucket_name)
    blob = bucket.get_blob(blob_name)
    size_byte = blob.size
    size_megabyte = size_byte / 1024 / 1024
    return size_megabyte


def get_directory_size(bucket_name, directory):
    """Gets directory size in MB."""
    blobs = get_directory_blobs(bucket_name, directory)
    directory_size = 0
    for blob in blobs:
        directory_size = directory_size + get_blob_size(bucket_name, blob.name)
    return directory_size


def calc_num_parquet_files(directory_size, mb_per_file=100):
    if mb_per_file <= 0:
        raise ValueError("mb_per_file must be greater than zero.")

    logger.info("Calculating repartition number...")
    num_files = round(directory_size / mb_per_file)
    num_files = max(num_files, 1)
    logger.info(f"{num_files} parquet file(s) required at {mb_per_file}MB per file.")
    return num_files


def delete_blob(bucket_name, blob_name):
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    blob.delete()


def delete_directory(bucket_name, directory):
    bucket = client.bucket(bucket_name)
    blobs = get_directory_blobs(bucket_name, directory)
    bucket.delete_blobs(blobs)
    logger.info(f"gs://{bucket_name}/{directory} deleted.")


def move_blob(bucket_name, destination_bucket_name, source_blob_name, destination_blob_name):
    """Copies blob with a new name and deletes the old blob."""
    bucket = client.bucket(bucket_name)
    source_blob = bucket.blob(source_blob_name)
    if destination_bucket_name != bucket_name:
        bucket = client.bucket(destination_bucket_name)
    bucket.rename_blob(source_blob, destination_blob_name)


def move_directory(
    bucket_name,
    destination_bucket_name,
    source_directory,
    destination_directory,
):
    """
    Copies blobs to destination directory and deletes source directory.
    Replaces blobs with same name (if exists).
    """
    blobs = get_directory_blobs(bucket_name, source_directory)
    source_directory = _append_slash(source_directory)
    destination_directory = _append_slash(destination_directory)
    for blob in blobs:
        source_blob_name = blob.name
        destination_blob_name = source_blob_name.replace(source_directory, destination_directory)
        logger.info(f"Moving {source_blob_name}")
        move_blob(
            bucket_name,
            destination_bucket_name,
            source_blob_name,
            destination_blob_name,
        )
    logger.info(
        f"gs://{bucket_name}/{source_directory} moved to gs://{destination_bucket_name}/{destination_directory}."
    )


def copy_blob(bucket_name, source_blob_name, destination_blob_name):
    """Copies blob to the same bucket with a new name."""
    bucket = client.bucket(bucket_name)
    source_blob = bucket.blob(source_blob_name)
    bucket.copy_blob(source_blob, bucket, destination_blob_name)


def copy_directory(bucket_name, source_directory, destination_directory):
    """Copies blobs to destination directory in same bucket. Replaces blobs with same name (if exists)."""
    blobs = get_directory_blobs(bucket_name, source_directory)
    source_directory = _append_slash(source_directory)
    destination_directory = _append_slash(destination_directory)
    for blob in blobs:
        source_blob_name = blob.name
        destination_blob_name = source_blob_name.replace(source_directory, destination_directory)
        copy_blob(bucket_name, source_blob_name, destination_blob_name)
    logger.info(f"gs://{bucket_name}/{source_directory} copied to gs://{bucket_name}/{destination_directory}.")


def get_uri_bucket_and_directory(gcs_uri):
    """
    Gets the bucket and directory from a GCS URI. Assumes that the first part after gs:// is the bucket, and the rest
    is the directory.
    """
    bucket, directory = strip_uri(gcs_uri).split("/", 1)
    return bucket, directory


def _get_partition_to_value(path, partition_cols):
    """Returns a dict of partition column name to value for partitions that are listed in partition_cols."""
    parts = strip_uri(path).split("/")
    part_to_val = {}
    for part in parts:
        col_name, val = None, None
        if "=" in part:
            col_name, val = part.split("=")[:2]
        if col_name in partition_cols:
            part_to_val[col_name] = val
    return part_to_val


def _get_subdirectories(gcs_uri):
    """Gets names of all blobs that does not contain 'part-'."""
    logger.info(f"fetching the sub directories for uri - {gcs_uri}")
    bucket, directory = get_uri_bucket_and_directory(gcs_uri)
    blobs = get_directory_blobs(bucket, directory, delimiter="part-")
    return [blob.name for blob in blobs]


def get_partition_combination_df(table_path, partitions):
    """
    Returns Pandas df containing all existing partition column name to value combinations for given partitions.

    :param table_path:        GCS URI containing parquet table.
    :type table_path:         string
    :param partitions:        Parquet table's partition columns. Can be a subset of all available partition columns.
    :type partitions:         list
    """
    logger.info(f"fetching the partition combinations for path - {table_path}")
    subdirectories = _get_subdirectories(table_path)
    complete_directories = []
    logger.info("finding the partitions in the fetched sub directories")
    for subdirectory in subdirectories:
        is_complete = all([partition in subdirectory for partition in partitions])
        if is_complete:
            complete_directories.append(subdirectory)

    partition_combinations = [_get_partition_to_value(directory, partitions) for directory in complete_directories]
    return pd.DataFrame(partition_combinations).drop_duplicates()


def is_path_exist(bucket, blob_path):
    """Checks if a path exists."""
    blobs = list(client.list_blobs(bucket_or_name=bucket, prefix=blob_path))
    return len(blobs) > 0
