-c https://raw.githubusercontent.com/apache/airflow/constraints-2.10.0/constraints-no-providers-3.8.txt

# Airflow Providers
apache-airflow-providers-apache-spark==3.0.0
apache-airflow-providers-apache-kafka==1.6.0
# apache-airflow-providers-cncf-kubernetes==7.14.0
# apache-airflow-providers-google==10.21.0
# apache-airflow-providers-atlassian-jira==2.6.1
apache-airflow-providers-jira==3.0.1
# apache-airflow-providers-mysql==5.6.2
apache-airflow-providers-zendesk==4.7.1


# flask-appbuilder==4.3.6
bs4==0.0.2
dill==0.3.8
facebook-business==17.0.0
fastparquet==0.6.3
flask-bcrypt==1.0.1
fsspec==2024.6.1
gcsfs==2024.6.1
gspread
# google-auth==2.33.0
apache-airflow-client==2.9.1
zdesk==2.8.1
xlrd
openpyxl==3.1.5
tqdm==4.66.4
# google-ads==25.0.0
# sqlparse==0.5.0
# google-cloud-storage==2.18.2
# google-cloud-core==2.4.1
# google-api-core==2.19.1
google-analytics-data==0.16.2
# google-api-python-client==2.140.0
# google==3.0.0
# sklearn
pyspark==3.1.1
scipy==1.10.0
sqlalchemy
python-snappy==0.5.4
# pyarrow==17.0.0
# pyhocon
psycopg2-binary==2.9.3
# protobuf
# pendulum==3.0.0
oauth2client==4.1.3
# nltk==3.8.2
mysql-connector==2.2.9
# kubernetes
h3==3.7.7
httplib2==0.22.0
# Salesforce-FuelSDK
# pandas
# numpy
simple-salesforce==1.11.1
lifetimes==0.11.1