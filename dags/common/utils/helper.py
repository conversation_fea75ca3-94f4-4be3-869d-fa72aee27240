import logging
from metadata.schema_map import SCHEMA_MAP
from metadata.table_sizes import TABLE_SIZES
from metadata.spark_conf import SPARK_CONF


def map_schema(schema):
    return SCHEMA_MAP.get(schema, schema)


def determine_group(schema_table):
    for group, sizes in TABLE_SIZES.items():
        for size, tables in sizes.items():
            if schema_table in tables:
                logging.info(f"Group found for table {schema_table}: {group}")
                return group
    logging.error(f"Group not found for table {schema_table}")
    return None


def get_table_size_category(schema_table, table_sizes, table_sizes_airflow=None, default_size='medium'):
    combined_sizes = {}

    combined_sizes.update(table_sizes)
    if table_sizes_airflow:
        combined_sizes.update(table_sizes_airflow)

    for sizes, tables in combined_sizes.items():
        if schema_table in tables:
            logging.info(f"Size category found for table {schema_table}: {sizes}")
            return sizes
    logging.warning(f"Size category not found for table {schema_table}. Defaulting to {default_size}.")
    return default_size


def get_spark_conf_by_table(env, schema_table, table_sizes_airflow=None, group=None):
    group = group if group else determine_group(schema_table)
    if not group:
        raise ValueError(f"Group not found for table {schema_table}")

    if group == 'dwh':
        table_size_category = get_table_size_category(schema_table, TABLE_SIZES[group],
                                                      table_sizes_airflow=table_sizes_airflow, default_size='small')
    else:
        if schema_table in table_sizes_airflow:
            table_size_category = get_table_size_category(schema_table, TABLE_SIZES[group],
                                                          table_sizes_airflow=table_sizes_airflow)
        else:
            table_size_category = get_table_size_category(schema_table, TABLE_SIZES[group])
    if table_size_category not in SPARK_CONF[env][group]:
        raise ValueError(f"Size category {table_size_category} not found for environment {env} and group {group}")
    return SPARK_CONF[env][group][table_size_category]


def build_config(schema, table, env, image, task_type, table_sizes_airflow=None, group=None):
    if task_type == "process":
        schema_table = f"process_cdc_messages_{schema}_{table}"
    elif task_type == "delta":
        schema_table = f"pii_delta_{schema}_{table}"
    elif task_type == "dwh":
        schema_table = f"{schema}_{table}"
    elif task_type == "compaction":
        schema_table =  f"pii_compact_delta_{schema}_{table}"
    else:
        raise ValueError(f"Unknown task type: {task_type}")
    group = group if group else determine_group(schema_table)
    if not group:
        raise ValueError(f"Group not found for table {schema_table}")

    config = {
        "namespace": env,
        "spark_version": "3.1.1",
        "task_type": task_type,
        "version": "3.1.1",
        "image": image,
        "service_account": f"{env}-sparkoperator-spark",
        "env": env,
        "group": group,
        "schema_table": schema_table,
        "spark_conf": get_spark_conf_by_table(env=env, schema_table=schema_table,
                                              table_sizes_airflow=table_sizes_airflow, group=group),
        "time_to_live": "300",
        "deps": {
            "jars": [],
            "files": [],
            "pyFiles": []
        }
    }
    return config
