import logging
from tempfile import NamedTemporaryFile

import pandas as pd
from airflow.models import BaseOperator, SkipMixin
from airflow.providers.google.cloud.hooks.gcs import GCSHook

from salesforce.hooks.salesforce_hook import SalesforceHook
from data_warehouse.utils.gcs import get_uri_bucket_and_directory


PARTITION_COLUMN = "nv_updated_date"


class SFAttachmentsToGCSOperator(BaseOperator, SkipMixin):
    _FILE_TYPE_EXTENSIONS = {
        "POWER_POINT_M": ".pptm",
        "PDF": ".pdf",
        "M4A": ".m4a",
        "EXCEL_M": ".xlsm",
        "ASF": ".asf",
        "POWER_POINT": ".ppt",
        "MHTML": ".mhtml",
        "WORD_M": ".docm",
        "CSV": ".csv",
        "SHTML": ".shtml",
        "XML": ".xml",
        "WORD": ".doc",
        "PNG": ".png",
        "POWER_POINT_X": ".pptx",
        "TIFF": ".tiff, .tif",
        "RTF": ".rtf",
        "MP3": ".mp3",
        "MSG": ".msg",
        "MP4": ".mp4",
        "BMP": ".bmp",
        "WEBP": ".webp",
        "UNKNOWN": ".unknown",
        "EXE": ".exe",
        "MOV": ".mov",
        "MPEG": ".mpeg",
        "SVG": ".svg",
        "PSD": ".psd",
        "ZIP": ".zip",
        "GIF": ".gif",
        "HTML": ".html",
        "TEXT": ".txt",
        "EXCEL": ".xls",
        "TIF": ".tif",
        "AVIF": ".avif",
        "SNOTE": ".snote",
        "WORD_X": ".docx",
        "JPEG": ".jpeg",
        "JSON": ".json",
        "JPG": ".jpg",
        "ICS": ".ics",
        "EXCEL_X": ".xlsx",
        "HTM": ".htm",
        "ODS": ".ods",
        "OGG": ".ogg",
        "AVI": ".avi",
        "LOG": ".log",
        "WAV": ".wav",
        "ODT": ".odt",
        "JFIF": ".jfif",
        "AAC": ".aac"
    }
    _OBJ_PATH = "sobjects/ContentVersion/{version_id}/VersionData"
    EXECUTION_DATE_FORMAT = "%Y-%m-%d %H-%M-%S"

    def __init__(
        self,
        gcs_bucket,
        dwh_input_path,
        sf_conn_id="salesforce_default",
        gcs_conn_id="google_cloud_default",
        gcs_folder_path="salesforce/sales_cloud/sf_export_files",
        *args,
        **kwargs,
    ):
        super(SFAttachmentsToGCSOperator, self).__init__(*args, **kwargs)

        self.sf_conn_id = sf_conn_id
        self.dwh_input_path = dwh_input_path,
        self.gcs_conn_id = gcs_conn_id
        self.gcs_bucket = gcs_bucket
        self.gcs_folder_path = gcs_folder_path

    def _get_dwh_table(self, gcs_hook, execution_date):
        """
        Read in data warehouse table from Google Cloud Storage(GCS) as pandas Dataframe

        :param execution_date: Datetime when task runs
        :return:               Pandas Dataframe
        """
        measurement_datetime_partition = f"/measurement_datetime={execution_date.strftime(self.EXECUTION_DATE_FORMAT)}"
        dwh_table_path = self.dwh_input_path[0] + measurement_datetime_partition
        bucket, directory = get_uri_bucket_and_directory(dwh_table_path)
        parquet_file_directories = gcs_hook.list(bucket_name=bucket, prefix=directory, delimiter=".parquet")
        dfs = []
        for file_directory in parquet_file_directories:
            dfs.append(pd.read_parquet(f"gs://{bucket}/{file_directory}"))
        return pd.concat(dfs, ignore_index=True)
    
    def _get_response_from_salesforce(self, gcs_hook, df):
        """
        Call G-suite API as internal services using G-suite Hook, schematize response and return as a Pandas Dataframe

        :param df:              DWH data frame to extract values to be searched
        :return:                Pandas Dataframe
        """
        sf_hook = SalesforceHook(conn_id=self.sf_conn_id)
        for _, row in df.iterrows():
            version_id = row["id"]
            path = self._OBJ_PATH.format(version_id=version_id)
            try:
                response = sf_hook.sf._call_salesforce(method="GET", url=sf_hook.sf.base_url + path)
                data = response.content
                gs_obj_name = self.gcs_folder_path + "/" + row['id'] + self._FILE_TYPE_EXTENSIONS[row['file_type']]
                self._write_results(gcs_hook, data, gs_obj_name)
            except Exception as e:
                logging.error(f"Error processing ID {version_id}: {str(e)}")

    def _write_results(self, gcs_hook, data, gs_obj_name):
        """
        Write and read a blob from GCS using file-like IO

        :param data: binary data to be stored
        :param gs_obj_name: path to store data
        :return:   None
        """
        with NamedTemporaryFile("wb") as tmp:
            tmp.write(data)
            tmp.flush()
            gcs_hook.upload(self.gcs_bucket, gs_obj_name, tmp.name)

    def execute(self, context):
        """
        Fetches gsuite entity data and stores it in GCS in parquet format.

        :param context: Task context
        """
        gcs_hook = GCSHook(self.gcs_conn_id)
        logging.info(f"Loading input data warehouse table: {self.dwh_input_path[0].split('/')[-1]}...")
        execution_date = context["execution_date"]
        dwh_df = self._get_dwh_table(gcs_hook, execution_date.add(days=1))
        logging.info(f"Loaded data warehouse table with {dwh_df.shape[0]} records")

        logging.info("Fetching data from salesforce")
        self._get_response_from_salesforce(gcs_hook, dwh_df)
        logging.info("Fetched data from salesforce")
