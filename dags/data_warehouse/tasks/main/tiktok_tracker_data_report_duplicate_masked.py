import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, parquet_tables_masked, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_TRACKER_DATA_REPORT_DUPLICATE_MASKED + ".py",
    task_name=data_warehouse.CNTicketingToolDAG.Task.TIKTOK_TRACKER_DATA_REPORT_DUPLICATE_MASKED,
    system_ids=(constants.SystemID.GL,),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    # input_env = "prod"
    is_masked = True
    input_env = env

    input_config = base.InputConfig(
        parquet_tables=(
            base.InputTable(path=parquet_tables_masked.GSheets(input_env).TIKTOK_TRACKER_DATA_REPORT,
                            view_name="tiktok_tracker_data_report"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                select 
                    ticket_type
                    , cast(lsp_ticket_no as bigint) as lsp_ticket_id
                    , tid as tracking_id
                    , country
                    , level_1_tag
                    , level_2_tag
                    , tag_description
                    , ticket_description
                    , case when tag_vs_description_match = '正确' then 1 
                            when tag_vs_description_match = '错误' then 0 else null
                            end as tag_vs_description_match
                    , supposed_tag_description
                    , ticket_issues
                    , to_timestamp(creation_time,'MM/dd/yyyy HH:mm:ss') as creation_time
                    , assign_to
                    , to_timestamp(first_response_time,'MM/dd/yyyy HH:mm:ss') as first_response_time
                    , case when reopen_nv_cause_flag  = 'NJV原因' then 1
                            when reopen_nv_cause_flag  = '非NJV原因' then 0 else null
                            end as reopen_nv_cause_flag
                    , reopen_type
                    , reship_tid
                    , cast(internal_ticket_no as bigint) as internal_ticket_no
                    , compensation_flag
                    , dnr_issue_type
                    , case when tt_info_complete_flag = '是' then 1
                            when tt_info_complete_flag = '不是' then 0 else null
                            end as tt_info_complete_flag
                    , case when country_response_capability_flag  = '能' then 1
                            when country_response_capability_flag  = '不能' then 0 else null
                            end as country_response_capability_flag
                    , no_investigation_reason
                    , remarks
                    , my_refresh_status_flag
                    , to_date(Input_Date, 'MM/dd/yyyy') as created_date
                from tiktok_tracker_data_report
                """,
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).TIKTOK_TRACKER_DATA_REPORT_DUPLICATE,
        measurement_datetime=measurement_datetime,
        partition_by=("created_date",),
        # write_mode='merge',
        # primary_keys=["lsp_ticket_id","first_response_time"],
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.measurement_datetime,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    run(spark, task_config)
    spark.stop()