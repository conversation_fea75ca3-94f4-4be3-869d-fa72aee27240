import json
from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from data_warehouse.utils import airflow
from metadata.data_warehouse import SalesforceDAG
from metadata.parquet_tables_masked import DataWarehouse
from plugins.salesforce.operators.sf_attachments_to_gcs_operator import SFAttachmentsToGCSOperator

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")

default_args = {
    "owner": "airflow",
    "start_date": "2024-01-01",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


with DAG(
    dag_id="datalake_sales_cloud_attachments",
    default_args=default_args,
    schedule_interval="0 18 * * *",  # Daily 2am SGT
    concurrency=3,
    max_active_runs=1,
    catchup=False,
    tags=["data_lake"],
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"

    wait_for_dwh_table_task = airflow.get_dwh_external_task_sensor(
        SalesforceDAG.DAG_ID,
        SalesforceDAG.Task.SALESFORCE_CONTENT_VERSION_ENRICHED_MASKED,
    )
    load_objects = SFAttachmentsToGCSOperator(
        task_id=f"load_salesforce_attachments",
        gcs_bucket=gcs_bucket,
        dwh_input_path=DataWarehouse(env).SALESFORCE_CONTENT_VERSION_ENRICHED_MASKED,
        sf_conn_id="salesforce_default",
        gcs_conn_id="google_cloud_default",
        gcs_folder_path=f"salesforce/sales_cloud/sf_export_files",
    )

    wait_for_dwh_table_task >> load_objects
