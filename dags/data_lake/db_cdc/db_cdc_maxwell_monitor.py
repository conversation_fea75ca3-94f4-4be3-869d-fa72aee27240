from datetime import datetime, timedelta

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import PythonOperator

from common.utils.alerts import raise_grafana_alert
from metadata.cdc_data_integrity import MAXWELL_DB_SCHEMAS
from common.airflow import notifications as notif
from airflow.providers.mysql.hooks.mysql import MySqlHook

INTERVAL_DURATION = 15
ALERT_CHANNEL = "high_priority_alert"

env = Variable.get("env")
mysql_connections = Variable.get("mysql_connections", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "start_date": datetime(2024, 5, 28, 0, 0, 0),
    "retries": 3,
    "retry_delay": timedelta(minutes=2),
    "executor_config": {"KubernetesExecutor": {"request_memory": "128Mi"}},
    "priority_weight": 10,
    "pool": "sensor_pool",
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


def fetch_and_verify_maxwell_schema_positions(**kwargs):
    sql_str = "select gtid_set from {schema}.positions"
    connection = MySqlHook(mysql_conn_id='maxwell_db').get_conn()
    for schema in MAXWELL_DB_SCHEMAS.get(env):
        summary = "Maxwell binlog old_pos: {old_pos} new_pos: {new_pos} schema:{schema}"
        cursor = connection.cursor()
        cursor.execute(sql_str.format(schema=schema))
        data = cursor.fetchall()
        maxwell_ps = f"{schema}_maxwell_position"
        print(f"fetching maxwell position - {schema}_maxwell_position")
        print(f"current maxwell pos: {data}")
        old_position = Variable.get(maxwell_ps)
        print(f"current maxwell pos: {old_position}")
        if len(data) > 0:  # if the database is returning something then go ahead and alert
            if not old_position:
                Variable.set(maxwell_ps, data)
                status = raise_grafana_alert("Maxwell bing log position",
                                             f"Old position has been removed or Running for the first time - {schema}",
                                             'alerting',
                                             'Data'
                                             )
                if not status:  # if the alert raise is not successful then  send a chat notification
                    notif.chat.send_ti_failure_alert(kwargs)
            elif old_position == 'First Run':
                print("ignoring the first run")
            elif hash(str(data)) == hash(str(old_position)):
                print(f"Alerting maxwell position {schema} - {data} - {old_position}")
                status = raise_grafana_alert("Maxwell bing log position",
                                             summary.format(old_pos=old_position, new_pos=data[0], schema=schema),
                                             'alerting',
                                             'Data'
                                             )
                print(status, "status")
                if not status:  # if the alert raise is not successful then  send a chat notification
                    notif.chat.send_ti_failure_alert(kwargs)
            Variable.set(maxwell_ps, data)
        else:
            status = raise_grafana_alert("Maxwell bing log position",
                                         f"Maxwell db is not returning any records - {schema}",
                                         'alerting',
                                         'Data'
                                         )
            if not status:  # if the alert raise is not successful then  send a chat notification
                notif.chat.send_ti_failure_alert(kwargs)
            notif.chat.send_ti_failure_alert(kwargs)
            print(summary.format(old_pos=old_position, new_pos=data[0], schema=schema))


with DAG(
        dag_id="db_cdc_maxwell_monitor",
        schedule_interval=f"*/{INTERVAL_DURATION} * * * *",
        default_args=default_args,
        max_active_runs=1,
        catchup=False,
        on_failure_callback=notif.create_dag_run_failure_callback(env == "prod"),
        tags=["data_lake", "db_cdc"],
) as dag:
    position_checker = PythonOperator(
        task_id=f"check_maxwell_positions",
        python_callable=fetch_and_verify_maxwell_schema_positions,
        retries=1,
    )
