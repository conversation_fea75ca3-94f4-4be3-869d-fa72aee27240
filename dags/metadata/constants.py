from dataclasses import dataclass, field
from datetime import timedelta

# TODO: Deprecate these loose variables when DWH tasks are migrated to anonymized data lake.
BI_SENSITIVE_REPORTS_BASE_URI = "gs://nv-data-{}-bi-sensitive-reports"
DATALAKE_BASE_URI = "gs://nv-data-{}-data-lake"
DATALAKE_NV_DB_URI = f"{DATALAKE_BASE_URI}/db"
MASKED_DATALAKE_BASE_URI = "gs://nv-data-{}-data-lake"
DATAWAREHOUSE_BASE_URI = "gs://nv-data-{}-datawarehouse"
MASKED_DATA_WAREHOUSE_BASE_URI = "gs://nv-data-{}-data-warehouse"
DATASCIENCE_BASE_URI = "gs://nv-data-{}-datascience"
NV_DATA_ARCHIVE = "nv-data-archive"


@dataclass
class CDCKafkaTopics:
    env: str
    ticdc: str = "data-{}-ticdc-data-engineering-gl-cdc"

    def __post_init__(self):
        self.ticdc = self.ticdc.format(self.env)


@dataclass
class LegacyGcsBucketUris:
    env: str
    legacy: str = "gs://nv-data-{}-data-lake"
    raw: str = "gs://nv-data-{}-data-lake-raw"
    processed: str = field(init=False)
    db: str = field(init=False)

    def __post_init__(self):
        self.raw = self.raw.format(self.env)
        self.legacy = self.legacy.format(self.env)
        self.processed = self.legacy
        self.db = f"{self.legacy}/db"


@dataclass
class DataLakeGcsBucketUris:
    env: str
    raw: str = "gs://nv-data-{}-data-lake-raw"
    processed: str = "gs://nv-data-{}-data-lake-processed"
    db: str = "gs://nv-data-{}-data-lake/db"

    def __post_init__(self):
        self.raw = self.raw.format(self.env)
        self.processed = self.processed.format(self.env)
        self.db = self.db.format(self.env)


@dataclass
class DataLakeGcsNinjaMartBucketUris:
    env: str
    legacy: str = "gs://nv-data-{}-datalake"
    raw: str = "gs://nv-data-prod-ninjamart-raw"
    processed: str = "gs://nv-data-{}-data-lake-processed"
    db: str = "gs://nv-data-{}-data-lake/db"

    def __post_init__(self):
        self.raw = self.raw.format(self.env)
        self.processed = self.processed.format(self.env)
        self.db = self.db.format(self.env)


class DataIntegrityFix:
    MISMATCH_FOLDER_URI = (
        "gs://nv-data-{env}-datalake/data_integrity_fix/{schema}/{table}/{mismatch_type}/{execution_date}"
    )
    SOURCE_SNAPSHOT_GCS_URI = "gs://nv-data-{env}-datalake/snapshots/{schema}/{table}"
    TARGET_DELTA_GCS_URI = "gs://nv-data-{env}-datalake/delta/{schema}/{table}"
    PARTITION_COLUMN = "created_month"
    FILTER_COLUMN = "created_at"


class Email:
    DATA_ENGR = "<EMAIL>"
    SRE = "<EMAIL>"


class SystemID:
    """Enum for System IDs."""

    ID = "id"
    MM = "mm"
    MY = "my"
    PH = "ph"
    SG = "sg"
    TH = "th"
    VN = "vn"
    IN = "in"
    GL = "gl"


class Timeout:
    ONE_MINUTE = timedelta(minutes=1)
    TWO_MINUTES = timedelta(minutes=2)
    FIVE_MINUTES = timedelta(minutes=5)
    FIFTEEN_MINUTES = timedelta(minutes=15)
    FORTY_FIVE_MINUTES = timedelta(minutes=45)
    ONE_HOUR = timedelta(hours=1)
    TWO_HOURS = timedelta(hours=2)
    THREE_HOURS = timedelta(hours=3)
    FOUR_HOURS = timedelta(hours=4)
    SIX_HOURS = timedelta(hours=6)
    EIGHT_HOURS = timedelta(hours=8)
    TWELVE_HOURS = timedelta(hours=12)
    FIFTEEN_HOURS = timedelta(hours=15)
    TWENTY_FOUR_HOURS = timedelta(hours=24)


class ExecutionDelta:
    SIX_DAYS_LATER = timedelta(days=-6)
