import sys

from pyspark.sql import SparkSession

from common import date
from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, delta_tables, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.OrderEventsDAG.Task.WAREHOUSE_SCAN_EVENTS_MASKED + ".py",
    task_name=data_warehouse.OrderEventsDAG.Task.WAREHOUSE_SCAN_EVENTS_MASKED,
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MM,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.SG,
        constants.SystemID.TH,
        constants.SystemID.VN,
    ),
)


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 0, 1)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        delta_tables=(
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).INBOUND_SCANS,
                view_name="inbound_scans",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.CoreProdGL(system_id, input_env, is_masked).WAREHOUSE_SWEEPS,
                view_name="warehouse_sweeps",
                input_range=lookback_ranges.input,
            ),
            base.InputTable(
                path=delta_tables.RouteProdGL(input_env, is_masked).ROUTE_LOGS,
                view_name="route_logs",
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="hub_inbound_scans",
                jinja_template="""
                -- inbound_scans type 2=SORTING_HUB

                SELECT scans.id AS scan_id
                       , scans.order_id
                       , scans.route_id
                       , scans.hub_id AS scan_hub_id
                       , cast(route.driver_id as bigint) AS route_driver_id
                       , cast(route.hub_id as bigint) AS route_hub_id
                       , IF(scans.route_id IS NOT NULL, 'route_inbound', 'global_inbound') AS type
                       , 'inbound_scans' AS source_table
                       , from_utc_timestamp(scans.created_at, '{{ local_timezone }}') AS event_datetime
                       , date_format(scans.created_at, 'yyyy-MM') AS created_month
                FROM inbound_scans AS scans
                LEFT JOIN route_logs AS route 
                    ON scans.route_id = route.legacy_id
                    AND route.system_id = '{{ system_id }}'
                WHERE scans.deleted_at IS NULL
                  AND scans.type = 2
                  AND scans.order_id IS NOT NULL
                """,
                jinja_arguments={
                    "local_timezone": getattr(date.Timezone, system_id.upper()),
                    "system_id": system_id,
                },
            ),
            base.TransformView(
                view_name="warehouse_sweeps_enriched",
                jinja_template="""
                SELECT id AS scan_id
                       , order_id
                       , cast(NULL AS int) AS route_id
                       , hub_id AS scan_hub_id
                       , cast(NULL AS int) AS route_driver_id
                       , cast(NULL AS int) AS route_hub_id
                       , 'warehouse_sweep' AS type
                       , 'warehouse_sweeps' AS source_table
                       , from_utc_timestamp(created_at, '{{ local_timezone }}') AS event_datetime
                       , date_format(created_at, 'yyyy-MM') AS created_month
                FROM warehouse_sweeps
                WHERE deleted_at IS NULL
                  AND order_id IS NOT NULL
                """,
                jinja_arguments={"local_timezone": getattr(date.Timezone, system_id.upper())},
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                SELECT *
                FROM hub_inbound_scans
                UNION ALL
                SELECT *
                FROM warehouse_sweeps_enriched
                """,
            ),
        ),
        output_range=lookback_ranges.output,
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).WAREHOUSE_SCAN_EVENTS,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
        partition_by=("created_month",),
        output_range=lookback_ranges.output,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    df = base.run(spark, config)
    return df


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
