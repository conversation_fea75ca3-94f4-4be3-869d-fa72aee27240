from airflow import DAG
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from datetime import timedelta
import logging

tables = [
    "fm_zones",
    "fm_zones_id",
    "fm_zones_my",
    "fm_zones_mm",
    "fm_zones_ph",
    "fm_zones_sg",
    "fm_zones_th",
    "fm_zones_vn"
]

# Function to fetch data from PostgreSQL (FM Zones database)
def fetch_all_data_from_fm_zones_db(postgres_hook, query, table, limit, offset=0):
    rows_fetched = []
    while True:
        query_with_limit = f"{query} LIMIT {limit} OFFSET {offset};"
        rows = postgres_hook.get_records(query_with_limit)
        if not rows:
            break
        rows_fetched.extend(rows)
        offset += limit
        logging.info(f"Fetched {len(rows)} rows (Total fetched: {len(rows_fetched)})")

    logging.info(f"All rows from {table} fetched. Total rows: {len(rows_fetched)}")
    return rows_fetched

# Function to insert data into the corresponding MySQL table using transactions
def upsert_data_into_mysql(mysql_hook, mysql_table, rows):
    upsert_query = f"""
        INSERT INTO {mysql_table} 
        (id, system_id, hub_id, `name`, short_name, description, latitude, longitude, created_at, updated_at, deleted_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            system_id = VALUES(system_id),
            hub_id = VALUES(hub_id),
            `name` = VALUES(`name`),
            short_name = VALUES(short_name),
            description = VALUES(description),
            latitude = VALUES(latitude),
            longitude = VALUES(longitude),
            updated_at = VALUES(updated_at),
            deleted_at = VALUES(deleted_at);
    """

    conn = mysql_hook.get_conn()
    cursor = conn.cursor()

    try:
        conn.autocommit = False
        cursor.executemany(upsert_query, rows)
        logging.info(f"Upserted {len(rows)} rows into {mysql_table}")
        
        conn.commit()
    except Exception as e:
        conn.rollback()
        logging.error(f"Error upserting data into {mysql_table}: {e}")
        raise
    finally:
        cursor.close()
        conn.close()

# Function to handle batch processing logic for each table
def process_table(postgres_conn_id, mysql_conn_id, table_name):
    logging.info(f"Processing table: {table_name}")
    
    postgres_hook = PostgresHook(postgres_conn_id=postgres_conn_id)
    mysql_hook = MySqlHook(mysql_conn_id=mysql_conn_id)
    
    query = f"""
    SELECT id, system_id, hub_id, name, short_name, description, latitude, longitude, created_at, updated_at, deleted_at
    FROM {table_name}
    """
    
    rows = fetch_all_data_from_fm_zones_db(postgres_hook, query, table_name, 1000, 0)
    if rows:
        upsert_data_into_mysql(mysql_hook, table_name, rows)

# DAG definition
with DAG(
    'pggis_to_fm_zone_table_exporter',
    default_args={
        'owner': 'airflow',
        'start_date': days_ago(1),
        'retries': 3,
        'retry_delay': timedelta(minutes=3),
    },
    schedule_interval='@daily',
    catchup=False,
) as dag:

    for table_name in tables:
        task = PythonOperator(
            task_id=f"export_{table_name}_from_zones_prod_gl_postgres_to_tidb_redash",
            python_callable=process_table,
            op_args=['pggis_fm_zones', 'tidb_redash_fm_zones', table_name],
            provide_context=True,
        )
        task
