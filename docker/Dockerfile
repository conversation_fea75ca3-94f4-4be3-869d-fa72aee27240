# NOTE: paths are relative to the project root since the build context we specify is the project root

FROM apache/airflow:2.3.4-python3.7
USER root

ENV SPARK_HOME /home/<USER>/.local/lib/python3.7/site-packages/pyspark
ENV PATH $PATH:$SPARK_HOME/bin
ENV PYTHONPATH $PYTHONPATH:$AIRFLOW_HOME/dags
ENV GOOGLE_APPLICATION_CREDENTIALS /keys/key.json
ENV PATH $PATH:/opt/google-cloud-sdk/bin

RUN mkdir -p /usr/share/man/man1 \
    && apt-get update -y \
    && apt-get install -y \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        libsnappy-dev \
        procps \
        software-properties-common \
        unzip \
        wget \
        apt-transport-https \
    && mkdir -p /etc/apt/keyrings \
    && wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | tee /etc/apt/keyrings/adoptium.asc \
    && echo "deb [signed-by=/etc/apt/keyrings/adoptium.asc] https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
    && apt-get update -y \
    && apt-get install -y temurin-8-jdk \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

USER airflow
COPY requirements-spark.txt requirements-spark.txt
RUN pip install --upgrade pip==20.2.4
RUN pip install --no-cache-dir --user -r requirements-spark.txt
RUN pip install --user delta-spark==1.0.0
COPY requirements-airflow.txt requirements.txt
RUN pip install --no-cache-dir --user -r requirements.txt


USER root
RUN cd $SPARK_HOME/jars && curl -O https://storage.googleapis.com/hadoop-lib/gcs/gcs-connector-hadoop2-2.0.1.jar
COPY docker/keys /keys
RUN curl -sSL https://sdk.cloud.google.com > /tmp/gcl && bash /tmp/gcl --install-dir=/opt --disable-prompts
RUN ln -s /spark-conf $SPARK_HOME/conf

# zip dags folder for use of spark UDF
COPY dags $AIRFLOW_HOME/dags
RUN mkdir $AIRFLOW_HOME/files \
    && tar -cvzf $AIRFLOW_HOME/files/dags.zip $AIRFLOW_HOME/dags

USER airflow
RUN gcloud auth activate-service-account --key-file /keys/key.json --project ninja-van-data
