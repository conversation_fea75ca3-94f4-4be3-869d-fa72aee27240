import sys
import os
import json
import tempfile
from pyspark.sql import SparkSession
from delta.tables import DeltaTable
from pyspark.sql.functions import lit
from datetime import datetime
from pathlib import Path

def read_table(spark, jdbc_url, jdbc_properties, view_name, ca_cert_content=None):
    """
    Read data from TiDB table using JDBC
    
    Args:
        spark: SparkSession
        jdbc_url: JDBC URL for TiDB
        jdbc_properties: Dictionary with TiDB connection properties
        view_name: Name of the table or view to read
        ca_cert_content: CA certificate content string for SSL/TLS (optional)
        
    Returns:
        DataFrame with data from TiDB or None if there's an error
    """
    print(f"Querying from {view_name}")
    temp_cert_file = None
    
    try:
        # Base JDBC URL
        jdbc_url = jdbc_url

        # SSL configuration with certificate content
        if ca_cert_content:
            # Create a temporary file for the certificate
            temp_cert_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pem")
            temp_cert_file.write(ca_cert_content.encode())
            temp_cert_file.flush()
            temp_cert_path = temp_cert_file.name
            
            jdbc_url += (
                "?useSSL=true"
                "&requireSSL=true"
                "&enabledTLSProtocols=TLSv1.2"
                "&verifyServerCertificate=true"
                f"&serverSslCert={temp_cert_path}"
            )

        # SQL query with alias
        sql_query = f"(SELECT * FROM {view_name}) AS tmp_view"

        # Read using Spark JDBC
        df = spark.read \
            .format("jdbc") \
            .option("url", jdbc_url) \
            .option("dbtable", sql_query) \
            .option("user", jdbc_properties["user"]) \
            .option("password", jdbc_properties["password"]) \
            .option("driver", jdbc_properties["driver"]) \
            .option("zeroDateTimeBehavior", "convertToNull") \
            .option("tinyInt1isBit", "false") \
            .load()

        # Return DataFrame if not empty
        if not df.rdd.isEmpty():
            return df
        else:
            return None
    except Exception as e:
        print(f"Error querying from {view_name}: {e}")
        return None
    finally:
        # Clean up temporary certificate file
        if temp_cert_file:
            try:
                os.unlink(temp_cert_file.name)
            except Exception as e:
                print(f"Warning: Failed to delete temporary certificate file: {e}")


def merge(spark, source_df, delta_path):
    """
    Replace data in Delta Lake table with new data
    
    Args:
        spark: SparkSession
        source_df: Source DataFrame from TiDB
        delta_path: Path to Delta Lake table
    """
    # Add processing timestamp column
    source_df = source_df.withColumn("nv_processed_at", lit(datetime.now().timestamp()))
    
    source_df.write.format("delta").mode("overwrite").save(delta_path)
    print(f"Successfully written data in {delta_path}")


def construct_jdbc_connection(db_config):
    """
    Construct JDBC connection URL and properties from a config dictionary
    
    Args:
        db_config: Dictionary with database connection parameters
            (host, port, schema, user, password, driver)
        
    Returns:
        Tuple of (jdbc_url, jdbc_properties)
    """
    try:
        # Extract connection parameters
        host = db_config.get("host")
        port = db_config.get("port", 4000)
        schema = db_config.get("schema")
        user = db_config.get("user")
        password = db_config.get("password")
        driver = db_config.get("driver", "com.mysql.jdbc.Driver")
        
        if not host or not user or not password:
            print("Missing required database configuration parameters")
            return None, None
        
        # Construct JDBC URL
        jdbc_url = f"jdbc:mysql://{host}:{port}"
        if schema:
            jdbc_url += f"/{schema}"
        
        # Setup connection properties
        jdbc_properties = {
            "user": user,
            "password": password,
            "driver": driver
        }
            
        return jdbc_url, jdbc_properties
        
    except Exception as e:
        print(f"Error constructing JDBC connection: {e}")
        return None, None


def main():
    """
    Main function to read from TiDB and merge to Delta Lake for a single view
    """
    env, view_name, date, delta_base_path, db_config_json = sys.argv[1:6]
    
    # Parse the database configuration JSON
    try:
        db_config = json.loads(db_config_json)
    except Exception as e:
        print(f"Error parsing database configuration JSON: {e}")
        sys.exit(1)
    
    # Construct JDBC connection details
    jdbc_url, jdbc_properties = construct_jdbc_connection(db_config)
    
    if not jdbc_url or not jdbc_properties:
        print("Failed to construct JDBC connection details")
        sys.exit(1)
    
    print(f"Using connection: {jdbc_url} with user {db_config.get('user')}")
    
    # Get CA certificate content from config
    ca_cert_content = db_config.get("ca_cert")
    if ca_cert_content:
        print(f"Using CA certificate from configuration")
    else:
        print("No CA certificate provided in configuration")
    
    spark = SparkSession.builder.getOrCreate()
    
    try:
        # Read data from TiDB
        source_df = read_table(spark, jdbc_url, jdbc_properties, view_name, ca_cert_content)
        
        if source_df is not None:
            # Check if DataFrame is empty
            row_count = source_df.count()
            if row_count == 0:
                print(f"Source DataFrame for {view_name} is empty, skipping merge")
                # Exit successfully
                sys.exit(0)
                
            print(f"Successfully read {row_count} rows from {view_name}")
            
            # Construct full Delta Lake path
            delta_path = f"{delta_base_path}{view_name}"
            print(f"Using Delta Lake path: {delta_path}")
            
            # Merge data into Delta Lake
            merge(spark, source_df, delta_path)
            print(f"Successfully processed view: {view_name}")
        else:
            print(f"No data read from {view_name}, skipping merge")
            # Exit successfully
            sys.exit(0)
    
    except Exception as e:
        print(f"Error processing {view_name}: {e}")
        sys.exit(1)
    finally:
        spark.stop()


if __name__ == "__main__":
    main() 