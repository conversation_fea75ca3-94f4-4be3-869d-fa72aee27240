SPARK_CONF = {
    "dev": {
        "cdc": {
            "small": {
                "spark.executor.instances": "1",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.executor.cores": "4",
                "spark.sql.adaptive.enabled": "true",
                "spark.executor.memory": "5600m",
            },
            "medium": {
                "spark.executor.instances": "5",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.cores": "8",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.driver.memory": "5600m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "240",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInWrite": "CORRECTED",
            },
            "large": {
                "spark.executor.instances": "26",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.cores": "10",
                "spark.driver.memory": "5600m",
                "spark.sql.adaptive.enabled": "true",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.driver.cores": "3",
                "spark.sql.shuffle.partitions": "960",
            },
        },
        "cdc_hive": {
            "small": {"spark.executor.instances": "1", "spark.executor.memory": "1g", "spark.driver.memory": "1g"},
            "large": {"spark.executor.instances": "2", "spark.executor.memory": "5g", "spark.driver.memory": "5g"},
        },
        "compaction": {
            "medium": {
                "spark.executor.instances": "1",
                "spark.executor.memory": "5500m",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.executor.cores": "2",
                "spark.driver.memory": "5500m",
                "spark.driver.cores": "1",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInWrite": "CORRECTED",
            },
            "large": {
                "spark.executor.instances": "2",
                "spark.executor.memory": "5500m",
                "spark.sql.adaptive.enabled": "true",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.executor.cores": "2",
                "spark.driver.memory": "5500m",
                "spark.driver.cores": "1",
            },
            "larger": {
                "spark.executor.instances": "10",
                "spark.executor.memory": "17000m",
                "spark.sql.adaptive.enabled": "true",
                "spark.executor.cores": "4",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
            },
        },
        "dwh": {
            "small": {
                "spark.executor.instances": "20",
                "spark.executor.cores": "3",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "120",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "medium": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "3",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "320",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "3",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "320",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large_l2": {
                "spark.executor.instances": "34",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.maxResultSize": "2048m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "480",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large_l3": {
                "spark.executor.instances": "55",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "3",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "800",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
            "large_l4": {
                "spark.executor.instances": "60",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "3",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
            },
        },
        "gdrive": {
            "small": {
                "spark.driver.memory": "4000m",
                "spark.driver.cores": "2",
                "spark.sql.adaptive.enabled": "true",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
            "large": {
                "spark.driver.memory": "17000m",
                "spark.driver.cores": "2",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.maxResultSize": "2g",
                "spark.sql.execution.arrow.enabled": "true",
                "spark.sql.execution.arrow.pyspark.enabled": "true",
            },
        },
    },
    "prod": {
        "cdc": {
            "small": {
                "spark.driver.memory": "2800m",
                "spark.driver.cores": "1",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.executor.instances": "1",
                "spark.sql.adaptive.enabled": "true",
                "spark.kubernetes.executor.request.cores": "900m",
                "spark.executor.cores": "4",
                "spark.executor.memory": "5600m",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "medium": {
                "spark.executor.instances": "5",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.cores": "8",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.driver.memory": "5600m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "240",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "medium_legacy_datetimeRebaseModeInWrite": {
                "spark.executor.instances": "15",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.cores": "8",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.driver.memory": "5600m",
                "spark.driver.cores": "2",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.sql.shuffle.partitions": "240",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "LEGACY",
                "spark.sql.legacy.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large": {
                "spark.executor.instances": "28",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.cores": "10",
                "spark.driver.memory": "5600m",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.driver.cores": "4",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "960",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large_l2": {
                "spark.executor.instances": "56",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.cores": "10",
                "spark.driver.memory": "5600m",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.sql.adaptive.enabled": "true",
                "spark.kubernetes.driver.request.cores": "900m",
                "spark.driver.cores": "4",
                "spark.sql.shuffle.partitions": "960",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large_l3": {
                "spark.executor.instances": "56",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.cores": "10",
                "spark.sql.adaptive.enabled": "true",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.driver.memory": "5600m",
                "spark.kubernetes.driver.request.cores": "1800m",
                "spark.driver.cores": "4",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "recovery": {
                "spark.executor.instances": "300",
                "spark.executor.memory": "3g",
                "spark.executor.memoryOverhead": "1g",
                "spark.sql.adaptive.enabled": "true",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.kubernetes.executor.request.cores": "3",
                "spark.driver.memory": "1g",
                "spark.kubernetes.driver.request.cores": "3",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "recovery_2": {
                "spark.executor.instances": "400",
                "spark.executor.memory": "2g",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.executor.memoryOverhead": "1g",
                "spark.kubernetes.executor.request.cores": "2",
                "spark.driver.memory": "1g",
                "spark.sql.adaptive.enabled": "true",
                "spark.kubernetes.driver.request.cores": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "recovery_events_3": {
                "spark.executor.instances": "60",
                "spark.executor.memory": "50g",
                "spark.executor.memoryOverhead": "1g",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "sparkSqlAdaptiveEnabled": "true",
                "spark.sql.adaptive.enabled": "true",
                "spark.kubernetes.executor.request.cores": "6",
                "spark.driver.memory": "4g",
                "spark.kubernetes.driver.request.cores": "4",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "recovery_orders":{
                "spark.executor.instances": "80",
                "spark.executor.memory": "28200m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.cores": "10",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.memory": "5600m",
                "spark.kubernetes.driver.request.cores": "1800m",
                "spark.driver.cores": "4",
                "spark.sql.shuffle.partitions": "1024",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            }
        },
        "cdc_hive": {
            "small": {"spark.executor.instances": "2", "spark.executor.memory": "4g", "spark.driver.memory": "2g"},
            "medium": {"spark.executor.instances": "3", "spark.executor.memory": "6g", "spark.driver.memory": "4g"},
            "large": {"spark.executor.instances": "5", "spark.executor.memory": "8g", "spark.driver.memory": "6g"},
        },
        "compaction": {
            "medium": {
                "spark.executor.instances": "4",
                "spark.executor.memory": "22000m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.cores": "8",
                "spark.driver.memory": "5500m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "1",
                "spark.sql.legacy.parquet.datetimeRebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.datetimeRebaseModeInWrite": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInRead": "CORRECTED",
                "spark.sql.legacy.parquet.int96RebaseModeInWrite": "CORRECTED",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large": {
                "spark.executor.instances": "36",
                "spark.executor.memory": "22000m",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.cores": "8",
                "spark.driver.memory": "11000m",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.cores": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "larger": {
                "spark.executor.instances": "66",
                "spark.executor.memory": "44000m",
                "spark.executor.cores": "6",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.memory": "22000m",
                "spark.driver.cores": "3",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
        },
        "dwh": {
            "small": {
                "spark.executor.instances": "10",
                "spark.executor.cores": "2",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "32",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "medium": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "2",
                "spark.kubernetes.executor.request.cores": "1800m",
                "spark.executor.memory": "10150m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "160",
                "spark.sql.analyzer.maxIterations": "400",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "320",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large_l2": {
                "spark.executor.instances": "34",
                "spark.executor.cores": "4",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.maxResultSize": "2048m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "480",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large_l3": {
                "spark.executor.instances": "48",
                "spark.executor.cores": "6",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.sql.shuffle.partitions": "800",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large_l4": {
                "spark.executor.instances": "60",
                "spark.executor.cores": "6",
                "spark.kubernetes.executor.request.cores": "3600m",
                "spark.executor.memory": "20300m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "-1",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "large_l5": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "6",
                "spark.executor.memory": "21960m",
                "spark.driver.memory": "8500m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "exclusive": {
                "spark.executor.instances": "40",
                "spark.executor.cores": "6",
                "spark.executor.memory": "22960m",
                "spark.driver.memory": "8500m",
                "spark.sql.broadcastTimeout": "600",
                "spark.kubernetes.driver.request.cores": "2200m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "exclusive_60": {
                "spark.executor.instances": "100",
                "spark.executor.cores": "6",
                "spark.executor.memory": "8960m",
                "spark.driver.memory": "8500m",
                "spark.sql.broadcastTimeout": "600",
                "spark.kubernetes.driver.request.cores": "2200m",
                "spark.kubernetes.executor.request.cores": "2600m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.shuffle.partitions": "1024",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "exclusive_200": {
                "spark.executor.instances": "200",
                "spark.executor.cores": "6",
                "spark.memory.storageFraction": "0.3",
                "spark.executor.memory": "8960m",
                "spark.driver.memory": "8500m",
                "spark.sql.broadcastTimeout": "600",
                "spark.kubernetes.driver.request.cores": "2200m",
                "spark.kubernetes.executor.request.cores": "2200m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "6g",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },
            "exclusive_200_v1": {
                "spark.executor.instances": "200",
                "spark.executor.cores": "6",
                "spark.kubernetes.executor.request.cores": "2600m",
                "spark.executor.memory": "8300m",
                "spark.driver.memory": "6500m",
                "spark.driver.cores": "2",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.shuffle.partitions": "2048",
                "spark.sql.analyzer.maxIterations": "200",
                "spark.sql.autoBroadcastJoinThreshold": "300MB",
                "spark.sql.broadcastTimeout": "600",
                "spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version": "2",
                "spark.sql.adaptive.enabled": "true",
                "spark.sql.shuffle.spill.compress": "true",
                "spark.sql.shuffle.compress": "true",
                "spark.sql.sources.partitionOverwriteMode": "dynamic"
            },

        },
        "gdrive": {
            "small": {
                "spark.driver.memory": "4000m",
                "spark.driver.cores": "2",
                "spark.sql.adaptive.enabled": "true"
            },
            "large": {
                "spark.driver.memory": "34000m",
                "spark.driver.cores": "2",
                "spark.executor.cores": "3",
                "spark.executor.instances": "5",
                "spark.sql.adaptive.enabled": "true",
                "spark.driver.maxResultSize": "3g",
                "spark.sql.execution.arrow.enabled": "true",
                "spark.sql.execution.arrow.pyspark.enabled": "true",
                "spark.executor.memory": "11200m",
                "spark.kubernetes.executor.request.cores": "1800m",
            },
        },
    },
}