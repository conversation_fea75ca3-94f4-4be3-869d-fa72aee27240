import sys

from delta import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import date_format

from data_warehouse.utils import logger
from metadata.appsflyer import APPSFLYER_CONFIG

logger = logger.get_logger(__file__)
NV_CREATED_MONTH = "nv_created_month"
APP_ID = "app_id"


def _get_delta_path(gcs_bucket, entity):
    """
    Returns path at which an Appsflyer entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Appsflyer entity (e.g. users)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/appsflyer/delta/{entity}"


def _get_obj_path(gcs_bucket, entity):
    """
    Returns path at which incremental change data of an Appsflyer entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      Appsflyer entity (e.g. users, transactions)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/appsflyer/objects/{entity}"


def _get_object(spark, path, date_str, event_timestamp_col, src_partition_col):
    """
    Fetches incremental change data of an Appsflyer entity for a specified updated date.

    :param spark:                   Spark session
    :param entity:                  Appsflyer entity
    :param path:                    GCS path at which entity change data is stored
    :param date_str:                Updated date filter for entity change data
    :param event_timestamp_col:     Column used to decide which partition the record belongs to
    :param src_partition_column:    Partition column
    :return:                        Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[src_partition_col] == date_str)
        .drop(src_partition_col)
        .withColumn(NV_CREATED_MONTH, date_format(event_timestamp_col, month_fmt))
    )
    return df


def merge_delta(spark, object_gcs_bucket, delta_gcs_bucket, entity, date_str, event_timestamp_col, src_partition_col):

    obj_path = _get_obj_path(object_gcs_bucket, entity)
    logger.info(f"obj_path: {obj_path}")

    obj_df = _get_object(spark, obj_path, date_str, event_timestamp_col, src_partition_col).distinct()

    if obj_df.count() == 0:
        logger.info("No records to update")
        return

    delta_path = _get_delta_path(delta_gcs_bucket, entity)

    if DeltaTable.isDeltaTable(spark, delta_path):
        delta_table = DeltaTable.forPath(spark, delta_path)
        primary_keys = APPSFLYER_CONFIG[entity].get("primary_keys", [])

        if len(primary_keys) == 0:
            unioned_data = delta_table.toDF().unionByName(obj_df, allowMissingColumns=True)

            # Deduplicate the data
            deduplicated_data = unioned_data.dropDuplicates()

            # Overwrite the existing Delta table with the deduplicated data
            deduplicated_data.write.format("delta").mode("overwrite").save(delta_path)
        else:
            merge_condition = " AND ".join(
                f"existing_data.{col} = new_data.{col}" for col in primary_keys
            )

            delta_table.alias("existing_data").merge(
                obj_df.alias("new_data"),
                merge_condition
            ) \
                .whenMatchedUpdateAll() \
                .whenNotMatchedInsertAll() \
                .execute()

        logger.info("Results are updated")
    else:
        obj_df.write.format("delta").partitionBy(APP_ID, NV_CREATED_MONTH).save(delta_path)
        logger.info(f"Results are save in {delta_path}")


if __name__ == "__main__":
    object_gcs_bucket, delta_gcs_bucket, entity, date_str, event_timestamp_col, src_partition_col = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, object_gcs_bucket, delta_gcs_bucket, entity, date_str, event_timestamp_col, src_partition_col)
    spark.stop()
