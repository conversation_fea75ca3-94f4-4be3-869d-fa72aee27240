---
  kubernetes_context: data-context

  kubernetes_namespace: prod

  airflow:
    version: "{{ versions.airflow }}"
    secret_key: "2h0CNqFIsOzRaKdzrW7e9RXMfYuq0Ges"
    fernet_key: "LjrvXDz1foCN0yt5MYWYXlO71QCQaw07674KsgGjwf4="
    domain: "https://airflow.ninjavan.co"

  airflow_v2:
    version: "{{ versions.airflow_v2 }}"
    secret_key: "2h0CNqFIsOzRaKdzrW7e9RXMfYuq0Ges"
    fernet_key: "LjrvXDz1foCN0yt5MYWYXlO71QCQaw07674KsgGjwf4="
    domain: "https://airflow.ninjavan.co"

  airflow_web:
    req_cpu: 500
    req_mem: 3072

  airflow_v2_web:
    req_cpu: 500
    req_mem: 3072

  airflow_scheduler:
    num_replicas: 2
    req_cpu: 6000
    req_mem: 6000

  airflow_v2_scheduler:
    num_replicas: 2
    req_cpu: 6000
    req_mem: 6000

  airflow_postgres:
    version: 9.6.2
    db_host: ************
    db_schema: airflow
    user: postgres
    password: Ninjitsu89
    pd_size: 350Gi
    req_cpu: 15000
    req_mem: 37500
    storage_class: ssd

  airflow_v2_postgres:
    version: 9.6.2
    db_host: ************
    db_schema: airflow
    user: postgres
    password: Ninjitsu89
    pd_size: 350Gi
    req_cpu: 15000
    req_mem: 37500
    storage_class: ssd

  airflow_postgres_exporter:
    version: v0.5.1

  sql_alchemy_conn: postgresql+psycopg2://{{ airflow_postgres.user }}:{{ airflow_postgres.password }}@{{ airflow_postgres.db_host }}:5432/{{ airflow_postgres.db_schema }}

  postgresql_alchemy_conn: postgresql+psycopg2://{{ airflow_v2_postgres.user }}:{{ airflow_v2_postgres.password }}@{{ airflow_v2_postgres.db_host }}:5432/{{ airflow_v2_postgres.db_schema }}

  git_repo: ssh://*******************:7999/data/airflow.git
