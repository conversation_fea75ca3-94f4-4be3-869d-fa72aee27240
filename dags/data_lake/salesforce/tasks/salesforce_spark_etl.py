from datetime import datetime
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, BooleanType, DoubleType, TimestampType, IntegerType
import logging, requests
import sys, argparse
import pendulum
from google.cloud import storage
from simple_salesforce import Salesforce
from typing import Dict, List

_PRIMITIVE_TYPE_TO_SPARK_TYPE = {
    "xsd:boolean": BooleanType(),
    "xsd:dateTime": TimestampType(),
    "xsd:date": TimestampType(),
    "xsd:double": DoubleType(),
    "xsd:int": IntegerType(),
    "xsd:base64Binary": StringType(),
    "xsd:string": StringType(),
    "xsd:time": StringType(),
    "xsd:anyType": StringType(),
    "tns:ID": StringType(),
}

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def parse_arguments():
    parser = argparse.ArgumentParser(description='Salesforce ETL Spark Job')
    parser.add_argument('--obj_name', required=True, help='Salesforce object name')
    parser.add_argument('--updated_at_field', required=True, help='Last modified field name')
    parser.add_argument('--gcs_bucket', required=True, help='GCS bucket name')
    parser.add_argument('--gcs_path', required=True, help='GCS path prefix')
    parser.add_argument('--sf_username', required=True, help='Salesforce username')
    parser.add_argument('--sf_password', required=True, help='Salesforce password')
    parser.add_argument('--sf_security_token', required=True, help='Salesforce security token')
    parser.add_argument('--sf_domain', required=True, help='Salesforce domain')
    parser.add_argument('--sf_instance_url', required=True, help='Salesforce instance URL')
    parser.add_argument('--sf_version', required=True, help='Salesforce API version')
    parser.add_argument('--execution_date', required=True, help='Airflow execution date')
    parser.add_argument('--next_execution_date', required=True, help='Next execution date')
    
    return parser.parse_args()

class SalesforceSparkETL:
    def __init__(self, spark_session: SparkSession, sf_client: Salesforce):
        self.spark = spark_session
        self.sf_client = sf_client
        self.storage_client = storage.Client()

    def get_spark_schema(self, obj_name: str) -> StructType:
        """Convert Salesforce schema to Spark schema"""
        desc = self.sf_client.__getattr__(obj_name).describe()
        fields = []
        
        logger.info(f"Mapping schema for object: {obj_name}")
        
        for f in desc["fields"]:
            name, sf_type = f["name"], f["soapType"]
            spark_type = _PRIMITIVE_TYPE_TO_SPARK_TYPE.get(sf_type, StringType())
            fields.append(StructField(name, spark_type, True))
            logger.debug(f"Field mapping: {name} ({sf_type} -> {spark_type})")
        
        return StructType(fields)

    def query_salesforce_in_batches(self, obj_name: str, where_clause: str, batch_size: int = 40000) -> List[Dict]:
        records = []
        schema = self.get_spark_schema(obj_name)
        field_names = [field.name for field in schema.fields]
        soql = f"SELECT {','.join(field_names)} FROM {obj_name} WHERE {where_clause}"

        query_more_id = None
        while True:
            if query_more_id:
                results = self.sf_client.query_more(query_more_id, identifier_is_url=True)
            else:
                results = self.sf_client.query(soql)

            records.extend(results["records"])

            if not results["done"]:
                query_more_id = results["nextRecordsUrl"]
            else:
                break

            if len(records) >= batch_size:
                yield records
                records = []

        if records:
            yield records

    def detect_and_convert_timestamps(self, batch: List[Dict], schema: StructType) -> List[Dict]:
        """Detect timestamp-like fields and convert them from string to datetime format"""
        for record in batch:
            for field, value in record.items():
                if isinstance(value, str):
                    try:
                        timestamp = datetime.strptime(value, "%Y-%m-%dT%H:%M:%S.%f%z")
                        record[field] = timestamp
                        for struct_field in schema:
                            if struct_field.name == field and isinstance(struct_field.dataType, StringType):
                                struct_field.dataType = TimestampType()
                    except ValueError:
                        pass
        return batch

    def process_and_save_to_gcs(self, obj_name: str, where_clause: str, partition_path: str, bucket_name: str, output_path: str):
        schema = self.get_spark_schema(obj_name)
        logger.info(f"Generated Spark schema: {schema.simpleString()}")
        
        for batch in self.query_salesforce_in_batches(obj_name, where_clause):
            if not batch:
                continue
                
            logger.info(f"Processing batch of size: {len(batch)}")
            logger.debug(f"Sample raw record before cleaning: {batch[0]}")
            
            cleaned_batch = [{k: v for k, v in record.items() if k != 'attributes'} for record in batch]
            
            logger.debug(f"Sample cleaned record after removing attributes: {cleaned_batch[0]}")
            
            cleaned_batch = self.detect_and_convert_timestamps(cleaned_batch, schema)
            
            try:
                logger.info("Attempting to create DataFrame...")
                df = self.spark.createDataFrame(data=cleaned_batch, schema=schema)
                df.write.format("parquet").save(output_path)
                
                logger.info(f"Saved DataFrame to {output_path}")
            except Exception as e:
                logger.error(f"ERROR creating DataFrame: {str(e)}")
                raise

def main():
    args = parse_arguments()
    
    spark = SparkSession.builder \
        .appName("SalesforceToGCS") \
        .getOrCreate()
    
    logging.info(f"Connecting to Salesforce with domain: {args.sf_domain}")
    logging.info(f"Instance URL: {args.sf_instance_url}")
    logging.info(f"API Version: {args.sf_version}")
    
    try:
        sf_client = Salesforce(
            username=args.sf_username,
            password=args.sf_password,
            security_token="",
            version=args.sf_version,
        )
    except requests.exceptions.InvalidURL as e:
        logging.error(f"Invalid URL error: {str(e)}")
        logging.error(f"Domain: {args.sf_domain}, Instance URL: {args.sf_instance_url}")        
        raise
    except Exception as e:
        logging.error(f"Error connecting to Salesforce: {str(e)}")
        raise

    etl = SalesforceSparkETL(spark, sf_client)
    
    from_dt = pendulum.parse(args.execution_date)
    to_dt = pendulum.parse(args.next_execution_date) 

    from_dt_formatted = from_dt.strftime("%Y-%m-%dT%H:%M:%SZ")
    to_dt_formatted = to_dt.strftime("%Y-%m-%dT%H:%M:%SZ")

    obj_name = args.obj_name
    updated_at_field = args.updated_at_field

    where_clause = f"{updated_at_field} >= {from_dt_formatted} AND {updated_at_field} < {to_dt_formatted}"
    
    bucket_name = args.gcs_bucket
    obj_root = f"{args.gcs_path}/{obj_name.lower()}/"
    partition_path = f"{obj_root}nv_updated_date={from_dt.date()}/"
    
    etl.process_and_save_to_gcs(
        obj_name=obj_name,
        where_clause=where_clause,
        partition_path=partition_path,
        bucket_name=bucket_name,
        output_path=partition_path
    )

    spark.stop()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    try:
        main()
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}")
        raise
