import sys

import pandas as pd
from pyspark.sql import SparkSession

from data_warehouse.tasks.main import base
from metadata import constants, data_warehouse, versioned_parquet_tables_masked

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.FieldSalesDAG.Task.FIELD_SALES_SHORT_TERM_RETENTION_MASKED + ".py",
    task_name=data_warehouse.FieldSalesDAG.Task.FIELD_SALES_SHORT_TERM_RETENTION_MASKED,
    depends_on=(data_warehouse.FieldSalesDAG.Task.FIELD_SALES_SHORT_TERM_RETENTION_BASE_MASKED,),
    system_ids=(
        constants.SystemID.ID,
        constants.SystemID.MY,
        constants.SystemID.PH,
        constants.SystemID.VN,
    ),
)


PICKLE_PATH = "gs://nv-data-{env}-data-warehouse/pickles/field_sales_short_term_retention/effective_date=2022-01-01"


def get_task_config(env, system_id, last_measurement_datetime, measurement_datetime, enable_full_run=False):
    lookback_ranges = base.get_look_back_ranges(last_measurement_datetime, measurement_datetime, 2, 0)
    if enable_full_run:
        lookback_ranges = base.LookBackRange(None, None)

    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    # input_env = "prod"
    is_masked = True
    input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).FIELD_SALES_SHORT_TERM_RETENTION_BASE,
                view_name="field_sales_short_term_retention_base",
                input_range=lookback_ranges.input,
                system_id=system_id,
            ),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="final",
                jinja_template="""
                select
                    sf_parent_acc_id_coalesce
                    , m0_start_date as cohort_month
                    , shipper_size as shipper_size_cat
                    , case
                        when shipper_size = 'small' then 1
                        when shipper_size = 'medium' then 2
                        when shipper_size = 'large' then 3
                        end as shipper_size
                    , cast(m0_m1_avg_ppd as float) as m0_m1_avg_ppd
                    , cast(ppd_change_rate as float) as m0_m1_ppd_change_rate
                    , m1_category
                    , m0_m1_sla_rate
                    , coalesce(m0_m1_rts_rate_overall, 0) as m0_m1_rts_rate_overall
                    , coalesce(m0_m1_rts_rate_nv_liable, 0) as m0_m1_rts_rate_nv_liable
                    , coalesce(m0_m1_rts_rate_external_liable, 0) as m0_m1_rts_rate_external_liable
                    , cast(coalesce(m0_m1_no_of_discount, 0) as float) as m0_m1_no_of_discount
                    , cast(coalesce(m0_m1_max_discount, 0) as float) / 100 as m0_m1_max_discount
                    , cast(coalesce(m0_m1_avg_discount, 0) as float) / 100 as m0_m1_avg_discount
                    , m0_m1_avg_rts_spd
                    , m0_m1_ontime_pickup_rate
                    , m0_m1_early_pickup_rate
                    , m0_m1_late_pickup_rate
                    , m0_m1_missing_rate
                    , m0_m1_damaged_rate
                    , m0_m1_cod_rts_rate
                    , m0_m1_cod_rate
                    , m0_m1_alive_prob_avg
                    , m0_m1_alive_prob_max
                    , m0_m1_alive_prob_min
                    , m0_m1_case_sla_adherence_rate
                    , m0_m1_avg_hour_to_resolution
                    , m0_m1_avg_hour_to_ops_response
                    , '{{ system_id }}' as system_id
                from field_sales_short_term_retention_base
                where
                    ppd_change_rate is not null
                    and m0_m1_avg_ppd > 1
                """,
                jinja_arguments={
                    "system_id": system_id,
                },
            ),
        ),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).FIELD_SALES_SHORT_TERM_RETENTION,
        measurement_datetime=measurement_datetime,
        system_id=system_id,
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config, env):
    base.load_data(spark, config.input)
    df = base.transform_data(spark, config.transform).toPandas()
    df["churn_pred"] = pd.Series(_load_model(env).predict(_transform_input(df)))
    results = df[["system_id", "sf_parent_acc_id_coalesce", "cohort_month", "churn_pred"]]
    results = spark.createDataFrame(results)
    base.write_data(results, config.output)
    return results


def _transform_input(df):
    df_model = df.drop(["sf_parent_acc_id_coalesce", "cohort_month"], axis=1)
    x_cont = df_model.drop(["shipper_size_cat", "system_id", "m1_category"], axis=1)
    x_cat_ordered = df_model[["shipper_size_cat", "system_id", "m1_category"]]
    df_lgbm = pd.concat([x_cont, x_cat_ordered], axis=1)
    for col in ["shipper_size_cat", "system_id", "m1_category"]:
        df_lgbm[col] = df_lgbm[col].astype("category")
    return df_lgbm


def _load_model(env):
    model_path = f"{PICKLE_PATH.format(env=env)}/lgbm_short_term.pkl"
    trained_model = pd.read_pickle(model_path)
    return trained_model


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(
        input_args.env,
        input_args.system_id,
        input_args.last_measurement_datetime,
        input_args.measurement_datetime,
        input_args.enable_full_run,
    )

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config, input_args.env)
    spark.stop()
