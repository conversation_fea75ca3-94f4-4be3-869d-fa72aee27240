import ast
import logging
from tempfile import NamedTemporaryFile

import pandas as pd
from airflow.models import Base<PERSON>perator, SkipMixin
from airflow.providers.google.cloud.hooks.gcs import GCSHook

from gdrive.hooks.gdrive_hook import GoogleDriveHook


class GoogleDriveToGcsOperator(BaseOperator, SkipMixin):
    """
    Google Drive to Google Cloud Storage (GCS) Operator

    Fetches file from Google Drive and stores it in GCS in parquet format. Data will be stored at the path
    {gcs_bucket}/{gcs_folder_path}/{context["ts_nodash"]}.snappy.parquet

    :param gdrive_file:         Google Drive file object (with "id" and "name" keys)
    :type gdrive_file:          dict
    :param gcs_bucket:          GCS bucket to upload file to
    :type gcs_bucket:           string
    :param gcs_folder_path:     GCS folder path
    :type gcs_folder_path:      string
    :param gdrive_schema_file:  Google Drive schema file for file to be uploaded
    :type gdrive_schema_file:   dict
    :param gdrive_conn_id:      Google Drive connection ID
    :type gdrive_conn_id:       string
    :param gcs_conn_id:         GCS connection ID
    :type gcs_conn_id:          string
    """

    template_fields = ["gdrive_file", "gdrive_schema_file"]

    def __init__(
        self,
        gdrive_file,
        gcs_bucket,
        gcs_folder_path,
        gdrive_schema_file=None,
        gdrive_conn_id="google_cloud_default",
        gcs_conn_id="google_cloud_default",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.gdrive_file = gdrive_file
        self.gcs_bucket = gcs_bucket
        self.gcs_folder_path = gcs_folder_path
        self.gdrive_schema_file = gdrive_schema_file
        self.gdrive_conn_id = gdrive_conn_id
        self.gcs_conn_id = gcs_conn_id

    def execute(self, context):
        """
        Fetches specified Google drive file and uploads to GCS. Skips all downstream tasks if no such file is found.

        :param context: Task context
        """
        if isinstance(self.gdrive_file, str):
            self.gdrive_file = ast.literal_eval(self.gdrive_file)
        if not self.gdrive_file or not {"id", "name"} <= self.gdrive_file.keys():
            logging.info("No file specified")
            self._skip_downstream_tasks(context)
            return

        file_id = self.gdrive_file["id"]
        file_name = self.gdrive_file["name"]
        gdrive_hook = GoogleDriveHook(conn_id=self.gdrive_conn_id)
        logging.info(f"Downloading {file_name}")
        file = gdrive_hook.download_file(file_id)

        # Get schema if specified
        schema = None
        if isinstance(self.gdrive_schema_file, str):
            self.gdrive_schema_file = ast.literal_eval(self.gdrive_schema_file)
        if self.gdrive_schema_file:
            schema_file_id = self.gdrive_schema_file["id"]
            schema_file_name = self.gdrive_schema_file["name"]
            logging.info("Downloading schema file")
            schema_file = gdrive_hook.download_file(schema_file_id)
            schema_df = self._file_to_df(schema_file, schema_file_name)
            schema = {row["field"]: row["type"] for row in schema_df.to_dict(orient="records")}

        df = self._file_to_df(file, file_name, schema)

        dirname = f"{self.gcs_folder_path}"
        filename = f"{context['ts_nodash']}.snappy.parquet"
        file_path = f"{dirname}/{filename}"
        logging.info(f"Uploading to GCS bucket: {self.gcs_bucket}, path: {file_path}")
        gcs_hook = GCSHook(gcp_conn_id=self.gcs_conn_id)
        with NamedTemporaryFile("w") as tmp:
            df.to_parquet(tmp.name, compression="snappy", engine="pyarrow", index=False)
            gcs_hook.upload(self.gcs_bucket, file_path, tmp.name)
        logging.info("Uploaded data to GCS")

    def _skip_downstream_tasks(self, context):
        downstream_tasks = context["task"].get_flat_relatives(upstream=False)
        if downstream_tasks:
            logging.info("Skipping downstream tasks...")
            logging.debug(f"Downstream task_ids {downstream_tasks}")
            self.skip(context["dag_run"], context["ti"].execution_date, downstream_tasks)

    @staticmethod
    def _split_date_and_non_date_schema(schema):
        date_dtypes = ["date", "datetime", "datetime64"]
        date_schema, non_date_schema = {}, {}
        for column, dtype in schema.items():
            if dtype in date_dtypes:
                date_schema[column] = dtype
            else:
                non_date_schema[column] = dtype
        return date_schema, non_date_schema

    @staticmethod
    def _file_to_df(file, file_name, schema=None):
        """
        Reads a file as a Pandas dataframe

        :param file:        File handle
        :param file_name:   File name (to determine Pandas method for reading file)
        :param schema:      Dict of column -> type specifying data type for columns.
        :return:            File contents as a Pandas dataframe
        """
        non_date_schema, date_schema = {}, {}
        if schema:
            # This is necessary as Pandas does not support parsing of datetime dtypes in read_csv's dtype argument.
            # It needs to be separately passed in the parse_dates argument.
            date_schema, non_date_schema = GoogleDriveToGcsOperator._split_date_and_non_date_schema(schema)
        date_columns = list(date_schema)

        if file_name.endswith(".csv"):
            # reset to start of stream
            if not isinstance(file, str):
                file.seek(0)
            df = pd.read_csv(file, dtype=non_date_schema, parse_dates=date_columns)
        elif file_name.endswith((".xls", ".xlsx")):
            df = pd.read_excel(file, dtype=non_date_schema, parse_dates=date_columns)
        else:
            raise ValueError(f"Invalid file type: {file_name}")

        # Cast date columns to date type if applicable.
        # This is required if not date columns will end up as datetime in the output parquet file.
        for column, dtype in date_schema.items():
            if dtype == "date":
                df[column] = df[column].dt.date
        return df
