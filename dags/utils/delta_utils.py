import logging, json
from google.cloud import storage
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.api.common.experimental import get_task_instance
from common.utils.gcs import get_nv_data_bucket_uri

def check_and_extract_delta_metrics(env, schema, table, delta_task_id, is_pii, **context):
    """Checks if the upstream Delta merge task successfully committed a new Delta version and extracts delta metrics."""
    hook = GCSHook()
    client = storage.Client()
    gs_bucket_path_db = get_nv_data_bucket_uri(env, bucket_type="db", schema="legacy", strip=True)
    if is_pii:
        gs_bucket_path_db = get_nv_data_bucket_uri(env, bucket_type="db", schema=schema, strip=True)
    gs_bucket, base_path = gs_bucket_path_db.split("/")
    prefix = f"{base_path}/{schema}/{table}/_delta_log"
    logging.info(f"Checking in '{gs_bucket}/{prefix}'")
    blobs = list(hook.list(bucket_name=gs_bucket, prefix=prefix, delimiter=".json"))
    if not blobs:
        raise Exception(f"No json logs found in '{gs_bucket}/{prefix}'.")
    last_delta_log = max(blobs)
    logging.info(f"Last delta log: {last_delta_log}")
    last_delta_log_update = hook.get_blob_update_time(gs_bucket, last_delta_log)

    delta_ti = get_task_instance.get_task_instance(context["dag"].dag_id, delta_task_id, context["execution_date"])
    delta_start_date = delta_ti.start_date

    if last_delta_log_update < delta_start_date:
        raise Exception("Delta merge not found.")

    bucket = client.bucket(gs_bucket)
    latest_log = bucket.blob(last_delta_log)
    latest_log_content = latest_log.download_as_string().decode('utf-8')
    latest_log_data = [json.loads(line) for line in latest_log_content.strip().split('\n')]
    latest_version = latest_log.name.split('/')[-1].split('.')[0]
    latest_commit = next((entry for entry in latest_log_data if "commitInfo" in entry), None)

    if latest_commit and "commitInfo" in latest_commit:
        operation_metrics = latest_commit["commitInfo"].get("operationMetrics", {})
    else:
        logging.error(f"No commitInfo found in the latest log: {latest_log.name}")
        operation_metrics = {}

    metrics_dict = {
        "version": latest_version,
        "numTargetRowsCopied": int(operation_metrics.get("numTargetRowsCopied", 0)),
        "numTargetRowsDeleted": int(operation_metrics.get("numTargetRowsDeleted", 0)),
        "numTargetFilesAdded": int(operation_metrics.get("numTargetFilesAdded", 0)),
        "numTargetRowsInserted": int(operation_metrics.get("numTargetRowsInserted", 0)),
        "numTargetRowsUpdated": int(operation_metrics.get("numTargetRowsUpdated", 0)),
        "numSourceRows": int(operation_metrics.get("numSourceRows", 0)),
        "numOutputRows": int(operation_metrics.get("numOutputRows", 0)),
        "numTargetFilesRemoved": int(operation_metrics.get("numTargetFilesRemoved", 0)),
        "schema": schema,
        "table": table,
        "execution_date": str(context["execution_date"]),
    }
    context['ti'].xcom_push(key=f'delta_metrics_{schema}_{table}', value=metrics_dict)
    return metrics_dict

def clear_delta_callback(delta_task, context):
    # Temporarily disable alerts when clear delta task fails.
    # notif.chat.send_ti_failure_alert(context)
    delta_task.clear(start_date=context["execution_date"], end_date=context["execution_date"], downstream=True)