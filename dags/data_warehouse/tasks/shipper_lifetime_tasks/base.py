import re
from collections import namedtuple
from tempfile import NamedTemporaryFile
from typing import Tuple, Type, Union

import numpy as np
import pandas as pd
import pendulum
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from lifetimes import GammaGammaFitter, ModifiedBetaGeoFitter
from lifetimes.utils import ConvergenceError  # noqa

from common import stringcase
from common.date import to_measurement_datetime_str
from data_warehouse.utils import logger
from data_warehouse.utils.gcs import get_uri_bucket_and_directory

logger = logger.get_logger(__file__)

PICKLE_EFFECTIVE_DATE_REGEX = "20[0-9]{2}-[0-9]{2}-[0-9]{2}"
PICKLE_EFFECTIVE_DATE_FORMAT = "%Y-%m-%d"
TaskConfig = namedtuple(
    "TaskConfig",
    (
        "system_id",
        "gcp_conn_id",
        "input_file_path",
        "pickle_bucket",
        "pickle_directory",
        "execution_date",
        "model_types",
        "output_file_path",
    ),
)


def run(config) -> None:
    gcs_hook = GCSHook(gcp_conn_id=config.gcp_conn_id)

    df = _load_base_data(gcs_hook, config.input_file_path, config.execution_date, config.system_id)
    if 'shipper_id' in df.columns:
        df['shipper_id'].fillna(value=0, inplace=True)
        df['shipper_id'] = df['shipper_id'].astype('int64')
    pickle_path = _get_relevant_pickle_path(
        gcs_hook, config.pickle_bucket, config.pickle_directory, df["report_date"][0]
    )
    logger.info(f"Pickle path: {pickle_path}.")
    df_final = _transform_data(gcs_hook, pickle_path, config.system_id, df, config.model_types)

    _write_data(df_final, config.system_id, config.output_file_path, config.execution_date)
    logger.info("All done.")


def _load_base_data(
    gcs_hook: GCSHook, input_table: str, next_execution_date: pendulum.DateTime, system_id: str
) -> pd.DataFrame:
    base_path = _get_dwh_table_path(input_table, next_execution_date, system_id)
    logger.info(f"Loading base table from {base_path}.")
    bucket, directory = get_uri_bucket_and_directory(base_path)
    parquet_file_directories = gcs_hook.list(bucket_name=bucket, prefix=directory, delimiter=".parquet")

    dfs = []
    for file_directory in parquet_file_directories:
        dfs.append(pd.read_parquet(f"gs://{bucket}/{file_directory}"))
    return pd.concat(dfs, ignore_index=True)


def _get_relevant_pickle_path(
    gcs_hook: GCSHook, pickle_bucket: str, pickle_dir: str, report_date: pendulum.DateTime
) -> str:
    pickle_version_directories = gcs_hook.list(bucket_name=pickle_bucket, prefix=pickle_dir, delimiter="/")
    pickle_versions = {
        re.findall(PICKLE_EFFECTIVE_DATE_REGEX, directory)[0] for directory in pickle_version_directories
    }

    pickle_version_cutoff = report_date.strftime(PICKLE_EFFECTIVE_DATE_FORMAT)
    latest_pickle_version = "0000-00-00"
    for version in pickle_versions:
        if latest_pickle_version < version <= pickle_version_cutoff:
            latest_pickle_version = version

    if latest_pickle_version == "0000-00-00":
        raise ValueError(f"No relevant pickle path for report_date={report_date}.")

    return f"gs://{pickle_bucket}/{pickle_dir}={latest_pickle_version}"


def _get_fitter(
    gcs_hook: GCSHook,
    pickle_path: str,
    system_id: str,
    model_type: str,
    fitter: Union[Type[GammaGammaFitter], Type[ModifiedBetaGeoFitter]],
) -> Union[GammaGammaFitter, ModifiedBetaGeoFitter]:
    fitter_type = stringcase.snake_case(fitter.__name__)
    pickle_path = f"{pickle_path}/{fitter_type}_{model_type}"

    penalizer_coef_path = f"{pickle_path}/penalizer_coef.pkl"
    penalizer_coef = pd.read_pickle(penalizer_coef_path).at[system_id, "penalizer_coef"]
    fitter_obj = fitter(penalizer_coef)

    model_path = f"{pickle_path}/{system_id}_model.pkl"
    model_bucket, model_directory = get_uri_bucket_and_directory(model_path)
    with NamedTemporaryFile("w") as tmp:
        model_pickle = gcs_hook.download(object_name=model_directory, bucket_name=model_bucket, filename=tmp.name)
        fitter_obj.load_model(model_pickle)
    return fitter_obj


def _transform_data(
    gcs_hook: GCSHook, pickle_path: str, system_id: str, df: pd.DataFrame, model_types: Tuple
) -> pd.DataFrame:
    gg_fitter = _get_fitter(gcs_hook, pickle_path, system_id, "default", fitter=GammaGammaFitter)
    for model_type in model_types:
        logger.info(f"Fitting data with '{model_type}' model.")
        mbg_fitter = _get_fitter(gcs_hook, pickle_path, system_id, model_type, fitter=ModifiedBetaGeoFitter)
        try:
            df["alive_probability"] = mbg_fitter.conditional_probability_alive(
                df["frequency"], df["recency"], df["age"]
            )
            df["lifetime_value"] = gg_fitter.customer_lifetime_value(
                mbg_fitter,
                df["frequency"],
                df["recency"],
                df["age"],
                df["monetary_value"],
                time=12,
                freq="D",
                discount_rate=0,
            )
        except ConvergenceError:
            logger.info("Model failed to converge. Attempting next model.")
            pass
        else:
            df["alive_probability"] = df["alive_probability"].round(2)
            df["alive_probability_model"] = model_type
            df.loc[(df["lifetime_value"] < 0) | (df["lifetime_value"] == np.inf), "lifetime_value"] = None
            df["lifetime_value"] = df["lifetime_value"].round(0).astype("Int64")
            return df
    raise ConvergenceError("All models failed to converge.")


def _write_data(df: pd.DataFrame, system_id: str, output_table: str, next_execution_date: pendulum.DateTime):
    base_path = _get_dwh_table_path(output_table, next_execution_date, system_id)
    file_path = f"{base_path}/1.snappy.parquet"
    logger.info(f"Writing output to {file_path}.")
    df.to_parquet(file_path, compression="snappy", index=False)


def _get_dwh_table_path(base_path: str, next_execution_date: pendulum.DateTime, system_id: str):
    measurement_datetime = to_measurement_datetime_str(next_execution_date)
    return f"{base_path}/measurement_datetime={measurement_datetime}/system_id={system_id}"
