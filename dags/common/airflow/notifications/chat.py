import json
import logging
from urllib.parse import parse_qsl

import requests
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator


def get_connection_details(conn_id):
    connection_object = BaseHook.get_connection(conn_id)
    connection = connection_object.extra_dejson
    connection['host'] = connection_object.host
    return connection

def _request(message, connection):
    conn = BaseHook.get_connection(connection)
    # Assemble webhook URL from connection info.
    url = "&".join(["=".join(_) for _ in parse_qsl(conn.get_uri())]).replace("http://", "")
    logging.info("Sending notification to Google Chat")
    requests.post(
        url=url,
        data=json.dumps({"text": message}),
        headers={"Content-Type": "application/json"},
    )


def _gchat(message, connection="google_chat"):
    """
    Returns an operator to post messages to a Google Chat space using a preconfigured incoming webhook
    :param message:         Message to send
    :type message:          string
    """
    return PythonOperator(
        task_id="google_chat_alert",
        python_callable=_request,
        op_kwargs={"message": message, "connection": connection},
    )


def send_ti_failure_alert(context):
    """
    Sends a Google Chat message for task instance failure.
    For use as operator's on_failure_callback function.

    :param context:         Contains references to Airflow execution context
    :type context:          dict
    :param connection:      Airflow connection
    :type connection:       str
    """
    ti = context.get("task_instance")
    tags = context["dag"].params.get("tags", [])

    layer = "DWH" if "data_warehouse" in tags else "CDC"
    ti_failed_message = f"""
        ❌  Task instance failed.
            *DAG*: {ti.dag_id}
            *Task*: {ti.task_id}
            *Execution Date*: {context.get("execution_date")}
            *Duration*: {ti.duration:.1f} seconds
            *Log URL*: {ti.log_url}
            *Retry*:{ti.try_number}
            *Max Tries*:{ti.max_tries}
         """
    alert_channel = context["params"].get("alert_channel")
    if alert_channel:
        connection = alert_channel
    else:
        connection = "google_chat"
    _gchat(ti_failed_message, connection).execute(context)


def send_dag_run_failure_alert(context):
    """
    Sends a Google Chat message for a DagRun failure.
    For use as DAG's on_failure_callback function.

    :param context:         Contains references to Airflow execution context
    :type context:          dict
    """
    dag_run = context.get("dag_run")
    webserver_base_url = context.get("conf").get("webserver", "base_url")
    dag_run_failed_message = f"""
        DAG Run failed.
            *DAG Run*: {dag_run}
            *Reason*: {context.get("reason")}
            *URL*: {webserver_base_url}/tree?dag_id={dag_run.dag_id}
        """
    _gchat(dag_run_failed_message).execute(context)


def send_ti_success_alert(context):
    """
    Sends a Google Chat message for task instance success.
    For use as operator's on_success_callback function.

    :param context:         Contains references to Airflow execution context
    :type context:          dict
    :param connection:      Airflow connection
    :type connection:          str
    """
    ti = context.get("task_instance")
    ti_success_message = f"""
        ✅ [CDC] Task instance success.
            *DAG*: {ti.dag_id}
            *Task*: {ti.task_id}
            *Execution Date*: {context.get("execution_date")}
            *Duration*: {ti.duration:.1f} seconds
            *Log URL*: {ti.log_url}
         """
    connection = "google_chat"
    _gchat(ti_success_message, connection).execute(context)


def send_dag_run_sla_miss_alert(dag, task_list, blocking_task_list, slas, blocking_tis):
    """
    Sends a Google Chat message for any missed SLAs.
    For use as DAG object's sla_miss_callback function.
    See for details:
    https://github.com/apache/airflow/blob/457d3fe22b8def270bcbb73c85e38f22fca33c48/airflow/jobs/scheduler_job.py#L448
    """
    context = {
        "dag": dag,
        "task_list": task_list,
        "blocking_task_list": blocking_task_list,
        "slas": slas,
        "blocking_tis": blocking_tis,
    }

    dag_run_sla_miss_message = f"""
        :airflow: *SLA miss on DAG {dag.dag_id}*
        Tasks that missed their SLA: ```{task_list}```
        Blocking tasks: ```{blocking_task_list}```

        Note that SLA miss also applies to tasks that are rerun after they have succeeded.
        """
    _gchat(dag_run_sla_miss_message).execute(context)
