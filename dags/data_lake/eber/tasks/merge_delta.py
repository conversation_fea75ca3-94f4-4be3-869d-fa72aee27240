import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, row_number, from_unixtime, col, lit

from common.spark import pii
from metadata.constants import LegacyGcsBucketUris
from metadata.eber import EBER_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

SYSTEM_ID = "system_id"
ID = "id"
CREATED_AT = "created_at"
OBJECT_BUCKET = LegacyGcsBucketUris.raw


def get_delta_path(gcs_bucket, entity):
    """
    Returns path at which an Eber entity delta table is stored.

    :param gcs_bucket:  OBS bucket
    :param entity:      Eber entity (e.g. users)
    :return:            OBS path
    """
    return f"gs://{gcs_bucket}/eber/delta/{entity}"


def get_obj_path(env, entity):
    """
    Returns path at which incremental change data of an Eber entity is stored.

    :param gcs_bucket:  OBS bucket
    :param entity:      Eber entity (e.g. users, transactions)
    :return:            OBS path
    """
    return f"{OBJECT_BUCKET.format(env)}/eber/objects/{entity}"


def read_and_union_partitions(spark, base_path):
    """
    Read individual partitions and union them with consistent schema.
    This is a fallback when automatic schema merging fails.

    :param spark:       Spark session
    :param base_path:   Base path containing partitioned data
    :return:            Unified DataFrame
    """


    print(f"Attempting to read individual partitions from {base_path}")

    # Get list of partition directories
    try:
        # Use Hadoop FileSystem to list directories
        hadoop_conf = spark._jsc.hadoopConfiguration()
        fs = spark._jvm.org.apache.hadoop.fs.FileSystem.get(hadoop_conf)
        path_obj = spark._jvm.org.apache.hadoop.fs.Path(base_path)

        if not fs.exists(path_obj):
            raise Exception(f"Path does not exist: {base_path}")

        # List all subdirectories (partitions)
        file_statuses = fs.listStatus(path_obj)
        partition_paths = []

        for file_status in file_statuses:
            if file_status.isDirectory():
                partition_path = str(file_status.getPath())
                partition_paths.append(partition_path)

        print(f"Found {len(partition_paths)} partition directories")

        if not partition_paths:
            raise Exception("No partition directories found")

        # Read each partition individually and collect schemas
        dataframes = []
        schemas = []

        for partition_path in partition_paths[:5]:  # Limit to first 5 partitions for schema analysis
            try:
                print(f"Reading partition: {partition_path}")
                partition_df = spark.read.option("mergeSchema", "false") \
                    .option("timeZone", "UTC") \
                    .parquet(partition_path)

                dataframes.append(partition_df)
                schemas.append(partition_df.schema)
                print(f"Partition schema: {partition_df.schema}")

            except Exception as e:
                print(f"Failed to read partition {partition_path}: {str(e)}")
                continue

        if not dataframes:
            raise Exception("No partitions could be read successfully")

        # Use the first successful schema as the target schema
        target_schema = schemas[0]
        print(f"Using target schema: {target_schema}")

        # Now read all partitions and convert to target schema
        all_dataframes = []
        for partition_path in partition_paths:
            try:
                partition_df = spark.read.option("mergeSchema", "false") \
                    .option("timeZone", "UTC") \
                    .parquet(partition_path)

                # Convert schema to match target
                converted_df = convert_to_target_schema(partition_df, target_schema)
                all_dataframes.append(converted_df)

            except Exception as e:
                print(f"Warning: Failed to read partition {partition_path}: {str(e)}")
                continue

        if not all_dataframes:
            raise Exception("No partitions could be successfully converted")

        # Union all dataframes
        result_df = all_dataframes[0]
        for df in all_dataframes[1:]:
            result_df = result_df.union(df)

        print(f"Successfully unioned {len(all_dataframes)} partitions")
        return result_df

    except Exception as e:
        print(f"Error in read_and_union_partitions: {str(e)}")
        raise


def convert_to_target_schema(df, target_schema):
    """
    Convert a DataFrame to match the target schema, handling type conversions.

    :param df:              Source DataFrame
    :param target_schema:   Target schema to convert to
    :return:                Converted DataFrame
    """
    from pyspark.sql.functions import col, lit, from_unixtime
    from pyspark.sql.types import TimestampType, LongType

    converted_df = df

    for target_field in target_schema.fields:
        field_name = target_field.name
        target_type = target_field.dataType

        if field_name in df.columns:
            source_field = df.schema[field_name]
            source_type = source_field.dataType

            # If types don't match, convert
            if source_type != target_type:
                print(f"Converting {field_name} from {source_type} to {target_type}")

                if isinstance(target_type, TimestampType) and isinstance(source_type, LongType):
                    # Convert long to timestamp
                    converted_df = converted_df.withColumn(field_name,
                                                         from_unixtime(col(field_name)))
                elif isinstance(target_type, LongType) and isinstance(source_type, TimestampType):
                    # Convert timestamp to long (unix timestamp)
                    converted_df = converted_df.withColumn(field_name,
                                                         col(field_name).cast("long"))
                else:
                    # Generic cast
                    converted_df = converted_df.withColumn(field_name,
                                                         col(field_name).cast(target_type))
        else:
            # Add missing column with null values
            print(f"Adding missing column {field_name} with null values")
            converted_df = converted_df.withColumn(field_name, lit(None).cast(target_type))

    # Remove any extra columns not in target schema
    target_columns = [field.name for field in target_schema.fields]
    converted_df = converted_df.select(*target_columns)

    return converted_df


def get_object(spark, entity, path, date_str, pii_fields):
    """
    Fetches incremental change data of an Eber entity for a specified updated date.

    :param spark:       Spark session
    :param entity:      Eber entity
    :param path:        OBS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :param pii_fields:  PII fields to be masked
    :return:            Spark dataframe of entity change data
    """
    month_fmt = "yyyy-MM"
    
    # Set comprehensive Spark configurations to handle timestamp schema mismatches and timestamp_ntz issues
    spark.conf.set("spark.sql.parquet.int96RebaseModeInRead", "LEGACY")
    spark.conf.set("spark.sql.parquet.enableVectorizedReader", "false")
    spark.conf.set("spark.sql.legacy.parquet.nanosAsLong", "true")
    spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")
    spark.conf.set("spark.sql.parquet.datetimeRebaseModeInRead", "LEGACY")
    spark.conf.set("spark.sql.parquet.int96TimestampConversion", "true")
    spark.conf.set("spark.sql.ansi.enabled", "false")
    spark.conf.set("spark.sql.files.ignoreCorruptFiles", "true")
    spark.conf.set("spark.sql.files.ignoreMissingFiles", "true")
    # Additional configurations for schema merging
    spark.conf.set("spark.sql.parquet.mergeSchema", "true")
    spark.conf.set("spark.sql.parquet.filterPushdown", "false")
    spark.conf.set("spark.sql.adaptive.enabled", "false")
    
    # Try different approaches to read the data without automatic schema merging
    # We'll handle schema unification manually after reading
    max_attempts = 3
    attempt = 1
    last_exception = None

    while attempt <= max_attempts:
        try:
            if attempt == 1:
                # First attempt: Read without schema merging to avoid conflicts
                print(f"Attempt {attempt}/{max_attempts}: Reading parquet without schema merging...")
                # Read specific date partition to avoid reading all data
                date_partition_path = f"{path}/{SRC_PARTITION_COLUMN}={date_str}"
                print(f"Attempting to read specific date partition: {date_partition_path}")

                try:
                    # Try to read the specific date partition first across all system_ids
                    # Use a wildcard pattern to read from all system_ids for the specific date
                    date_wildcard_path = f"{path}/system_id=*/{SRC_PARTITION_COLUMN}={date_str}"
                    print(f"Attempting to read date partition with wildcard: {date_wildcard_path}")

                    df = spark.read.option("timeZone", "UTC") \
                        .option("mergeSchema", "false") \
                        .option("basePath", path) \
                        .parquet(date_wildcard_path)
                    print(f"Successfully read date partition with wildcard: {date_wildcard_path}")
                except Exception as partition_e:
                    print(f"Failed to read specific date partition with wildcard: {str(partition_e)}")
                    print("Falling back to reading entire path with recursive lookup...")
                    # Fallback to reading entire path
                    df = spark.read.option("timeZone", "UTC") \
                        .option("mergeSchema", "false") \
                        .option("basePath", path) \
                        .option("recursiveFileLookup", "true") \
                        .parquet(path)
            elif attempt == 2:
                # Second attempt: Disable vectorized reader and use permissive mode
                print(f"Attempt {attempt}/{max_attempts}: Reading with vectorized reader disabled...")
                df = spark.read.option("mergeSchema", "false") \
                    .option("timeZone", "UTC") \
                    .option("vectorizedReader", "false") \
                    .option("basePath", path) \
                    .option("recursiveFileLookup", "true") \
                    .option("ignoreCorruptFiles", "true") \
                    .parquet(path)
            else:
                # Third attempt: Use most permissive settings
                print(f"Attempt {attempt}/{max_attempts}: Reading with most permissive settings...")
                df = spark.read.option("mode", "PERMISSIVE") \
                    .option("timeZone", "UTC") \
                    .option("mergeSchema", "false") \
                    .option("inferSchema", "false") \
                    .option("vectorizedReader", "false") \
                    .option("basePath", path) \
                    .option("recursiveFileLookup", "true") \
                    .option("pathGlobFilter", "*.parquet") \
                    .option("ignoreCorruptFiles", "true") \
                    .parquet(path)

            # If we reach here, read was successful
            print(f"Successfully read parquet data from {path}")
            print(f"Schema: {df.schema}")
            print(f"Columns: {df.columns}")
            break

        except Exception as e:
            last_exception = e
            print(f"Attempt {attempt}/{max_attempts} failed: {str(e)}")

            # If this is a schema merge error, try reading individual partitions
            if "Failed to merge" in str(e) and "incompatible data types" in str(e):
                print("Schema merge error detected. Trying to read and union individual partitions...")
                try:
                    df = read_and_union_partitions(spark, path)
                    print("Successfully read data using partition union approach")
                    break
                except Exception as union_e:
                    print(f"Partition union approach also failed: {str(union_e)}")

            attempt += 1

    # If all attempts failed, raise the last exception
    if attempt > max_attempts:
        print(f"All {max_attempts} attempts to read parquet data failed")
        raise last_exception
    
    # Check if dataframe is empty
    if df.rdd.isEmpty():
        print(f"Warning: Dataframe is empty for path {path}")
        # Return empty dataframe with expected schema
        return spark.createDataFrame([], df.schema)

    # Add debugging information
    total_rows_before_filtering = df.count()
    print(f"Total rows read before filtering: {total_rows_before_filtering}")

    # Show sample data for debugging
    print("Sample data (first 5 rows):")
    df.show(5, truncate=False)

    # Check if system_id column exists, if not, try to add it
    if SYSTEM_ID not in df.columns:
        print(f"Warning: {SYSTEM_ID} column not found in data. Attempting to extract from path or add default value...")

        # Try to extract system_id from the path structure
        # Path structure should be like: .../eber/objects/member_transactions/system_id=XX/nv_updated_date=YYYY-MM-DD/
        try:
            # Create a temporary view to get file paths
            df.createOrReplaceTempView("temp_df_for_path")
            file_paths = [row.path for row in spark.sql("SELECT input_file_name() as path FROM temp_df_for_path LIMIT 1").collect()]

            if file_paths:
                file_path = file_paths[0]
                print(f"Sample file path: {file_path}")

                # Extract system_id from path using regex or string manipulation
                import re
                system_id_match = re.search(r'system_id=([^/]+)', file_path)
                if system_id_match:
                    extracted_system_id = system_id_match.group(1)
                    print(f"Extracted system_id from path: {extracted_system_id}")
                    df = df.withColumn(SYSTEM_ID, lit(extracted_system_id))
                else:
                    print("Could not extract system_id from file path")
                    # Use a default system_id based on entity config
                    default_system_id = EBER_CONFIG[entity]["system_ids"][0] if entity in EBER_CONFIG else "unknown"
                    print(f"Using default system_id: {default_system_id}")
                    df = df.withColumn(SYSTEM_ID, lit(default_system_id))
            else:
                print("Could not get file paths for system_id extraction")
                # Use a default system_id
                default_system_id = EBER_CONFIG[entity]["system_ids"][0] if entity in EBER_CONFIG else "unknown"
                print(f"Using default system_id: {default_system_id}")
                df = df.withColumn(SYSTEM_ID, lit(default_system_id))

        except Exception as e:
            print(f"Error extracting system_id from path: {str(e)}")
            # Fallback: use the first system_id from config
            default_system_id = EBER_CONFIG[entity]["system_ids"][0] if entity in EBER_CONFIG else "unknown"
            print(f"Using fallback system_id: {default_system_id}")
            df = df.withColumn(SYSTEM_ID, lit(default_system_id))
    else:
        print(f"Found {SYSTEM_ID} column in data")
    
    # Convert timestamp fields if they are stored as bigint or have timestamp_ntz issues
    # Handle common timestamp fields
    timestamp_fields = ["created_at", "updated_at"]

    # Add entity-specific timestamp fields from config
    if entity in EBER_CONFIG:
        if "created_at_field" in EBER_CONFIG[entity]:
            timestamp_fields.append(EBER_CONFIG[entity]["created_at_field"])
        if "updated_at_field" in EBER_CONFIG[entity]:
            timestamp_fields.append(EBER_CONFIG[entity]["updated_at_field"])

    # Deduplicate the list
    timestamp_fields = list(set(timestamp_fields))

    # Define updated_at earlier
    updated_at = EBER_CONFIG[entity]["updated_at_field"]

    # Convert all timestamp fields to ensure consistent data types
    print("Starting timestamp field conversion...")
    for field in timestamp_fields:
        if field in df.columns:
            try:
                field_type = df.schema[field].dataType.typeName()
                print(f"Field {field} has type {field_type}")

                if field_type in ["long", "integer", "bigint"]:
                    print(f"Converting {field} from {field_type} to timestamp using from_unixtime")
                    # Handle Unix timestamps (seconds) and cast to timestamp
                    df = df.withColumn(field, from_unixtime(col(field)).cast("timestamp"))
                elif field_type in ["timestamp", "timestamp_ntz"]:
                    # Normalize all timestamp types to standard timestamp
                    print(f"Normalizing {field} from {field_type} to standard timestamp")
                    df = df.withColumn(field, col(field).cast("timestamp"))
                elif field_type == "string":
                    # Try to parse string timestamps
                    print(f"Parsing {field} from string to timestamp")
                    df = df.withColumn(field, col(field).cast("timestamp"))
                else:
                    print(f"Field {field} has unexpected type {field_type}, attempting cast to timestamp")
                    df = df.withColumn(field, col(field).cast("timestamp"))

                # Verify the conversion worked
                new_field_type = df.schema[field].dataType.typeName()
                print(f"Field {field} converted to type {new_field_type}")

            except Exception as e:
                print(f"Warning: Error processing timestamp field {field}: {str(e)}")
                # Try a more aggressive conversion as fallback
                try:
                    print(f"Attempting fallback conversion for {field}")
                    df = df.withColumn(field, col(field).cast("string").cast("timestamp"))
                    print(f"Fallback conversion successful for {field}")
                except Exception as e2:
                    print(f"Fallback conversion also failed for {field}: {str(e2)}")
                    # Continue with other fields without failing the entire process
    
    # Check if nv_updated_date partition column exists for filtering
    if SRC_PARTITION_COLUMN in df.columns:
        print(f"Filtering by partition column {SRC_PARTITION_COLUMN} = {date_str}")
        df = (
            df.where(col(SRC_PARTITION_COLUMN) == date_str)
            .drop(SRC_PARTITION_COLUMN)
        )
    else:
        print(f"Partition column {SRC_PARTITION_COLUMN} not found, filtering by {updated_at} field")
        # Fallback: Filter using updated_at date part, assuming date_str is 'yyyy-MM-dd'
        df = (
            df.where(date_format(col(updated_at), "yyyy-MM-dd") == date_str)
        )

    # Add nv_created_month column after filtering
    print(f"Adding {NV_CREATED_MONTH} column using {CREATED_AT} field")
    print(f"Current {CREATED_AT} field type: {df.schema[CREATED_AT].dataType.typeName()}")

    # Ensure created_at is properly converted to timestamp before using date_format
    if df.schema[CREATED_AT].dataType.typeName() != "timestamp":
        print(f"Converting {CREATED_AT} to timestamp before date formatting")
        df = df.withColumn(CREATED_AT, col(CREATED_AT).cast("timestamp"))

    df = df.withColumn(NV_CREATED_MONTH, date_format(col(CREATED_AT), month_fmt))

    # Debug: Show sample of nv_created_month values
    print("Sample nv_created_month values:")
    df.select(CREATED_AT, NV_CREATED_MONTH).show(5, truncate=False)

    # Check row count after filtering
    rows_after_filtering = df.count()
    print(f"Rows after date filtering: {rows_after_filtering}")

    if rows_after_filtering == 0:
        print(f"No data found for date {date_str}. Returning empty dataframe.")
        return spark.createDataFrame([], df.schema)
    # get latest update by id
    # updated_at is already defined above
    df = (
        df.withColumn("row_number", row_number().over(Window.partitionBy(SYSTEM_ID, ID).orderBy(desc(updated_at))))
        .filter("row_number = 1")
        .drop("row_number")
    )
    if pii_fields:
        df = pii.mask(spark, df, pii_fields)
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of Eber entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(SYSTEM_ID, NV_CREATED_MONTH).save(path)


def merge(spark, entity, obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param spark:   Spark session
    :param entity:  Eber entity
    :param obj_df:  Entity dataframe
    :param path:    Delta table path
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [
        (row[SYSTEM_ID], row[NV_CREATED_MONTH])
        for row in obj_df.select(SYSTEM_ID, NV_CREATED_MONTH).distinct().collect()
    ]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    updated_at = EBER_CONFIG[entity]["updated_at_field"]
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"(delta.{SYSTEM_ID}, delta.{NV_CREATED_MONTH}) IN ({partitions_str}) "
            f"AND delta.{SYSTEM_ID} = cdc.{SYSTEM_ID} AND delta.{ID} = cdc.{ID}",
        )
        .whenMatchedUpdateAll(condition=f"cdc.{updated_at} >= delta.{updated_at}")
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_delta(spark, env, gcs_bucket, entity, date_str, pii_fields):
    try:
        print(f"Starting merge_delta for entity: {entity}, date: {date_str}")
        obj_path = get_obj_path(env, entity)
        print(f"Reading data from path: {obj_path}")
        
        try:
            obj_df = get_object(spark, entity, obj_path, date_str, pii_fields)
            
            # Check if dataframe is empty or has no rows
            row_count = obj_df.count()
            print(f"Found {row_count} records to process")
            
            if row_count == 0:
                print(f"No records to update for entity {entity} on date {date_str}")
                return
                
            delta_path = get_delta_path(gcs_bucket, entity)
            print(f"Delta table path: {delta_path}")
            
            # Check if delta table exists
            is_delta = DeltaTable.isDeltaTable(spark, delta_path)
            print(f"Is existing delta table: {is_delta}")
            
            if is_delta:
                print(f"Merging {row_count} records into existing delta table")
                merge(spark, entity, obj_df, delta_path)
            else:
                print(f"Creating new delta table with {row_count} records")
                create(obj_df, delta_path)
                
            print(f"Successfully completed merge_delta for entity: {entity}")
            
        except Exception as e:
            print(f"Error processing entity {entity}: {str(e)}")
            raise
            
    except Exception as e:
        print(f"Failed to execute merge_delta: {str(e)}")
        raise


if __name__ == "__main__":
    try:
        # Validate command line arguments
        if len(sys.argv) < 6:
            print("Error: Insufficient arguments")
            print("Usage: <script> <gcs_bucket> <env> <entity> <date_str> <pii_str>")
            sys.exit(1)
            
        gcs_bucket, env, entity, date_str, pii_str = sys.argv[1:]
        print(f"Starting job with parameters: gcs_bucket={gcs_bucket}, env={env}, entity={entity}, date={date_str}")
        
        # Parse PII fields from string representation
        try:
            pii_fields = [
                f.strip() for f in pii_str[(pii_str.index("[") + 1) : (pii_str.index("]"))].replace("'", "").split(",")
            ]
            print(f"PII fields to mask: {pii_fields}")
        except (ValueError, IndexError) as e:
            print(f"Error parsing PII fields from '{pii_str}': {str(e)}")
            print("Using empty PII fields list instead")
            pii_fields = []

        # Initialize Spark session with additional configurations for better error handling
        print("Initializing Spark session...")
        spark = SparkSession.builder\
            .config("spark.sql.debug.maxToStringFields", 100)\
            .config("spark.sql.execution.arrow.pyspark.enabled", "false")\
            .getOrCreate()
            
        # Execute the main processing function
        print("Starting data processing...")
        merge_delta(spark, env, gcs_bucket, entity, date_str, pii_fields)
        print("Data processing completed successfully")
        
    except Exception as e:
        print(f"ERROR: Job failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
        
    finally:
        # Always stop the Spark session
        if 'spark' in locals():
            print("Stopping Spark session...")
            spark.stop()
            print("Spark session stopped")
        
    print("Job completed successfully")
    sys.exit(0)