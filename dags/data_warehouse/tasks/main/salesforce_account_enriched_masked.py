import sys

from pyspark.sql import SparkSession

from common.spark import util
from data_warehouse.tasks.main import base
from metadata import data_warehouse, delta_tables, versioned_parquet_tables_masked
from metadata.constants import SystemID

airflow_config = base.AirflowConfig(
    py_file=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED + ".py",
    task_name=data_warehouse.SalesforceDAG.Task.SALESFORCE_ACCOUNT_ENRICHED_MASKED,
    system_ids=(SystemID.GL,),
    depends_on=(
        data_warehouse.SalesforceDAG.Task.SALESFORCE_USER_ENRICHED_MASKED,
        data_warehouse.SalesforceDAG.Task.SALESFORCE_LEAD_ENRICHED_MASKED,
        data_warehouse.SalesforceDAG.Task.SALESFORCE_RECORD_TYPE_ENRICHED_MASKED,
    ),
    hive_metastore_config=(
        base.HiveMetastoreTaskConfig(hive_schema="salesforce"),
        base.HiveMetastoreTaskConfig(hive_schema="id_views"),
        base.HiveMetastoreTaskConfig(hive_schema="my_views"),
        base.HiveMetastoreTaskConfig(hive_schema="ph_views"),
        base.HiveMetastoreTaskConfig(hive_schema="sg_views"),
        base.HiveMetastoreTaskConfig(hive_schema="th_views"),
        base.HiveMetastoreTaskConfig(hive_schema="vn_views"),
    ),
)


def get_task_config(env, measurement_datetime):
    # TODO: Update input_env to reflect task env when we have proper dev/qa env for data
    input_env = "prod"
    is_masked = True
    # input_env = env

    input_config = base.InputConfig(
        versioned_parquet_tables=(
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_USER_ENRICHED,
                view_name="salesforce_user_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_LEAD_ENRICHED,
                view_name="salesforce_lead_enriched",
            ),
            base.InputTable(
                path=versioned_parquet_tables_masked.DataWarehouse(input_env).SALESFORCE_RECORD_TYPE_ENRICHED,
                view_name="salesforce_record_type_enriched",
            ),
        ),
        delta_tables=(
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).ACCOUNT, view_name="salesforce_account"),
            base.InputTable(
                path=delta_tables.SalesCloud(input_env, is_masked).ACCOUNT_HISTORY, view_name="salesforce_account_history"
            ),
            base.InputTable(path=delta_tables.SalesCloud(input_env, is_masked).MARKETING_C, view_name="salesforce_marketing"),
        ),
        version_datetime=measurement_datetime,
    )
    transform_config = base.TransformConfig(
        transforms=(
            base.TransformView(
                view_name="latest_lead",
                jinja_template="""
                select
                    account_id
                    , max_by(lead_gen_channel, conversion_date) as gen_channel
                    , max_by(lead_source, conversion_date) as source
                    , max_by(lead_source_details, conversion_date) as source_details
                from salesforce_lead_enriched
                where
                    account_id is not null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="latest_marketing",
                jinja_template="""
                select
                    account_name_c
                    , max_by(id, created_date) as id
                    , max_by(name, created_date) as name
                    , max_by(marketing_c, created_date) as marketing_c
                from salesforce_marketing
                where
                    account_name_c is not null
                group by 1
                """,
            ),
            base.TransformView(
                view_name="final_view",
                jinja_template="""
                with
                    account as (

                        select
                            *

                            -- Nullifies leading zeroes as they are not legitimate ids. Leading zeroes also get stripped
                            -- when the column is casted as int, causing duplicate rows when joining.
                            , cast(if(substring(global_id_c, 0, 1) = 0, null, global_id_c) as bigint) as shipper_id
                            , cast(if(substring(nd_shipper_id_c, 0, 1) = 0, null, nd_shipper_id_c) as bigint) as nd_shipper_id

                        from salesforce_account

                    ),

                    downrank as (

                        select
                            account_id
                            , downrank_date
                        from (
                            select
                                account_id
                                , max(date(created_date)) as downrank_date
                                , count(*) as count
                            from salesforce_account_history
                         -- Specify which fields are supposed to be changed during downranking
                            where
                                 (field = 'RecordType' and old_value = sha2('Indonesia',256)  
                                    and new_value = sha2('Self Serve',256))
                                or (field = 'Sales_Channel__c' and new_value = sha2('Self Serve',256))
                            group by 1
                            )
                        -- Indicate that there must be 2 mandatory fields that were changed
                        where count = 2

                    ),

                    downrank_adhoc as (
                    -- Take into account downranked shippers whose accounts created by dash instead of sf
                        select
                            history.account_id
                            , downrank_date
                        from (
                            select
                                account_id
                                , date(created_date) as downrank_date
                            from salesforce_account_history
                            where
                                field = 'created'
                                -- Account Creator ID for New Accounts = System Automation
                                and created_by_id = '0052u000000M2oIAAS'
                            order by account_id
                        )   history
                        left join salesforce_account account on
                            history.account_id = account.id
                        left join salesforce_user_enriched on
                            account.owner_id = salesforce_user_enriched.id
                        left join salesforce_record_type_enriched as record_type on
                            account.record_type_id = record_type.id
                        -- Ensure if the new accounts are newly created,
                        -- they still fulfilled the SOP criteria of downranked shippers
                        where
                            account.original_sales_channel_c = 'Field Sales'
                            and account.sales_channel_c ='Self Serve'
                            and salesforce_user_enriched.name = 'System Automation'
                            and record_type.name = 'Self Serve'
                    ),

                    final as (

                        select
                            account.id
                            , account.name
                            , account.parent_id as parent_acc_id
                            , if(account.parent_id = 'None', account.id, account.parent_id) as parent_acc_id_coalesce
                            , parent_account.name as parent_acc_name_coalesce
                            , regexp_extract(account.ultimate_parent_account_c , '(?<=\"\/)[a-zA-Z0-9]+(?<!\")', 0)
                             as ultimate_parent_account_id
                            , account.billing_street
                            , account.billing_city
                            , account.billing_state
                            , account.billing_postal_code
                            , account.shipping_street
                            , account.shipping_city
                            , account.shipping_state
                            , account.shipping_postal_code
                            , account.shipping_country
                            , account.industry_c as industry
                            , account.business_type_c as business_type
                            , account.business_model_c as business_model
                            , latest_marketing.id as marketing_id
                            , latest_marketing.name as marketing_name 
                            , latest_marketing.marketing_c as marketing
                            , account.payment_type_c as payment_type
                            , account.shipper_tier_c as shipper_tier
                            , cast(
                                account.potential_gmv_month_account_c as double
                            ) as potential_gmv_month_account
                            , account.currency_iso_code
                            , cast(account.expected_vol_mth_c as int) as expected_monthly_vol
                            , account.group_tier_c as group_tier
                            , account.customer_loyalty_c as customer_loyalty
                            , account.created_by_id as creation_id
                            , account.shipper_id
                            , account.nd_shipper_id
                            , parent_account.shipper_id as parent_acc_shipper_id_coalesce
                            , account.account_source
                            , account.owner_id as user_id
                            , salesforce_user_enriched.name as salesperson
                            , salesforce_user_enriched.alias as salesperson_alias
                            , salesforce_user_enriched.manager as salesperson_manager
                            , account.ssm_agent_c as ssm_agent
                            , user_ssm_agent.subteam as ssm_subteam
                            , account.e_kyc_stage_c as e_kyc_stage
                            , account.customer_success_manager_c as customer_success_manager
                            , account.original_sales_channel_c as original_sales_channel
                            , account.shipper_profile_c as shipper_profile
                            , account.social_media_id_c as social_media_id
                            , account.activation_blocker_c as activation_blocker
                            , account.detailed_activation_blocker_c as detailed_activation_blocker
                            , account.down_trader_lapsed_inactive_reason_c as down_trader_lapsed_inactive_reason
                            , account.shipper_category_c as shipper_category

                            , if(
                                account.sales_channel_c <> 'None'
                                , account.sales_channel_c
                                , salesforce_user_enriched.sales_channel
                            ) as sales_channel

                            , salesforce_user_enriched.sales_territory
                            , salesforce_user_enriched.sales_team
                            , record_type.name as record_type
                            , date(account.last_activity_date) as last_activity_date
                            , date(account.last_sales_activity_c) as last_sales_activity_date
                            , date(account.latest_closed_won_opportunity_date_c) as latest_closed_won_opportunity_date
                            , coalesce(downrank.downrank_date,downrank_adhoc.downrank_date) as downrank_date
                            , coalesce(
                                latest_lead.gen_channel, account.lead_gen_channel_c
                            ) as latest_lead_gen_channel
                            , account.lead_gen_channel_nd_c as lead_gen_channel_nd
                            , coalesce(
                                latest_lead.source, account.lead_source_c
                            ) as latest_lead_source
                            , account.lead_source_nd_c as lead_source_nd
                            , coalesce(
                                latest_lead.source_details, account.lead_source_details_c
                            ) as latest_lead_source_details
                            , if(
                                account.enabler_platform_c <> 'None'
                                , upper(account.enabler_platform_c)
                                , account.enabler_platform_c
                            ) as enabler_platform
                            , case
                                when record_type.name in ('Ninja Direct', 'Self Serve') then
                                    case
                                        {%- for iso_code, hour in iso_code_to_timezone.items() %}
                                        when account.currency_iso_code = '{{ iso_code }}' then
                                        account.created_date + interval '{{ hour }}' hour
                                        {%- endfor %}
                                    end
                                else from_utc_timestamp(account.created_date, {{ get_local_timezone }})
                                end as creation_datetime
                            , case
                                when record_type.name in ('Ninja Direct', 'Self Serve') then
                                    case
                                        {%- for iso_code, hour in iso_code_to_timezone.items() %}
                                        when account.currency_iso_code = '{{ iso_code }}' then
                                        account.last_modified_date + interval '{{ hour }}' hour
                                        {%- endfor %}
                                    end
                                else from_utc_timestamp(account.last_modified_date, {{ get_local_timezone }}) 
                                end as last_modified_datetime
                            , account.incubation_scenario_c as incubation_scenario
                            , account.report_frequency_c as report_frequency
                            , account.report_type_c as report_type
                            , account.reports_requirements_c as reports_requirements
                            , account.referrer_mobile_number_c as referrer_mobile_number
                            , cast(account.referrer_global_id_c as int) as referrer_global_id
                            , account.shipper_origin_c as shipper_origin
                            , account.nv_product_line_c as nv_product_line
                            , account.shipping_type_c as shipping_type
                            , case
                                when record_type.system_id is null
                                  and record_type.name in ('Ninja Direct', 'Self Serve') then
                                    case

                                    {%- for iso_code, system_id in iso_code_to_system_id.items() %}
                                    when account.currency_iso_code = '{{ iso_code }}' then '{{ system_id }}'
                                    {%- endfor %}

                                end
                                else record_type.system_id
                            end as system_id

                            , date_format(
                                case
                                when record_type.name in ('Ninja Direct', 'Self Serve') then
                                    case
                                        {%- for iso_code, hour in iso_code_to_timezone.items() %}
                                        when account.currency_iso_code = '{{ iso_code }}' then
                                        account.created_date + interval '{{ hour }}' hour
                                        {%- endfor %}
                                    end

                                else from_utc_timestamp(account.created_date, {{ get_local_timezone }})
                                end, 'yyyy-MM'
                            ) as created_month
                        from account
                        left join salesforce_user_enriched on
                            account.owner_id = salesforce_user_enriched.id
                        left join salesforce_user_enriched as user_ssm_agent on
                            account.ssm_agent_c = user_ssm_agent.id
                        left join salesforce_record_type_enriched as record_type on
                            account.record_type_id = record_type.id
                        left join downrank on
                            account.id = downrank.account_id
                        left join downrank_adhoc on
                            account.id = downrank_adhoc.account_id
                        left join account as parent_account on
                            if(account.parent_id = 'None', account.id, account.parent_id) = parent_account.id
                        left join latest_lead on
                            account.id = latest_lead.account_id
                        left join latest_marketing on
                            account.id = latest_marketing.account_name_c
                        where
                            account.is_deleted = false

                    )

                select
                    *
                    , system_id as country
                from final
                """,
                jinja_arguments={
                    "get_local_timezone": util.get_local_timezone("record_type.system_id"),
                    "iso_code_to_system_id": {
                        "IDR": SystemID.ID,
                        "MYR": SystemID.MY,
                        "PHP": SystemID.PH,
                        "SGD": SystemID.SG,
                        "THB": SystemID.TH,
                        "VND": SystemID.VN,
                    },
                    "iso_code_to_timezone": {"IDR": "7", "MYR": "8", "PHP": "8", "SGD": "8", "THB": "7", "VND": "7"},
                },
            ),
        ),
        nullified_values=("None",),
    )
    output_config = base.OutputConfig(
        base_path=versioned_parquet_tables_masked.DataWarehouse(env).SALESFORCE_ACCOUNT_ENRICHED,
        measurement_datetime=measurement_datetime,
        partition_by=("system_id", "created_month"),
    )
    return base.TaskConfig(input=input_config, transform=transform_config, output=output_config)


def run(spark, config):
    return base.run(spark, config)


if __name__ == "__main__":
    input_args = base.process_input_args(sys.argv[1])
    task_config = get_task_config(input_args.env, input_args.measurement_datetime)

    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")
    run(spark, task_config)
    spark.stop()
