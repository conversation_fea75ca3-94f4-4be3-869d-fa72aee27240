import sys

from delta import DeltaTable
from pyspark.sql import SparkSession, Window
from pyspark.sql.functions import date_format, desc, row_number

from metadata.maintainx import MAINTAINX_CONFIG

SRC_PARTITION_COLUMN = "nv_updated_date"
NV_CREATED_MONTH = "nv_created_month"

ID = "id"
CREATED_AT = "created_at"


def _get_delta_path(gcs_bucket, entity):
    """
    Returns path at which a MaintainX entity delta table is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      MaintainX entity (e.g. assets, meters)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}/maintainx/delta/{entity}"


def _get_obj_path(gcs_bucket, entity):
    """
    Returns path at which incremental change data of a MaintainX entity is stored.

    :param gcs_bucket:  GCS bucket
    :param entity:      MaintainX entity (e.g. assets, meters)
    :return:            GCS path
    """
    return f"gs://{gcs_bucket}-raw/maintainx/objects/{entity}"


def _get_object(spark, path, date_str):
    """
    Fetches incremental change data of a MaintainX entity for a specified updated date.

    :param spark:       Spark session
    :param path:        GCS path at which entity change data is stored
    :param date_str:    Updated date filter for entity change data
    :return:            Spark dataframe of entity change data
    """
    df = spark.read.option("mergeSchema", "true").parquet(path)
    df = (
        df.where(df[SRC_PARTITION_COLUMN] == date_str)
        .drop(SRC_PARTITION_COLUMN)
        .withColumn(NV_CREATED_MONTH, date_format(CREATED_AT, "yyyy-MM"))
    )
    # get latest update by id
    updated_at = MAINTAINX_CONFIG[entity]["updated_at_field"]
    df = (
        df.withColumn("row_number", row_number().over(Window.partitionBy(ID).orderBy(desc(updated_at))))
        .filter("row_number = 1")
        .drop("row_number")
    )
    return df


def create(obj_df, path):
    """
    Creates a Delta table with the input Spark dataframe at the specified path.

    :param obj_df:  Spark dataframe of a MaintainX entity data
    :param path:    Delta table path
    """
    obj_df.write.mode("overwrite").format("delta").partitionBy(NV_CREATED_MONTH).save(path)


def merge(spark, obj_df, path):
    """
    Merges entity data into an existing Delta table.

    :param spark:   Spark session
    :param obj_df:  Entity dataframe
    :param path:    Delta table path
    """
    delta_table = DeltaTable.forPath(spark, path)
    partitions = [row[NV_CREATED_MONTH] for row in obj_df.select(NV_CREATED_MONTH).distinct().collect()]
    partitions_str = ",".join(repr(p) for p in partitions)
    print(f"partitions_str: {partitions_str}")
    (
        delta_table.alias("delta")
        .merge(
            obj_df.alias("cdc"),
            f"delta.{NV_CREATED_MONTH} IN ({partitions_str}) AND delta.{ID} = cdc.{ID}",
        )
        .whenMatchedUpdateAll()
        .whenNotMatchedInsertAll()
        .execute()
    )


def merge_delta(spark, gcs_bucket, entity, date_str):
    obj_path = _get_obj_path(gcs_bucket, entity)
    obj_df = _get_object(spark, obj_path, date_str)
    if obj_df.count() == 0:
        print("No records to update")
        return

    delta_path = _get_delta_path(gcs_bucket, entity)
    if DeltaTable.isDeltaTable(spark, delta_path):
        merge(spark, obj_df, delta_path)
    else:
        create(obj_df, delta_path)


if __name__ == "__main__":
    gcs_bucket, entity, date_str = sys.argv[1:]

    spark = SparkSession.builder.getOrCreate()
    merge_delta(spark, gcs_bucket, entity, date_str)
    spark.stop()
