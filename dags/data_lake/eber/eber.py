from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator

from eber.operators.eber_to_gcs_operator import EberToGCSOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.constants import Timeout
from metadata.eber import EBER_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
pii_tables = Variable.get("pii_fields_eber", deserialize_json=True)

default_args = {
    "owner": "airflow",
    "start_date": "2020-11-02",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

spark_conf = {
    "spark.executor.instances": "4",
    "spark.executor.memory": "15g",
    "spark.kubernetes.executor.request.cores": "2100m",
    "spark.executor.memoryOverhead": "3g",
    "spark.executor.cores": "4",
    "spark.driver.memoryOverhead": "2g",
    "spark.driver.memory": "8g",
    "spark.sql.shuffle.partitions": "200",
    "spark.executor.heartbeatInterval": "30s",
    "spark.network.timeout": "300s",
    "spark.databricks.delta.schema.autoMerge.enabled": "true",
    "spark.sql.parquet.mergeSchema": "true",
    "spark.sql.caseSensitive": "true",
    "spark.sql.legacy.timeParserPolicy": "LEGACY",
    "spark.sql.parquet.enableVectorizedReader": "false",
    "spark.sql.parquet.int96RebaseModeInRead": "LEGACY"
}

with DAG(
    dag_id="datalake_eber",
    default_args=default_args,
    schedule_interval="0 22 * * *",  # Daily 06:00am SGT
    concurrency=3,
    max_active_runs=1,
    catchup=False,
    user_defined_filters={"extract": lambda var, t, default: var.get(t, default)},
    tags=["data_lake"],
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in EBER_CONFIG.items():
        task_id = f"merge_delta_{entity}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[
                gcs_bucket,
                env,
                entity,
                "{{ds}}",
                f"""{{{{ var.json.pii_fields_eber | extract('{entity}', {None}) }}}}""",
            ],
            conn_id="spark_default",
            conf=spark_conf,
            execution_timeout=Timeout.ONE_HOUR,
        )
        for system_id in config["system_ids"]:
            entity_config = {"system_id": system_id, "entity": entity, **config}
            entity_config.pop("system_ids")
            load_eber_objects = EberToGCSOperator(
                task_id=f"load_eber_objects_{entity}_{system_id}",
                entity_config=entity_config,
                gcs_bucket=gcs_bucket + "-raw",
                gcs_folder_path=f"eber/objects/{entity}",
                eber_conn_id=f"eber_{system_id}",
                gcs_conn_id="google_cloud_default",
                execution_timeout=Timeout.ONE_HOUR,
            )
            load_eber_objects >> [merge_delta]
