import json
from datetime import timedelta
from pathlib import Path

from airflow import DAG
from airflow.models import Variable
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from airflow.providers.http.operators.http import SimpleHttpOperator

from common.airflow import notifications as notif
from common.stringcase import kebab_case
from data_warehouse.utils import airflow
from metadata.constants import MASKED_DATA_WAREHOUSE_BASE_URI
from metadata.gsuite import GSUITE_CONFIG
from plugins.gsuite.operators.gsuite_to_gcs_operator import GsuiteToGCSOperator

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
aaa_client_secret = Variable.get("aaa_client_secret")

default_args = {
    "owner": "airflow",
    "start_date": "2023-04-15",
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}


with DAG(
    dag_id="datalake_gsuite",
    default_args=default_args,
    schedule_interval="0 18 * * *",  # Daily 2am SGT
    concurrency=3,
    catchup=False,
    max_active_runs=1,
    tags=["data_lake"],
) as dag:
    aaa_auth_token_task_id = "get_aaa_auth_token"
    get_aaa_auth_token_task = SimpleHttpOperator(
        task_id=aaa_auth_token_task_id,
        method="POST",
        http_conn_id="ninjavan-api",
        endpoint="global/aaa/login?grant_type=CLIENT_CREDENTIALS",
        data=json.dumps({"clientId": "AIRFLOW", "clientSecret": aaa_client_secret}),
        response_filter=lambda response: response.json()["accessToken"],
        response_check=lambda response: response.status_code == 200,
        do_xcom_push=True,
    )

    gcs_bucket = f"nv-data-{env}-data-lake"
    for entity, config in GSUITE_CONFIG.items():

        wait_for_dwh_table_task = airflow.get_dwh_external_task_sensor(
            config["dwh_dag_id"],
            config["dwh_task_name"],
        )

        entity_config = {"entity": entity, **config}
        load_objects = GsuiteToGCSOperator(
            task_id=f"load_gsuite_objects_{entity}",
            entity_config=entity_config,
            gcs_bucket=gcs_bucket + "-raw",
            gcs_folder_path=f"gsuite/objects/{entity}",
            dwh_input_path=f"{MASKED_DATA_WAREHOUSE_BASE_URI.format(env)}/{config['dwh_task_name']}",
            aaa_auth_token_task_id=aaa_auth_token_task_id,
            gsuite_conn_id="gsuite_default",
            gcs_conn_id="google_cloud_default",
        )

        task_id = f"merge_delta_{entity}"
        merge_delta = SparkSubmitOperator(
            task_id=task_id,
            name=kebab_case(task_id),
            application=f"{tasks_path}/merge_delta.py",
            application_args=[gcs_bucket, env, entity, "{{ds}}"],
            conn_id="spark_default",
            conf={
                "spark.executor.instances": "1",
                "spark.executor.memory": "5g",
                "spark.driver.memory": "5g",
                "spark.sql.shuffle.partitions": "1",
                "spark.databricks.delta.schema.autoMerge.enabled": "true",
            },
        )
        [get_aaa_auth_token_task, wait_for_dwh_table_task] >> load_objects >> merge_delta
