import contextlib
import json
import os
from dataclasses import dataclass, field
from datetime import timedelta
from typing import Optional, Tuple

import jinja2
import pendulum
from delta import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import rand
from pyspark.sql.types import StructType

from common import date
from data_warehouse.utils import gcs, logger, versioned_parquet
from data_warehouse.utils.etl_task_logger import EtlTaskLogger
from metadata.constants import Timeout

logger = logger.get_logger(__file__)
etl_task_logger = EtlTaskLogger()


@dataclass(frozen=True)
class DependsOnExternal:
    dag_id: str
    task_id: str
    execution_delta: Optional[timedelta] = None


@dataclass(frozen=True)
class HiveMetastoreTaskConfig:
    hive_schema: str
    partition_columns: Optional[tuple] = ("system_id", "created_month")


@dataclass(frozen=True)
class AirflowConfig:
    py_file: str
    task_name: str
    system_ids: tuple
    depends_on: tuple = ()
    depends_on_external: Tuple[DependsOnExternal, ...] = ()
    execution_timeout: timedelta = Timeout.SIX_HOURS
    hive_metastore_config: Tuple[HiveMetastoreTaskConfig, ...] = ()
    post_execution_check: bool = False

    def __post_init__(self):
        task_name = self.task_name
        etl_task_logger.task_name = task_name


@dataclass(frozen=True)
class CreatedMonthRange:
    """Configs for minimum and maximum created_month values in the format 'yyyy-MM'."""

    min: str
    max: str
    field: str = "created_month"


@dataclass(frozen=True)
class LookBackRange:
    input: Optional[CreatedMonthRange]
    output: Optional[CreatedMonthRange]


@dataclass(frozen=True)
class InputTable:
    path: str
    view_name: str
    schema: StructType = None
    input_range: Optional[CreatedMonthRange] = None
    system_id: str = None
    version_datetime: str = None


@dataclass(frozen=True)
class InputConfig:
    parquet_tables: Tuple[InputTable, ...] = ()
    versioned_parquet_tables: Tuple[InputTable, ...] = ()
    delta_tables: Tuple[InputTable, ...] = ()
    csv_tables: Tuple[InputTable, ...] = ()
    version_datetime: Optional[pendulum.datetime] = None


@dataclass(frozen=True)
class TransformView:
    view_name: str
    jinja_template: str
    jinja_arguments: dict = field(default_factory=dict)


@dataclass(frozen=True)
class TransformConfig:
    transforms: Tuple[TransformView, ...]
    output_range: Optional[CreatedMonthRange] = None
    nullified_values: tuple = ()


@dataclass(frozen=True)
class OutputConfig:
    base_path: str
    measurement_datetime: Optional[pendulum.datetime] = None
    system_id: Optional[str] = None
    partition_by: tuple = ()
    enable_compaction: bool = True
    enable_csv: bool = False
    output_range: Optional[CreatedMonthRange] = None
    write_mode: str = "overwrite"
    primary_keys: list = None
    update_latest_with_historical: Optional[bool] = None
    use_native_overwrite: bool = False
    output_format: str = "parquet"


@dataclass(frozen=True)
class TaskConfig:
    input: InputConfig
    transform: TransformConfig
    output: OutputConfig


@dataclass(frozen=True)
class InputArg:
    env: str
    last_measurement_datetime: pendulum.datetime
    measurement_datetime: pendulum.datetime
    enable_full_run: bool
    system_id: str


def process_input_args(input_args_json: str) -> InputArg:
    """Parses input_args JSON from main DAG."""
    input_args_raw = json.loads(input_args_json)
    return InputArg(
        env=input_args_raw["env"],
        last_measurement_datetime=date.to_datetime(input_args_raw["last_measurement_datetime_str"]),
        measurement_datetime=date.to_datetime(input_args_raw["measurement_datetime_str"]),
        enable_full_run=input_args_raw["enable_full_run_str"] == "True",
        system_id=input_args_raw["system_id"],
    )


def get_look_back_ranges(last_measurement_datetime, measurement_datetime, additional_months, buffer_months):
    """
    Calculates input_range and output_range based on the last_measurement_datetime and measurement_datetime timestamps,
    additional_months and buffer_months.
    - input_range is the min and max created_months filters used to read input tables (where applicable)
    - output_range is the min and max created_months generated by each task execution
    :param last_measurement_datetime:    Used to determine the min created_month for both input_range and output_range.
    :type last_measurement_datetime:     pendulum.datetime
    :param measurement_datetime:         Used to determine the max created_month for both input_range and output_range.
    :type measurement_datetime:          pendulum.datetime
    :param additional_months:            Months to subtract from measurement_datetime to determine the min created_month
                                         for input range and output_range. This value cannot be less than 0.
    :type additional_months:             int
    :param buffer_months:                Months to subtract from measurement_datetime to determine the min created_month
                                         for input_range ONLY. This value cannot be less than 0.
    :type buffer_months:                 int
    """
    if additional_months < 0:
        raise ValueError("additional_months must be >= 0.")
    if buffer_months < 0:
        raise ValueError("buffer_months must be >= 0.")

    start_refresh = last_measurement_datetime.subtract(months=additional_months)
    start_buffer = start_refresh.subtract(months=buffer_months)

    month_format = "%Y-%m"
    input_range = CreatedMonthRange(min=f"{start_buffer:{month_format}}", max=f"{measurement_datetime:{month_format}}")
    output_range = CreatedMonthRange(
        min=f"{start_refresh:{month_format}}", max=f"{measurement_datetime:{month_format}}"
    )
    return LookBackRange(input=input_range, output=output_range)


def _get_delta_timestamp_as_of(spark, path, version_datetime=None):
    """
    Gets the latest available version timestamp of a Delta table as of the version_datetime.
    Defaults to latest available version timestamp if no version_datetime is specified.
    """
    df = DeltaTable.forPath(spark, path).history()
    if version_datetime:
        df = df.filter(f"timestamp <= '{version_datetime}'")
        if df.rdd.isEmpty():
            raise ValueError(f"No table versions before {version_datetime}.")
    df = df.agg({"timestamp": "max"})
    return df.collect()[0][0]


def _load_delta(spark, path, view_name, system_id=None, version_datetime=None, created_month_range=None):
    """
    Returns a Delta table as a Spark DF. Also creates a temporary view for the table, with view_name as its alias.
    - Time travels to latest available version as of version_datetime if specified
    - system_id and created_month_range can be specified to filter the table based on system_id and created_month
      columns accordingly
    """
    logger.info(f"Loading delta table: {view_name}...")
    if version_datetime == "latest":
        version_datetime = pendulum.now().to_datetime_string()
    timestamp_as_of = _get_delta_timestamp_as_of(spark, path, version_datetime)
    timestamp_as_of_str = timestamp_as_of.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    logger.info(f"Version: {timestamp_as_of_str}")

    df = spark.read.format("delta").option("timestampAsOf", timestamp_as_of_str).load(path)

    if system_id:
        df = df.filter(df["system_id"] == system_id)
    if created_month_range:
        df = df.filter(df[created_month_range.field].between(created_month_range.min, created_month_range.max))

    df.createOrReplaceTempView(view_name)
    return df


def _load_versioned_parquet(spark, path, view_name, system_id=None, version_datetime=None, created_month_range=None):
    """
    Returns a versioned parquet table as a Spark DF. Also creates a temporary view for the table, with view_name as its
    alias.
    - Time travels to latest available version as of version_datetime if specified
    - system_id and created_month_range can be specified to filter the table based on system_id and created_month
      columns accordingly
    """
    logger.info(f"Loading versioned parquet table: {view_name}...")
    df = versioned_parquet.read(spark, path, version_datetime)
    if system_id:
        df = df.filter(df["system_id"] == system_id)
    if created_month_range:
        df = df.filter(df["created_month"].between(created_month_range.min, created_month_range.max))
    df.createOrReplaceTempView(view_name)
    return df


def _load_parquet(spark, path, view_name, system_id=None):
    """
    Returns a parquet table as a Spark DF. Also creates a temporary view for the table, with view_name as its alias.
    - system_id can be specified to filter the table based on system_id
    """
    logger.info(f"Loading parquet table: {view_name}...")
    df = spark.read.option("mergeSchema", "true").parquet(path)
    if system_id:
        df = df.filter(df["system_id"] == system_id)
    df.createOrReplaceTempView(view_name)
    return df


def _load_csv(spark, path, view_name, schema, system_id=None, created_month_range=None):
    """
    Returns a csv table as a Spark DF. Also creates a temporary view for the table, with view_name as its alias.
    - system_id can be specified to filter the table based on system_id
    """
    logger.info(f"Loading csv table: {view_name}...")
    df = spark.read.schema(schema).options(header=True, lineSep="\r", sep="#", multiLine=True).csv(path)
    if system_id:
        df = df.filter(df["system_id"] == system_id)
    if created_month_range:
        df = df.filter(df[created_month_range.field].between(created_month_range.min, created_month_range.max))
    df.createOrReplaceTempView(view_name)
    return df


def load_data(spark, input_config):
    """
    Loads tables in input_config.
    - Version of table read will be latest available as of the version_datetime specified
    - A filter on created_month and system_id will be applied if input_range and system_id are specified respectively

    Note that version_datetime and input_range are not applicable to parquet tables.
    """
    for x in input_config.parquet_tables:
        _load_parquet(spark, x.path, x.view_name, x.system_id)
    for x in input_config.versioned_parquet_tables:
        if x.version_datetime:
            version_datetime = x.version_datetime
            if x.version_datetime == "latest":
                version_datetime = "latest"
        else:
            version_datetime = input_config.version_datetime
        _load_versioned_parquet(spark, x.path, x.view_name, x.system_id, version_datetime, x.input_range)
    for x in input_config.delta_tables:
        if x.version_datetime:
            version_datetime = x.version_datetime
            if x.version_datetime == "latest":
                version_datetime = "latest"
        else:
            version_datetime = input_config.version_datetime
        _load_delta(spark, x.path, x.view_name, x.system_id, version_datetime, x.input_range)
    for x in input_config.csv_tables:
        _load_csv(spark, x.path, x.view_name, x.schema, x.system_id, x.input_range)


def transform_data(spark, transform_config):
    """
    Returns a fully transformed Spark DF.

    The data is first transformed based on the Spark SQL transformation logic given. Temporary views are created for
    each transformation step. Some additional transformations are then applied:
    - Empty strings and values specified in `nullified_values` are replaced with null
    - A filter on the created_month column is applied if output_range is specified.

    Note that the final dataframe from the Spark SQL transformations must have the following immutable columns:
    - system_id, created_month
    """
    df = None
    transforms = transform_config.transforms
    for t in transforms:
        view_name = t.view_name
        logger.info(f"Creating HIVE View: {view_name}...")
        args = t.jinja_arguments
        sql = jinja2.Template(t.jinja_template).render(args)
        df = spark.sql(sql)
        df.createOrReplaceTempView(view_name)

    nullified_values = {"", *transform_config.nullified_values}
    for value in nullified_values:
        df = df.replace(value, None)

    output_range = transform_config.output_range
    if output_range:
        df = df.filter(df["created_month"].between(output_range.min, output_range.max))
    return df


def _join_path(base_path, measurement_datetime=None, system_id=None, separator="/"):
    partitions = []
    if measurement_datetime:
        partitions.append(f"measurement_datetime={measurement_datetime}")
    if system_id:
        partitions.append(f"system_id={system_id}")
    return separator.join([base_path, *partitions])


def _generate_output_path(base_path, measurement_datetime=None, system_id=None):
    path = _join_path(base_path, measurement_datetime, system_id)
    temp_path = _join_path(f"{base_path}_temp", measurement_datetime, system_id, "_")
    latest_path = _join_path(base_path, "latest", system_id)
    return path, temp_path, latest_path


def _get_partition_paths(partition_combination_df):
    """
    Constructs a list of partition paths based on partition_combination_df, e.g.
    ["system_id=id/created_month=2020-01", "system_id=id/created_month=2020-02", ...]
    """
    updated_partitions = partition_combination_df.to_dict(orient="records")
    partition_paths = []
    for partition in updated_partitions:
        partition_parts = [f"{column}={value}" for column, value in partition.items()]
        partition_paths.append("/".join(partition_parts))
    return partition_paths


def _compact_data(df, ref_path, output_range, partition_by):
    """Repartitions the df to have ~100mb per file when written to disk."""
    try:
        partition_combination_df = gcs.get_partition_combination_df(ref_path, partition_by)
    except gcs.InvalidDirectoryException:
        logger.info(f"{ref_path} not found. Skipping compaction.")
        return df

    bucket, directory = gcs.get_uri_bucket_and_directory(ref_path)
    if output_range and "created_month" in partition_by:
        partition_combination_df = partition_combination_df.query(
            f"'{output_range.min}' <= created_month <= '{output_range.max}'"
        )
        if partition_combination_df.empty:
            logger.info("No data in output_range. Skipping compaction.")
            return df
    partition_paths = _get_partition_paths(partition_combination_df)

    directory_size = 0
    for partition_path in partition_paths:
        partition_ref_directory = f"{directory}/{partition_path}"
        directory_size += gcs.get_directory_size(bucket_name=bucket, directory=partition_ref_directory)
    repartition_number = gcs.calc_num_parquet_files(directory_size=directory_size, mb_per_file=100)

    return df.repartitionByRange(repartition_number, *partition_by, rand())


def _get_table_partitioning(measurement_datetime, system_id, partition_by):
    base_partitions = []
    if measurement_datetime:
        base_partitions.append("measurement_datetime")
    if system_id:
        base_partitions.append("system_id")
    return [*base_partitions, *partition_by]


def _should_update_latest(base_path, measurement_datetime, system_id, partition_by, update_latest_with_historical):
    if not measurement_datetime:
        return False
    logger.info("finding the table partitions --> start")
    table_partitioning = _get_table_partitioning(measurement_datetime, system_id, partition_by)
    try:
        partition_combination_df = gcs.get_partition_combination_df(base_path, table_partitioning)
    except gcs.InvalidDirectoryException:
        logger.info("finding the table partitions --> end")
        return True

    if system_id:
        logger.info(f"Filtering the partitions only related to  {system_id}")
        partition_combination_df = partition_combination_df.query(f"system_id == '{system_id}'")
        if partition_combination_df.empty:
            logger.info("finding the table partitions --> end")
            return True

    latest_version = versioned_parquet.get_latest_version(partition_combination_df)
    logger.info(f"Latest version is: {latest_version}.")
    logger.info(f"Measurement Datetime is: {measurement_datetime}.")
    logger.info(f"Latest version data type is: {type(latest_version)}.")
    logger.info(f"Measurement Datetime data type is: {type(measurement_datetime)}.")
    if update_latest_with_historical is None:
        # Default behavior when flag is not provided
        is_latest = measurement_datetime >= latest_version
        logger.info("finding the table partitions --> end")
        return is_latest
    elif update_latest_with_historical:
        logger.info("finding the table partitions --> end")
        return True
    else:
        is_latest = measurement_datetime >= latest_version
        logger.info("finding the table partitions --> end")
        return is_latest
    # is_latest = measurement_datetime >= latest_version
    # return is_latest


def _copy_temp_to_latest(temp_path, latest_path, partition_by):
    _, temp_directory = gcs.get_uri_bucket_and_directory(temp_path)
    bucket, latest_directory = gcs.get_uri_bucket_and_directory(latest_path)

    if not partition_by:
        try:
            gcs.delete_directory(bucket, latest_directory)
        except gcs.InvalidDirectoryException:
            pass
        gcs.copy_directory(bucket, temp_directory, latest_directory)
        return

    partition_combination_df = gcs.get_partition_combination_df(temp_path, partition_by)
    if partition_combination_df.empty:
        logger.info("Latest partition was not updated because no data was found in temp path.")
        return

    partition_paths = _get_partition_paths(partition_combination_df)
    for partition_path in partition_paths:
        partition_temp_directory = f"{temp_directory}/{partition_path}"
        partition_latest_directory = f"{latest_directory}/{partition_path}"

        try:
            gcs.delete_directory(bucket, partition_latest_directory)
        except gcs.InvalidDirectoryException:
            pass
        gcs.copy_directory(bucket, partition_temp_directory, partition_latest_directory)


def _merge_temp_to_latest(spark, temp_path, latest_path, output_config, measurement_datetime_str):
    _, temp_directory = gcs.get_uri_bucket_and_directory(temp_path)
    bucket, latest_directory = gcs.get_uri_bucket_and_directory(latest_path)
    temp_full_path = _join_path(
        f"{output_config.base_path}_temp_full", measurement_datetime_str, output_config.system_id, "_"
    )
    _, temp_full_directory = gcs.get_uri_bucket_and_directory(temp_full_path)

    partition_combination_df = gcs.get_partition_combination_df(temp_path, output_config.partition_by)

    if partition_combination_df.empty:
        logger.info("Latest partition was not updated because no data was found in temp path.")
        return

    partition_paths = _get_partition_paths(partition_combination_df)
    for partition_path in partition_paths:
        partition_temp_directory = f"{temp_directory}/{partition_path}"
        partition_latest_directory = f"{latest_directory}/{partition_path}"
        partition_temp_full_directory = f"{temp_full_directory}/{partition_path}"

        new_spark_df = spark.read.parquet(f"gs://{bucket}/{partition_temp_directory}")

        if gcs.is_path_exist(bucket, partition_latest_directory):
            latest_df = spark.read.parquet(f"gs://{bucket}/{partition_latest_directory}")
            latest_df_without_new_df = latest_df.join(new_spark_df, on=output_config.primary_keys, how="left_anti")
            full_new_df = latest_df_without_new_df.unionByName(new_spark_df, allowMissingColumns=True)
        else:
            full_new_df = new_spark_df
        full_new_df.write.mode("overwrite").parquet(f"{temp_full_path}/{partition_path}")

        try:
            gcs.delete_directory(bucket, partition_latest_directory)
        except gcs.InvalidDirectoryException:
            pass

        gcs.copy_directory(bucket, partition_temp_full_directory, partition_latest_directory)
    gcs.delete_directory(bucket, temp_full_directory)


def _move_temp_to_existing(temp_path, existing_path):
    bucket, directory = gcs.get_uri_bucket_and_directory(existing_path)
    _, temp_directory = gcs.get_uri_bucket_and_directory(temp_path)

    try:
        gcs.delete_directory(bucket, directory)
    except gcs.InvalidDirectoryException:
        pass
    gcs.move_directory(
        bucket_name=bucket,
        destination_bucket_name=bucket,
        source_directory=temp_directory,
        destination_directory=directory,
    )


def write_csv_output(df, output_config, measurement_datetime_str, should_update_latest):
    table_name = output_config.base_path.split("/")[-1]
    csv_base_path = f"{output_config.base_path.rstrip(table_name)}csvs/{table_name}"
    csv_path, temp_csv_path, latest_csv_path = _generate_output_path(
        csv_base_path, measurement_datetime_str, output_config.system_id
    )
    df.repartition(1).write.mode("overwrite").option("header", True).option("dateFormat", "yyyy-MM-dd").option(
        "timestampFormat", "yyyy-MM-dd HH:mm:ss"
    ).csv(temp_csv_path)
    if should_update_latest:
        _copy_temp_to_latest(temp_csv_path, latest_csv_path, output_config.partition_by)
    _move_temp_to_existing(temp_csv_path, csv_path)
    logger.info("csv written successfully")


def write_data(df, output_config, spark = None):
    """
    Writes the transformed DF as a parquet table in GCS. Also performs a compaction and updates the
    measurement_datetime=latest partition if applicable.
    """
    logger.info("Writing Results...")

    measurement_datetime_str = None
    if output_config.measurement_datetime:
        measurement_datetime_str = date.to_measurement_datetime_str(output_config.measurement_datetime)

    path, temp_path, latest_path = _generate_output_path(
        output_config.base_path, measurement_datetime_str, output_config.system_id
    )

    logger.info("Below are the output paths")
    logger.info(f"temp path: {temp_path}")
    logger.info(f"latest path: {latest_path}")
    logger.info(f"measurement_path: {path}")

    if output_config.enable_compaction:
        logger.info("gathering details for compaction --> start")
        compaction_ref_path = latest_path if output_config.measurement_datetime else path
        df = _compact_data(df, compaction_ref_path, output_config.output_range, output_config.partition_by)
        logger.info("gathering details for compaction --> end")

    df_write = df.write
    should_update_latest = _should_update_latest(
        output_config.base_path,
        measurement_datetime_str,
        output_config.system_id,
        output_config.partition_by,
        output_config.update_latest_with_historical,
    )

    if output_config.partition_by:
        logger.info(f"partitioning by - {output_config.partition_by} ")
        df_write = df_write.partitionBy(output_config.partition_by)
    if output_config.enable_csv:
        logger.info(f"writing csv output")
        write_csv_output(df, output_config, measurement_datetime_str, should_update_latest)
    if not output_config.use_native_overwrite and output_config.write_mode != "merge":
        logger.info(f"writing parquet output to temp path - {temp_path} ")
        df_write.mode("overwrite").parquet(temp_path)
        if should_update_latest:
            logger.info(f"latest needs to be updated {output_config.write_mode}")
            if output_config.write_mode == "merge":
                logger.info("merging temp to latest")
                _merge_temp_to_latest(spark, temp_path, latest_path, output_config, measurement_datetime_str)
            else:
                logger.info("copying temp to latest")
                _copy_temp_to_latest(temp_path, latest_path, output_config.partition_by)
        logger.info("finally moving temp to existing")
        _move_temp_to_existing(temp_path, path)
    else:
        logger.info("using spark native overwrite")
        logger.info(f"writing to measurement datetime path - {path}")
        df_write.mode("overwrite").parquet(path)
        logger.info(f"merging to latest path - {path}")
        spark.read.format(output_config.output_format).load(path).write.mode("overwrite").partitionBy(
            output_config.partition_by).format(output_config.output_format).save(latest_path)


def run(spark, config):
    etl_task_logger.task_start_datetime = pendulum.now(tz=date.Timezone.SG).strftime("%Y-%m-%dT%H:%M:%S")
    etl_task_logger.system_ids = etl_task_logger.get_system_ids(config.input)
    log_dest_path = etl_task_logger.get_dest_path(config.output)
    etl_task_logger.insert_log(spark, log_dest_path)

    logger.info(f"data loading ---> start")
    load_data(spark, config.input)
    logger.info(f"data loading ---> start")

    etl_task_logger.load_finish_datetime = pendulum.now(tz=date.Timezone.SG).strftime("%Y-%m-%dT%H:%M:%S")

    logger.info(f"executing  transformations ---> start")
    df = transform_data(spark, config.transform)
    logger.info(f"executing  transformations ---> end")

    logger.info(f"write ---> start")
    write_data(df, config.output, spark)
    logger.info(f"write ---> end")

    etl_task_logger.task_finish_datetime = pendulum.now(tz=date.Timezone.SG).strftime("%Y-%m-%dT%H:%M:%S")
    etl_task_logger.insert_log(spark, log_dest_path)

    logger.info("All done.")

    return df


@contextlib.contextmanager
def get_spark(add_dags_zip=False):
    spark = SparkSession.builder.getOrCreate()
    spark.sparkContext.setLogLevel("ERROR")

    if add_dags_zip:
        dags_zip = os.environ["AIRFLOW_HOME"] + "/files/dags.zip"
        spark.sparkContext.addPyFile(dags_zip)

    try:
        yield spark
    finally:
        spark.stop()
