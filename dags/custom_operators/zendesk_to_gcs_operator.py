import json
import logging

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.zendesk.hooks.zendesk import ZendeskHook


class ZendeskToGcsOperator(BaseOperator):
    """
    Zendesk to GCS Operator

    Fetches data from Zendesk and stores it in Google Cloud Storage (GCS). Uses the Zendesk incremental export API
    (https://developer.zendesk.com/rest_api/docs/support/incremental_export). Data will be stored in the path
    {gcs_bucket}/{gcs_folder_path}/system_id={system_id}/nv_updated_date={date}

    :param zd_entity_config:    Zendesk entity config to fetch data (e.g. tickets)
    :type zd_entity_config:     dict
    :param gcs_bucket:          GCS bucket to upload file to
    :type gcs_bucket:           string
    :param gcs_folder_path:     GCS folder path
    :type gcs_folder_path:      string
    :param zd_conn_id:          Zendesk connection ID
    :type zd_conn_id:           string
    """

    VALID_ENTITIES = {
        "brands",
        "groups",
        "organizations",
        "satisfaction_ratings",
        "ticket_fields",
        "ticket_forms",
        "ticket_metric_events",
        "tickets",
        "users",
    }
    PARTITION_COLUMN = "nv_updated_date"

    def __init__(self, zd_entity_config, gcs_bucket, gcs_folder_path, zd_conn_id="zendesk_default", **kwargs):
        super().__init__(**kwargs)
        entity = zd_entity_config["entity"]
        if entity not in self.VALID_ENTITIES:
            raise ValueError(f"Unknown zendesk entity: {entity}")
        self.zd_entity_config = zd_entity_config
        self.gcs_bucket = gcs_bucket
        self.gcs_folder_path = gcs_folder_path
        self.zd_conn_id = zd_conn_id

    def execute(self, context):
        """
        Fetches incremental data from Zendesk for a particular entity (e.g. tickets) and stores it in GCS in parquet
        format.

        :param context: Task context
        """
        entity = self.zd_entity_config["entity"]
        logging.info(f"Fetching Zendesk {entity} data")
        zendesk_hook = ZendeskHook(self.zd_conn_id)
        zendesk_path_fmt = self.zd_entity_config["path"]
        zendesk_path = zendesk_path_fmt.format(entity=entity)
        fetch_all = self.zd_entity_config["fetch_type"] == "all"
        from_dt = context["execution_date"]
        query_params = {}
        if not fetch_all:
            query_params = {"start_time": from_dt.int_timestamp}
        response = zendesk_hook.call(zendesk_path, query=query_params)
        logging.info("Fetched data")

        if not response.get(entity):
            logging.info("No records retrieved")
            return

        entity_schema = self.zd_entity_config.get("schema", {})
        df = pd.DataFrame(response[entity])
        df_columns = df.columns
        df_schema = {column: entity_schema[column] for column in df_columns}
        df = df.astype(df_schema)
        nested_columns = self.zd_entity_config.get("nested_columns", set()).intersection(df_columns)
        for column in nested_columns:
            df[column] = df[column].map(json.dumps)
            # json.dumps does not handle null values. Manually convert them to None.
            df[column] = df[column].mask(((df[column] == "NaN") | (df[column] == "null")), None)
        updated_at = self.zd_entity_config["updated_at_field"]
        df[self.PARTITION_COLUMN] = df[updated_at].dt.date
        # filter by updated_at values for endpoints that fetch all data
        # convert exec_date from pendulum DateTime to pandas datetime
        from_dt_dt = pd.to_datetime(from_dt.to_datetime_string(), infer_datetime_format=True)
        filtered_df = df[df[self.PARTITION_COLUMN] >= from_dt_dt]
        # drop columns with no data as pyarrow will mess up the data type for null columns.
        filtered_df = filtered_df.dropna(axis=1, how="all")
        logging.info(f"No. of records: {len(filtered_df.index)}")
        if filtered_df.empty:
            return

        system_id = self.zd_entity_config["system_id"]
        dirname = f"gs://{self.gcs_bucket}/{self.gcs_folder_path}/system_id={system_id}"

        logging.info("Uploading to GCS bucket")
        filtered_df.to_parquet(
            dirname, compression="snappy", engine="pyarrow", index=False, partition_cols=[self.PARTITION_COLUMN]
        )
        logging.info("Uploaded Zendesk data to GCS")