import json, logging
from datetime import datetime, timedelta
from pathlib import Path
from airflow import DAG
from airflow.models import Variable
from airflow.hooks.base import BaseHook
from airflow.providers.apache.spark.operators.spark_submit import SparkSubmitOperator
from amast.operators.amast_to_gcs_operator import AmastToGCSOperator
from common.airflow import notifications as notif
from common.stringcase import kebab_case
from metadata.constants import Timeout
from metadata.amast import AMAST_CONFIG

tasks_path = "local://" + str(Path(__file__).resolve().parent / "tasks")
env = Variable.get("env")
amast_conn=BaseHook.get_connection("amast_my")
logging.info(f"Amast_host: {amast_conn.host}, Amast_password: {amast_conn.password}")

default_args = {
    "owner": "airflow",
    "start_date": datetime(2023, 10, 16),
    "retries": 1,
    "retry_delay": timedelta(minutes=2),
    "on_failure_callback": notif.chat.send_ti_failure_alert if env == "prod" else None,
}

spark_conf = {
    "spark.executor.instances": "10",
    "spark.executor.memory": "10g",
    "spark.executor.cores": "4",
    "spark.driver.memory": "5g",
    "spark.sql.shuffle.partitions": "10",
    "spark.sql.adaptive.enabled":"true",
    "spark.databricks.delta.schema.autoMerge.enabled":"true"
}

with DAG(
    dag_id="datalake_amast",
    default_args=default_args,
    schedule_interval="0 22 * * *",  # Daily 10:00 PM UTC
    concurrency=2,
    catchup=False,
    max_active_runs=1,
    tags=["data_lake"],
) as dag:
    gcs_bucket = f"nv-data-{env}-data-lake"

    for system_id, entities in AMAST_CONFIG.items():
        for entity, config in entities.items():
            task_id_base = f"{entity}_{system_id}"
            
            if config.get('rate', False):
                # Use SparkSubmitOperator for rate-limited entities
                load_task = SparkSubmitOperator(
                    task_id=f"load_amast_objects_{task_id_base}",
                    name=kebab_case(f"load_amast_{task_id_base}"),
                    application=f"{tasks_path}/amast_task.py",
                    application_args=[
                        env,
                        entity,
                        system_id,
                        "{{ds}}",
                        json.dumps(config),
                        amast_conn.password
                    ],
                    conn_id="spark_default",
                    conf=spark_conf,
                    execution_timeout=Timeout.ONE_HOUR
                )
            else:
                load_task = AmastToGCSOperator(
                    task_id=f"load_amast_objects_{task_id_base}",
                    entity=entity,
                    gcs_bucket=gcs_bucket + "-raw",
                    system_id=system_id,
                    execution_timeout=Timeout.ONE_HOUR,
                )

            merge_task = SparkSubmitOperator(
                task_id=f"merge_delta_{task_id_base}",
                name=kebab_case(f"merge_delta_{task_id_base}"),
                application=f"{tasks_path}/merge_delta.py",
                application_args=[
                    env,
                    entity,
                    system_id,
                    "{{ds}}"
                ],
                conn_id="spark_default",
                conf=spark_conf,
                execution_timeout=Timeout.ONE_HOUR,
            )

            load_task >> merge_task
