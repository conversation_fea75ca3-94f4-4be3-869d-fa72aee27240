import json
import logging

import pandas as pd
from airflow.models import <PERSON><PERSON>perator, SkipMixin

from eber.hooks.eber_hook import EberHook


class EberToGCSOperator(BaseOperator, SkipMixin):
    """
    Eber to GCS Operator

    Fetches data from Eber (https://api.eber.co/api/v3/home) and stores it in Google Cloud Storage (GCS). Depending on
    the entity, either incremental or full data is returned (e.g. full data for users and incremental for transactions).

    Data will be stored in the path {gcs_bucket}/{gcs_folder_path}/nv_updated_date={date}

    :param entity_config:   Eber entity config to fetch data (e.g. users, transactions)
    :type entity_config:    dict
    :param gcs_bucket:      GCS bucket to upload data file to
    :type gcs_bucket:       string
    :param gcs_folder_path: GCS folder path
    :type gcs_folder_path:  string
    :param eber_conn_id:    Eber connection ID
    :type eber_conn_id:     string
    :param gcs_conn_id:     GCS connection ID
    :type gcs_conn_id:      string
    """

    PARTITION_COLUMN = "nv_updated_date"

    def __init__(
        self,
        entity_config,
        gcs_bucket,
        gcs_folder_path,
        eber_conn_id="eber_default",
        gcs_conn_id="google_cloud_default",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity_config = entity_config
        self.gcs_bucket = gcs_bucket
        self.gcs_folder_path = gcs_folder_path
        self.eber_conn_id = eber_conn_id
        self.gcs_conn_id = gcs_conn_id

    def execute(self, context):
        """
        Fetches Eber entity data and stores it in GCS in parquet format.

        :param context: Task context
        """
        entity = self.entity_config["entity"]
        logging.info(f"Fetching Eber {entity} data")
        eber_hook = EberHook(self.eber_conn_id)
        path_fmt = self.entity_config["path"]
        fetch_all = self.entity_config["fetch_type"] == "all"
        exec_date = context["execution_date"]
        query_params = {"limit": 100}
        if not fetch_all:
            from_field, to_field = self.entity_config["incremental_range_fields"]
            query_params[from_field] = exec_date.to_date_string()
            # add 2 days as the from/to dates follow local timezone
            query_params[to_field] = exec_date.add(days=2).to_date_string()
        data = eber_hook.get_data(path_fmt, query_params=query_params)
        logging.info("Fetched data")

        if not data:
            logging.info("No records retrieved")
            return

        df = pd.DataFrame(data)
        updated_at = self.entity_config["updated_at_field"]
        # filter by updated_at values for endpoints that fetch all data (e.g. users)
        exec_date_dt = exec_date.to_datetime_string()
        if not fetch_all:
            df = df[df[updated_at] >= exec_date_dt]
        logging.info(f"No. of records: {len(df.index)}")
        if df.empty:
            return
        # drop columns with no data as pyarrow will mess up the data type for null columns.
        df = df.dropna(axis=1, how="all")

        # Columns that require flexible date parsing - may have mixed formats
        friendly_date_columns = ["friendly_updated_at", "friendly_created_at", "friendly_enrolled_at", "friendly_transaction_created_at"]
        for col_name in friendly_date_columns:
            if col_name in df.columns and df[col_name].dtype == 'object':
                logging.info(f"Attempting flexible date parsing for '{col_name}'...")
                
                # First try inferring the format
                try:
                    df[col_name] = pd.to_datetime(df[col_name], errors='coerce')
                    null_count = df[col_name].isna().sum()
                    if null_count > 0:
                        logging.warning(f"  {null_count} values in '{col_name}' could not be parsed with automatic format inference.")
                    else:
                        logging.info(f"  Successfully parsed all values in '{col_name}' with automatic format inference.")
                    
                except Exception as e:
                    logging.error(f"Error during flexible date parsing for '{col_name}': {e}")
                    # Don't raise - we'll continue and let further processing handle this if needed

        schema = self.entity_config.get("schema", {})
        columns = df.columns
        try:
            df_schema = {column: schema[column] for column in columns}
            for column in columns:
                dtype = schema[column]
                if dtype == "datetime64":
                    dtype = "datetime64[ns]"
                df_schema[column] = dtype
        except KeyError as e:
            new_column = str(e).strip("'")
            raise KeyError(
                f"Data type for '{new_column}' is undefined. "
                f"Sample values: {df[df[new_column].notnull()][new_column][:5].tolist()}"
            ) from e
        df = df.astype(df_schema)

        nested_columns = self.entity_config.get("nested_columns", set()).intersection(columns)
        for column in nested_columns:
            df[column] = df[column].map(json.dumps)
            # json.dumps does not handle null values. Manually convert them to None.
            df[column] = df[column].mask(((df[column] == "NaN") | (df[column] == "null")), None)
        if fetch_all:
            df[self.PARTITION_COLUMN] = exec_date.strftime("%Y-%m-%d")  # self.PARTITION_COLUMN is "nv_updated_date"
        else:
            logging.info("Debugging KeyError: 'updated_at_field' - State before assigning partition column:")
            logging.info(f"  Current entity_config type: {type(self.entity_config)}")
            try:
                logging.info(f"  Current entity_config keys: {list(self.entity_config.keys())}")
                logging.info(f"  Value of 'updated_at_field' in entity_config: {self.entity_config.get('updated_at_field', 'NOT FOUND')}")
            except Exception as e_log_keys:
                logging.error(f"  Error logging entity_config keys: {e_log_keys}")
            logging.info(f"  Variable 'updated_at' (should be column name): {updated_at} (type: {type(updated_at)})")
            if isinstance(updated_at, str) and updated_at in df.columns:
                logging.info(f"  DataFrame column df['{updated_at}'] exists. Type: {type(df[updated_at])}, Dtype: {df[updated_at].dtype}")
                # Check if it has .dt accessor
                if hasattr(df[updated_at], 'dt'):
                    logging.info(f"  Column df['{updated_at}'] has .dt accessor.")
                    if hasattr(df[updated_at].dt, 'date'):
                        logging.info(f"  Column df['{updated_at}'].dt has .date attribute.")
                    else:
                        logging.warning(f"  Column df['{updated_at}'].dt does NOT have .date attribute.")
                else:
                    logging.warning(f"  Column df['{updated_at}'] does NOT have .dt accessor. Cannot use .dt.date.")
            elif isinstance(updated_at, str):
                logging.warning(f"  Column name '{updated_at}' (from variable 'updated_at') NOT found in DataFrame. Columns are: {list(df.columns)}")
            else:
                logging.warning(f"  Variable 'updated_at' is not a string, cannot be used as a column name directly.")
            logging.info(f"  self.PARTITION_COLUMN value: {self.PARTITION_COLUMN}")
            logging.info("Attempting line: df[self.PARTITION_COLUMN] = df[\"updated_at\"].dt.date (using literal string)")
            df[self.PARTITION_COLUMN] = df["updated_at"].dt.date

        system_id_value = self.entity_config["system_id"]
        df["system_id"] = system_id_value  # Add 'system_id' column with its value

        # Define the base directory for Parquet output, before partition columns are appended by to_parquet
        base_gcs_path = f"gs://{self.gcs_bucket}/{self.gcs_folder_path}"

        logging.info(f"Uploading to GCS bucket {base_gcs_path} with partitions system_id, {self.PARTITION_COLUMN}")
        df.to_parquet(
            base_gcs_path,  # Use base path
            compression="snappy",
            engine="pyarrow",
            index=False,
            # Partition by 'system_id' first, then by 'nv_updated_date'
            # This will create subdirectories system_id=.../nv_updated_date=.../
            partition_cols=["system_id", self.PARTITION_COLUMN]
        )
        logging.info("Uploaded Eber data to GCS")
