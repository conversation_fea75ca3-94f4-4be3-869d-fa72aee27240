import json, math
from common.utils import helper


def generate_application_file_for_task(task_type, tasks_path, schema, table, config):
    task_configurations = {
        "post_process_cdc": {
            "main_application_file": f"{tasks_path}/post_process_cdc.py",
            "name": f"process_cdc_{schema}_{table}"
        },
        "update_delta": {
            "main_application_file": f"{tasks_path}/update_delta.py",
            "name": f"pii_delta_{schema}_{table}"
        },
        "compaction_delta":  {
            "main_application_file": f"{tasks_path}/compact_delta.py",
            "name": f"pii_compact_delta_{schema}_{table}"
        }
    }

    if task_type not in task_configurations:
        raise ValueError(f"Unknown task type: {task_type}")

    config["main_application_file"] = task_configurations[task_type]["main_application_file"]
    config["name"] = task_configurations[task_type]["name"]

    return get_application_file_str(config)


def truncate_name(name, max_length=45):
    """Truncate name to max_length characters, adding a hash suffix if truncated."""
    name = name.replace("_", "-")
    name = name.rstrip('_-')
    if len(name) <= max_length:
        return name
    return name[:max_length].rstrip('_-')


def get_application_file_str(config, app_type='data-lake'):
    print(config)
    schema_table = config["schema_table"]
    application_dict = {
        "apiVersion": "sparkoperator.k8s.io/v1beta2",
        "kind": "SparkApplication",
        "metadata": {
            "name": truncate_name(config["name"]),
            "namespace": config["namespace"]
        },
        "spec": {
            "type": "Python",
            "timeToLiveSeconds": int(config["time_to_live"]),
            "mode": "cluster",
            "pythonVersion": "3",
            "sparkVersion": config["spark_version"],
            "image": config["image"],
            "imagePullPolicy": "IfNotPresent",
            "mainApplicationFile": config["main_application_file"],
            "sparkConf": get_spark_conf(config),
            "driver": get_driver_conf(config, schema_table, driver_type=app_type),
            "executor": get_executor_conf(config, schema_table, executor_type=app_type),
            "volumes": get_volumes(config)
        }
    }
    return json.dumps(application_dict)


def get_driver_conf(config, schema_table, driver_type='data-lake'):
    spark_conf = helper.get_spark_conf_by_table(config["env"], schema_table,
                                                table_sizes_airflow=config['table_sizes_airflow'],
                                                group=config["group"])

    return {
        "affinity": {
            "nodeAffinity": {
                "requiredDuringSchedulingIgnoredDuringExecution": {
                    "nodeSelectorTerms": [
                        {
                            "matchExpressions": [
                                {
                                    "key": "group",
                                    "operator": "In",
                                    "values": [driver_type]
                                },
                                {
                                    "key": "role",
                                    "operator": "In",
                                    "values": ["driver"]
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "tolerations": [
            {
                "key": "group",
                "operator": "Equal",
                "value": driver_type,
                "effect": "NoSchedule"
            },
            {
                "key": "role",
                "operator": "Equal",
                "value": "driver",
                "effect": "NoSchedule"
            }
        ],
        "coreRequest": spark_conf.get("spark.kubernetes.executor.request.cores", "900m"),
        "coreLimit": spark_conf.get("spark.kubernetes.executor.request.cores", "900m"),
        "cores": int(spark_conf.get("spark.driver.cores", 1)),
        "memory": spark_conf.get("spark.driver.memory"),
        "labels": {
            "version": config["version"]
        },
        "serviceAccount": config["service_account"],
        "volumeMounts": [
            {"mountPath": "/keys", "name": "storage-service-account-key-volume"}
        ]
    }


def get_executor_conf(config, schema_table, executor_type='data-lake'):
    spark_conf = helper.get_spark_conf_by_table(config["env"], schema_table,
                                                table_sizes_airflow=config["table_sizes_airflow"],
                                                group=config["group"])

    return {
        "affinity": {
            "nodeAffinity": {
                "requiredDuringSchedulingIgnoredDuringExecution": {
                    "nodeSelectorTerms": [
                        {
                            "matchExpressions": [
                                {
                                    "key": "group",
                                    "operator": "In",
                                    "values": [executor_type]
                                }
                            ]
                        }
                    ]
                }
            }
        },
        "tolerations": [
            {
                "key": "group",
                "operator": "Equal",
                "value": executor_type,
                "effect": "NoSchedule"
            }
        ],
        "coreRequest": spark_conf.get("spark.kubernetes.executor.request.cores", "1800m"),
        "coreLimit": spark_conf.get("spark.kubernetes.executor.request.cores", "1800m"),
        "cores": int(spark_conf.get("spark.executor.cores", 1)),
        "instances": int(spark_conf.get("spark.executor.instances")),
        "memory": spark_conf.get("spark.executor.memory"),
        "labels": {
            "version": config["version"]
        },
        "serviceAccount": config["service_account"],
        "volumeMounts": [
            {"mountPath": "/keys", "name": "storage-service-account-key-volume"}
        ]
    }


def get_volumes(config):
    return [
        {
            "name": "storage-service-account-key-volume",
            "secret": {
                "secretName": "storage-service-account-key",
                "defaultMode": 420
            }
        }
    ]


def get_spark_conf(config):
    if config.get('task_type') in ["delta", "process", "dwh", "compaction"]:
        return config.get('spark_config')
    else:
        return {}
