import json
import logging

import pandas as pd
from airflow.models import BaseOperator
from airflow.providers.google.cloud.hooks.gcs import GCSHook
from airflow.providers.jira.hooks.jira import JiraHook


class JiraToGcsOperator(BaseOperator):
    """
    Jira to GCS Operator

    Fetches Jira data and stores it in Google Cloud Storage (GCS). Note that only issue data can be fetched at present
    with this operator.

    Data will be stored in the path gs://{gcs_bucket}/{gcs_folder_path}/nv_updated_date={date}

    :param entity_config:   Jira entity config to fetch data
    :type entity_config:    dict
    :param gcs_bucket:      GCS bucket to upload data file to
    :type gcs_bucket:       string
    :param gcs_folder_path: GCS folder path
    :type gcs_folder_path:  string
    :param jira_conn_id:    Jira connection ID
    :type jira_connd_id:    string
    :param gcs_conn_id:     GCS connection ID
    :type gcs_conn_id:      string
    """

    PARTITION_COLUMN = "nv_updated_date"

    def __init__(
        self,
        entity_config,
        gcs_bucket,
        gcs_folder_path,
        jira_conn_id="jira_default",
        gcs_conn_id="google_cloud_default",
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.entity_config = entity_config
        self.gcs_bucket = gcs_bucket
        self.gcs_folder_path = gcs_folder_path
        self.jira_conn_id = jira_conn_id
        self.gcs_conn_id = gcs_conn_id

    def execute(self, context):
        """
        Fetches Jira data and stores it in GCS in parquet format.

        :param context: Task context
        """
        # Check snapshot or incremental data fetch
        gcs_hook = GCSHook(self.gcs_conn_id)
        blobs = gcs_hook.list(bucket_name=self.gcs_bucket, prefix=self.gcs_folder_path, delimiter=self.PARTITION_COLUMN)
        snapshot = len(blobs) == 0 or context["params"].get("snapshot", False)

        jira_hook = JiraHook(jira_conn_id=self.jira_conn_id)
        jira_client = jira_hook.client
        opts = {
            "startAt": 0,  # note: camel case and 0-indexed
            "maxResults": 50,  # note: camel case
            "expand": "changelog",
            "json_result": True,
        }
        # Note that the query is in local (SG) time
        jql_str = "" if snapshot else f"updated >= '{context['ds']}'"
        logging.info(f'Fetching Jira issues with JQL: "{jql_str}"')
        data = []
        response = jira_client.search_issues(jql_str, **opts)
        total = response["total"]
        logging.info(f"No. of query results: {total}")
        data.extend(response["issues"])
        while len(data) < total:
            logging.info(f"No. of records: {len(data)}")
            next_opts = {**opts, "startAt": len(data)}
            response = jira_client.search_issues(jql_str, **next_opts)
            data.extend(response["issues"])
        logging.info(f"No. of records: {len(data)}")
        if len(data) == 0:
            return
        # Upload data as parquet file
        df = pd.DataFrame(data)
        nested_columns = self.entity_config.get("nested_columns", [])
        for column in nested_columns:
            df[column] = df[column].map(json.dumps)
            # json.dumps does not handle null values. Manually convert them to None.
            df[column] = df[column].mask(((df[column] == "NaN") | (df[column] == "null")), None)
        df[self.PARTITION_COLUMN] = context["execution_date"].date()
        dirname = f"gs://{self.gcs_bucket}/{self.gcs_folder_path}"
        df.to_parquet(
            dirname, compression="snappy", engine="pyarrow", index=False, partition_cols=[self.PARTITION_COLUMN]
        )
        logging.info("Uploaded Jira data to GCS")
